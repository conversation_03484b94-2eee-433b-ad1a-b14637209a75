<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
	id="WebApp_ID" version="2.5">
	<display-name>gops.developer</display-name>

	<filter>
		<filter-name>encodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>utf-8</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>encodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath*:egovframework/spring/context/**/context-*.xml</param-value>
	</context-param>

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>

	<servlet>
		<servlet-name>action</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath*:gops/developer/core/config/WebMvcConfig.java</param-value>
<!--			<param-value>/WEB-INF/config/egovframework/springmvc/**/egov-com-servlet.xml</param-value>-->
		</init-param>

		<load-on-startup>1</load-on-startup>
	</servlet>

	<servlet-mapping>
		<servlet-name>action</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>

	<welcome-file-list>
		<welcome-file></welcome-file>
	</welcome-file-list>

	<login-config>
		<auth-method>BASIC</auth-method>
	</login-config>

	<error-page>
		<exception-type>java.lang.Throwable</exception-type>
		<location>/error</location>
	</error-page>

	<error-page>
		<error-code>404</error-code>
		<location>/error/resourceNotFound</location>
	</error-page>

	<error-page>
		<error-code>500</error-code>
		<location>/error</location>
	</error-page>

	<jsp-config>
		<jsp-property-group>
			<url-pattern>*.jsp</url-pattern>
			<page-encoding>UTF-8</page-encoding>
			<include-prelude>/WEB-INF/jsp/common/include/taglibInc.jspf</include-prelude>
		</jsp-property-group>
	</jsp-config>
</web-app>
