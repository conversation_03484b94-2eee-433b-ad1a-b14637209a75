<th:block data-layout-decorate="~{_layout/default.html}">

	<!-- index.html 고유 CSS 추가 -->
	<th:block layout:fragment="css">
		<link rel="stylesheet" th:href="@{vendor/prism/prism.css}" />
		<link rel="stylesheet" th:href="${'js/odf'+'/odf.css'}" />
		<link rel="stylesheet" th:href="@{vendor/jqtree/dist/themes/default/style.min.css}" />
		<link rel="stylesheet" th:href="@{vendor/colorpicker/css/colorpicker.css}" />
		<link rel="stylesheet" th:href="@{css/toc.css}" />
		<link rel="stylesheet" th:href="@{css/sample.css}" />
	</th:block>

	<th:block layout:fragment="content">
		<h2 class="hidden">본문 영역</h2>

		<div class="sideMenu">
			<!-- 검색창 -->
			<div class="innerBox">
				<input id="txt-search" type="text" placeholder="| 검색" />
				<button type="button" class="btnSearchRemove"><span class="hidden">지우기</span></button>
			</div>

			<!-- 서브메뉴 -->
			<div class="listArea mScroll">
				<div class="tab tab1" id="submenu">
				</div>
			</div>
		</div>

		<div id="content" class="mScroll">
			<div class="btnGroup">
				<button type="button" class="btnTop"><span class="hidden">맨 위로</span></button>
				<button type="button" class="btnBottom"><span class="hidden">맨 아래로</span></button>
			</div>
			<div id="contents" class="innerContent">
				<section>
					<h3 id="sample-title" class="titSec"></h3>

					<div class="row">
						<div class="detailSec">
							<p id="sample-desc"></p>
						</div>
						<div class="mapArea">
							<iframe id="sample-container" class="container" style="width:100%;"></iframe>
						</div>
					</div>
				</section>
				<section>
					<h3 class="titSec">샘플 코드</h3>
					<div class="posBtn">
						<button id="showLive" type="button" class="whiteType try">직접해보기</button>
					</div>
					<div class="row">
						<div class="codeArea large">
							<div class="codeBox">
								<pre>
									<code id="sample-code" class="line-numbers language-markup">

									</code>
								</pre>
							</div>
						</div>
					</div>
				</section>
			</div>
		</div>

	</th:block>

	<th:block layout:fragment="script-bottom">
		<script th:src="@{/js/basemap-config.js}"></script>
		<script th:src="@{/vendor/clipboard/clipboard.js}"></script>
		<script th:src="@{/vendor/prism/prism.js}"></script>
		<script th:src="@{/js/widget/sample/index.js(ms=${ms})}"></script>
		<script th:src="@{/vendor/amcharts4/core.js}"></script>
		<script>
			var DeveloperUrl = location.protocol + "//" + location.host + '[[${@egovProperties.getProperty('Url.ContextPath')}]]';
			var OdfUrl = DeveloperUrl + '/js/odf';
			var OuiUrl = DeveloperUrl + '/js/oui';
			//var OuiUrl = 'http://*************:7070'
			var SmtUrl = DeveloperUrl + '/smt'
			//var APIGW = DeveloperUrl + '/proxy/api';
			var APIGW = '[[${@egovProperties.getProperty('Url.APIGW')}]]';
			/* var WfsAPI = '[[${@egovProperties.getProperty('Service.map')}]]' + '[[${@egovProperties.getProperty('Url.WfsAPI')}]]';
			var WmsAPI = '[[${@egovProperties.getProperty('Service.map')}]]' + '[[${@egovProperties.getProperty('Url.WmsAPI')}]]';
			var WmtsAPI = '[[${@egovProperties.getProperty('Service.map')}]]' + '[[${@egovProperties.getProperty('Url.WmtsAPI')}]]'; */
			var WfsAPI = '[[${@egovProperties.getProperty('Url.Geoserver')}]]';
			var WmsAPI = '[[${@egovProperties.getProperty('Url.Geoserver')}]]';
			var WmtsAPI = '[[${@egovProperties.getProperty('Url.Geoserver')}]]';
			var cssJs = DeveloperUrl + '/js/sample/buttonControl.js';

			//odf 레이어
			var pointLayer = '[[${@egovProperties.getProperty('Layer.PointLayer')}]]'
			var lineLayer = '[[${@egovProperties.getProperty('Layer.LineLayer')}]]'
			var polygonLayer1 = '[[${@egovProperties.getProperty('Layer.PolygonLayer1')}]]'
			var polygonLayer2 = '[[${@egovProperties.getProperty('Layer.PolygonLayer2')}]]'
			var polygonLayer3 = '[[${@egovProperties.getProperty('Layer.PolygonLayer3')}]]'
			var wmtsLayer = '[[${@egovProperties.getProperty('Layer.WmtsLayer')}]]'
			var hotspotLayer = '[[${@egovProperties.getProperty('Layer.HotspotLayer')}]]'

			// oui 레이어
			var testPointLayer1 = '[[${@egovProperties.getProperty('Layer.TestPointLayer1')}]]'
			var testPointLayer1Id = '[[${@egovProperties.getProperty('Layer.TestPointLayer1Id')}]]'
			var testPointLayer1Nm = '[[${@egovProperties.getProperty('Layer.TestPointLayer1Nm')}]]'
			var testLineLayer1 = '[[${@egovProperties.getProperty('Layer.TestLineLayer1')}]]'
			var testLineLayer1Id = '[[${@egovProperties.getProperty('Layer.TestLineLayer1Id')}]]'
			var testLineLayer1Nm = '[[${@egovProperties.getProperty('Layer.TestLineLayer1Nm')}]]'
			var testPolygonLayer1 = '[[${@egovProperties.getProperty('Layer.TestPolygonLayer1')}]]'
			var testPolygonLayer1Id = '[[${@egovProperties.getProperty('Layer.TestPolygonLayer1Id')}]]'
			var testPolygonLayer1Nm = '[[${@egovProperties.getProperty('Layer.TestPolygonLayer1Nm')}]]'
			var testEditPolygonLayer1 = '[[${@egovProperties.getProperty('Layer.TestEditPolygonLayer1')}]]'
			var testEditPolygonLayer1Id = '[[${@egovProperties.getProperty('Layer.TestEditPolygonLayer1Id')}]]'

			// 개발자지원센터 예제 공통 설정
			var crtfckey = '[[${@egovProperties.getProperty('Service.Crtfckey')}]]';
			var sampleBasemap = '[[${@egovProperties.getProperty('Sample.Basemap')}]]';
			var sampleSrid = '[[${@egovProperties.getProperty('Sample.widget.Srid')}]]';
			var userId = '[[${@egovProperties.getProperty('Sample.UserId')}]]';
			var userMapId = '[[${@egovProperties.getProperty('Map.UserMapId')}]]';
			var proxyUseAt = '[[${@egovProperties.getProperty('Sample.Proxy.Use')}]]';
			var customBasemapConfig = '[[${@egovProperties.getProperty('Sample.Basemap.Custom.Config')}]]';

			// 외부 API 관련 설정 (브이월드)
			var vWorldApiKey = '[[${@egovProperties.getProperty('VWorld.ApiKey')}]]';
			var vWorldDomain = '[[${@egovProperties.getProperty('VWorld.Domain')}]]';

			// API
			var smtAPI = '[[${@egovProperties.getProperty('Service.smt')}]]';
			var mapAPI = '[[${@egovProperties.getProperty('Service.map')}]]';
			var analysisAPI = '[[${@egovProperties.getProperty('Service.analysis')}]]';
			var publishAPI = '[[${@egovProperties.getProperty('Service.publish')}]]';
			var coordAPI = '[[${@egovProperties.getProperty('Service.coord')}]]';
			var addrgeoAPI = '[[${@egovProperties.getProperty('Service.addrgeo')}]]';
			// 워크플로우, 레이어그룹 API 미사용으로 주석처리
			// var workflowAPI = '[[${@egovProperties.getProperty('Service.workflow')}]]';
			// var lyrgroupAPI = '[[${@egovProperties.getProperty('Service.layerGroup')}]]';

			// 외부 API 관련 설정 (Kakao, 브이월드, 바로e맵, 국가공간정보 포털)
			var kakaoAppKey = '[[${@egovProperties.getProperty('AppKey.Kakao')}]]';
			var naverAppKey = '[[${@egovProperties.getProperty('AppKey.Naver')}]]';
			var googleAppKey = '[[${@egovProperties.getProperty('AppKey.Google')}]]';
			var baroEMapURL = '[[${@egovProperties.getProperty('Url.BaroEMapURL')}]]';
			var baroEMapKey = '[[${@egovProperties.getProperty('Url.BaroEMapKey')}]]';
			var baroEMapAirURL = '[[${@egovProperties.getProperty('Url.BaroEMapAirURL')}]]';
			var vWorldURL = '[[${@egovProperties.getProperty('Url.VWorldURL')}]]';
			var cctvApiKey = '[[${@egovProperties.getProperty('Ntic.ApiKey')}]]';
			var cctvApiURL = '[[${@egovProperties.getProperty('Ntic.Url')}]]';

			//샘플에서 사용하는 배경지도id 설정
			var basemapVars = BasemapUtils.getBasemapVariables(sampleBasemap);
			var basemapType = basemapVars.basemapType;
			var basemap_base = basemapVars.basemap_base;
			var basemap_air = basemapVars.basemap_air;
			var basemap_white = basemapVars.basemap_white;
			var basemap_color = basemapVars.basemap_color;
			var basemap_etc = basemapVars.basemap_etc;
			var input = false;
			if (sampleSrid === "5179") {
				var viewOption = "map.setCenter([948149.5499094268, 1934350.7375426753]);";
			} else {
				// sampleSrid === "5186"
				var viewOption = "map.setCenter([192396.63847319243, 534166.8213405443]);";
			}
			var zoomOption = "map.setZoom(14);";

		</script>
	</th:block>

</th:block>
