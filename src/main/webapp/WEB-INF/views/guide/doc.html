<section>
	<h3 id="guide-intro" class="titSec">개발자 지원센터 안내</h3>

	<div class="row">
	    <div class="detailSec">
	        <p><strong>개발자 지원센터</strong>는 웹환경에서 지도를 이용한 서비스 제작을 돕는 다양한 기능을 제공합니다.</p>
	    </div>
	</div>

	<div class="textArea">
		<div class="row">
		    <strong>1. API 문서</strong>
		    <p>
		    	도형분석, 지오코딩, 좌표변환 등 지도 정보를 위한 기능과 주소검색  등의 데이터를 조회할수 있는 기능을 RESTful API 형태로 제공합니다.
		    </p>
		</div>
		<div class="row">
		    <strong>2. 지도 문서</strong>
		    <p>
		    	웹환경에서 지도를 제어할수 있는 지도 컴포넌트에 관한 내용을 제공합니다.
		    </p>
		    <p style="color:blue">
	    			<a href="javascript:void(0)" onclick="downloadAPI('odf')" download>odf 파일 다운로드</a>
		    </p>
		</div>
		<div class="row">
		    <strong>3. 지도 예제</strong>
		    <p>
		    	지도 컴포넌트에서 제공하는 다양한 API들을 조합하여 지도를 제어하는 Sample을 제공합니다.
		    </p>
		    <p style="color:blue">
		    	<a href="javascript:void(0)" onclick="downloadSettingFile()" download>개발자설정파일다운로드(proxyUrl.jsp,odf-config설정 json 파일)</a>
		    </p>
		</div>
<!-- 		<div class="row">
		    <strong>4. 지도 마법사</strong>
		    <p>
		    	지도 마법사를 사용하여 지도 컴포넌트의 다양한 기능을 직접 조합하고 테스트 해볼 수 있습니다.
		    </p>
		</div> -->
		<div class="row">
		    <strong>5. 위젯 문서</strong>
		    <p>
		    	웹환경에서 지도 기능을 UI와 함께 제공하는 (위젯)컴포넌트에 관한 내용을 제공합니다.
		    </p>
		    <p style="color:blue">
	    			<a href="javascript:void(0)" onclick="downloadAPI('oui')" download>oui 파일 다운로드</a>
		    </p>
		</div>
		<div class="row">
		    <strong>6. 위젯 예제</strong>
		    <p>
		    	지도 컴포넌트에서 제공하는 다양한 기능을 API들과 조합한 UI(위젯) 생성 Sample을 제공합니다.
		    </p>
		</div>
<!-- 		<div class="row">
		    <strong>7. 위젯 마법사</strong>
		    <p>
		    	위젯 마법사를 사용하여 위젯 컴포넌트의 다양한 기능을 직접 조합하고 테스트 해볼 수 있습니다.
		    </p>
		</div> -->

<!-- 		<div class="row">
		    <h4 id="guide-ready" class="titSub">준비 사항</h4>
		    <p class="desc">개발자 지원센터의 API 를 사용하려면 다음 내용을 준비해 주세요.</p>
		    <strong>1.개발자 권한</strong>
		    <strong>2.방화벽 확인</strong>
		    <strong>3.API Key 발급</strong>
		</div> -->
		<div class="row">
		    <h4 id="guide-code-info" class="titSub">예제 코드 안내</h4>
		    <p class="desc">문서 중에는 예제 코드 영역이 있습니다. 예제 코드 영역의 우상단에 마우스를 올리면 Copy 기능을 사용할 수 있습니다.</p>
		    <div class="codeArea">
				<pre>
					<code class="line-numbers language-markup">
			// 우상단에 마우스를 올려 보세요.
			// 코드를 클립보드에 Copy 할 수 있습니다.
					</code>
				</pre>
			</div>
		</div>
	</div>

</section>


<section>
	<h3 id="guide-start" class="titSec">시작하기</h3>

	<div class="row">
	    <div class="detailSec">
	        <p>웹브라우저에 지도를 표시하기위해 간단한 코드를 작성해보겠습니다.</p>
	    </div>
	</div>

	<div class="textArea">
		<div class="row">
		    <h4 id="guide-start-1" class="titSub">CSS 링크 추가</h4>
		    <p class="desc">지도 영역에서 사용될 스타일시트를 <span class="strong">&lt;head &gt;</span> 영역에 추가합니다.</p>
		    <div class="codeArea">
		        <pre>
					<code class="line-numbers language-markup">
						&lt;head&gt;
							&lt;!-- ... --&gt;
							&lt;link href="http://지도컴포넌트URL/odf.css" rel="stylesheet"&gt;
							&lt;!-- ... --&gt;
						&lt;/head&gt;
					</code>
				</pre>
		    </div>
		</div>
		<div class="row">
		    <h4 id="guide-start-2" class="titSub">지도 영역 만들기</h4>
		    <p class="desc">지도를 담을 영역을 div 태그로 만듭니다. id 값을 <span class="strong">map</span>으로 지정합니다. class 값은 <span class="strong">odf-view</span>로 지정합니다.</p>
		    <div class="codeArea">
		        <pre>
					<code class="line-numbers language-markup">
						&lt;div id="map" class="odf-view"&gt;&lt;/div&gt;
					</code>
				</pre>
		    </div>
		</div>
		<div class="row">
		    <h4 id="guide-start-3" class="titSub">지도를 그리는 Javascript API 추가</h4>
		    <p class="desc">지도 컴포넌트 스크립트를 추가합니다. 발급받은 <span class="strong">appKey</span>를 파라미터로 넣어주셔야 합니다.</p>
		    <div class="codeArea">
		        <pre>
					<code class="line-numbers language-markup">
						&lt;script type="text/javascript" src="http://지도컴포넌트URL/odf.min.js?appKey=인증키"&gt;&lt;/script&gt;
					</code>
				</pre>
		    </div>
		</div>
		<div class="row">
		    <h4 id="guide-start-4" class="titSub">지도를 띄우는 script 코드 작성</h4>
		    <div class="codeArea">
		        <pre>
					<code class="line-numbers language-js">

	/* 1. 맵 타겟 */
	let mapContainer = document.getElementById('map');

	/* 2. 맵 중심점 */
	let coord = new odf.Coordinate(955156.7761, 1951925.0984);

	/* 3. 맵 객체 옵션 */
	let mapOption = {
		center : coord,
		zoom : 11,
		projection : 'EPSG:5179',
		maxZoom : 20,
		minZoom : 8,
		basemap : {baroEMap : ['eMapBasic','eMapAIR']},
	/*
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도
	 */

	//proxyURL: 'proxy.jsp' 프록시 설정
	};

	/* 4. 맵 객체 생성 */
	let map = new odf.Map(mapContainer, mapOption);

					</code>
				</pre>
		    </div>
		    <strong>1. 맵 타겟 : 지도 영역을 그려줄 컨테이너를 지정합니다.</strong>
		    <strong>2. 맵 중심점 : 지도를 그려주기 위해서는 중심점 선언이 반드시 필요합니다.</strong>
		    <strong>3. 맵 객체 옵션 : 지도를 그려주기 위한 각종 옵션을 설정합니다.</strong>
		    <strong>4. 맵 객체 생성 : 지도를 그려주고 맵 객체를 생성합니다.</strong>
		</div>
		<div class="row">
		    <h4 id="guide-start-5" class="titSub">지도 Control 기능 추가</h4>
		    <p class="desc">지도에는 지도 컴포넌트에서 제공하는 다양한 Control들을 추가할 수 있습니다. <a th:href="@{/mapdocs}" class="link">지도 문서</a>에서 확인해 보세요.</p>
		    <div class="codeArea">
		        <pre>
					<code class="line-numbers language-js">
/* 베이스맵 컨트롤 생성 */
let basemapControl = new odf.BasemapControl();
basemapControl.setMap(map);
					</code>
				</pre>
		    </div>
		    <p class="desc">전체 코드와 지도는 <a th:href="@{/sample}" class="link">지도 예제</a>에서 확인할 수 있습니다.</p>
		</div>
	</div>

</section>


<section>
	<h3 id="guide-api" class="titSec">API 사용</h3>

	<div class="row">
	    <div class="detailSec">
	        <p>
	        	개발자 지원센터에서는 지도와 함께 사용될 데이터를 다룰수 있도록 다양한 API 라이브러리를 제공합니다.<br>
				상세 Spec은 <a th:href="@{/apidocs}" class="link">API 문서</a>에서 확인 가능합니다.
	        </p>
	    </div>
	</div>

	<div class="textArea">
		<div class="row">
		</div>

		<div class="row">
		    <h4 id="guide-api-1" class="titSub">RESTful API 사용</h4>
		    <p class="desc">
		    	개발자 지원센터의 API는 RESTful 형태로 제공됩니다.<br>
				RESTful API 규격을 지킨다면 Java, javascript를 비롯하여 어떤 언어를 사용해도 무관합니다.
		    </p>
		</div>
		<div class="row">
		    <h4 id="guide-api-2" class="titSub">API Url</h4>
		    <p class="desc">
		    	API를 호출하려면 다음과 같은 형태로 주소를 조합해야 합니다.
		    </p>
		    <div class="tableArea">
			    <table>
					<tbody>
						<tr>
							<th>URL Pattern</th>
							<th>예시</th>
						</tr>
						<tr>
							<td>http://API.서버.기본주소/<span class="strong">API 주소</span></td>
							<td>
								<em class="page-link">
									<a href="#">http://API.서버.기본주소/bag/api/layers</a>
								</em>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<!-- <p class="desc">
				API 서버 기본주소는 <span class="strong">[[${@egovProperties.getProperty('Url.APIGW')}]]</span> 입니다.
			</p> -->
		</div>
		<div class="row">
		    <h4 id="guide-api-3" class="titSub">HTTP METHOD</h4>
		    <p class="desc">
		    	API Url이 동일한 경우 HTTP METHOD에 따라 용도가 다릅니다.<br>
				일반적으로 POST, GET, PUT, DELETE Method를 사용하여 CRUD를 할 수 있습니다.<br>
				정확한 용도는 각 API 상세를 참고해 주세요.
		    </p>
		    <div class="tableArea">
			    <table>
					<tbody>
						<tr>
							<th>METHOD</th>
							<th>용도</th>
						</tr>
						<tr>
							<td>POST</td>
							<td>
								새 리소스를 생성합니다. 또는 API로 전달할 데이터가 큰 경우에도 사용 가능합니다.
							</td>
						</tr>
						<tr>
							<td>GET</td>
							<td>
								해당 리소스를 조회합니다.
							</td>
						</tr>
						<tr>
							<td>PUT</td>
							<td>
								해당 리소스를 수정합니다.
							</td>
						</tr>
						<tr>
							<td>DELETE</td>
							<td>
								해당 리소스를 삭제합니다.
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</section>

<section>
	<h3 id="guide-wizard" class="titSec">마법사 사용</h3>

	<div class="row">
	    <div class="detailSec">
	        <p>
	        	<a th:href="@{/wizard}" class="link">마법사</a>를 사용하여 지도 컴포넌트의 다양한 기능을 직접 조합하고 테스트 해볼 수 있습니다.<br>
				초기 프로젝트 구성시 마법사를 사용하여 코드를 생성하고 이를 진입점으로 사용할 수 있습니다.
	        </p>
	    </div>
	</div>

	<div class="textArea">
		<div class="row">
		    <h4 id="guide-wizard-1" class="titSub">마법사 사용 예시</h4>
		    <p class="desc">1. 추가할 Control을 선택합니다.<br>
				<img th:src="@{/images/guide/wizard_1.jpg(ms=${ms})}" border="0" />
			</p>
			<p class="desc">2. 지도에 선택한 Contol이 반영됩니다.<br>
				<img th:src="@{/images/guide/wizard_2.jpg(ms=${ms})}" border="0" />
			</p>
			<p class="desc">3. 예제 코드에 Control 추가 코드가 반영됩니다.<br>
				<img th:src="@{/images/guide/wizard_3.jpg(ms=${ms})}" border="0" />
			</p>
			<p class="desc">4. 직접 해보기 기능으로 코드를 직접 수정해서 테스트 가능합니다.<br>

			</p>
	    </div>
	</div>
</section>
