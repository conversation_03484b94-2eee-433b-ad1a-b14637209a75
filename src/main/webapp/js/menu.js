/**
 *
 */

var menu = {

		nowMenuName: "" ,

		makeMenuJsonFunc: undefined,
		pageLoadFunc: undefined,

		init: function(defaultMenuName, makeMenuJsonFunc, pageLoadFunc, scrollSet=false) {

			// 초기화
			var menuName = menu.getFirstMenuName(defaultMenuName);
			menu.makeMenuJsonFunc = makeMenuJsonFunc;
			menu.pageLoadFunc = pageLoadFunc;

			menu.makeMenuJsonFunc.call(this);
			menu.loadMenu(menuName);
			menu.pageLoadFunc.call(this, menuName);
			menu.eventSearch();

			//메뉴 스크롤 위치 이동
			if(scrollSet){
				var activeA = document.querySelector('#submenu li>a.active');
				var activeLi = activeA.parentElement;
				var menuList = activeLi.parentElement;
				var listGroup = menuList.parentElement;

				document.querySelector('#mCSB_1_container').style.top = `-${listGroup.offsetTop + menuList.offsetTop + activeLi.offsetTop + activeA.offsetTop - activeA.scrollHeight}px`;
			}
		},

		goPage : function(path, subMenuName, target) {
			// 다른 페이지 이동
			if(location.pathname.includes(path) == false) {
				target = target&&target!==''?"#"+target:"";
				location.href = rootpath + path + "?sub=" + subMenuName+target;

				// target이 있는 경우 해당 위치로 스크롤
				menu.scrollToId(target, true);
			}
			// 동일 페이지에서 로딩
			else if(menu.pageLoadFunc != undefined) {
				menu.pageLoadFunc.call(this, subMenuName, target);
				// target이 있는 경우 해당 위치로 스크롤
				setTimeout(function(){menu.scrollToId(target, false)}, 500);
				//menu.scrollToId(target, false)
			}

		},

		loadMenu : function(subMenuName) {
			var html = "";
			var searchKeyword = $("#txt-search").val().toLowerCase();
			menu.nowMenuName = subMenuName;

			$.each(menuJson, function(index, grp) {

				var filteredMenu = menu.menuFilter(index, searchKeyword);
				var pathname = location.pathname.replace(rootpath, "");

				// 그룹 메뉴 시작 태그
				if(searchKeyword == "" || grp.name.toLowerCase().includes(searchKeyword) || filteredMenu.length > 0) {

					if(grp.path == undefined && grp.target == undefined) {
						html += '<div class="listGroup">'
								+ '<strong class="titList">' + grp.name + '</strong>'
								+ '<ul class="menuList">';
					} else {
						var link = "javascript:menu.goPage('" + pathname + "', '" + grp.name + "', '" + grp.target + "');";
						html += '<div class="listGroup">'
							+ '<strong class="titList"><a href="' + link + '">' + grp.name + '</strong>'
							+ '<ul class="menuList">';
					}
				}

				// 서브 메뉴
				$.each(filteredMenu, function(index, menu) {

					var tooltip = util.isNullOrEmpty(menu.desc) ? '' : '<div class="desc"><p>' + menu.desc + '</p></div>';

					var link = "javascript:menu.goPage(\"" + pathname + "\", \"" + menu.name + "\", \"" + menu.target + "\");";
					if(subMenuName == menu.name){
						html += "<li class='active'>"
							+ "<a href='" + link + "' class='active'>" + menu.name + "</a>"
							+ tooltip
							+ "</li>";
					}
					else{
						html += "<li>"
							+ "<a href='" + link + "'>" + menu.name + "</a>"
							+ tooltip
							+ "</li>";
					}
				});

				// 그룹 메뉴 종료 태그
				if(searchKeyword == "" || grp.name.toLowerCase().includes(searchKeyword) || filteredMenu.length > 0) {
					html += "</ul>"
							+ "</div>";
				}
			});

			$("#submenu").html(html);
			menu.eventTooltip();
		},

		menuFilter: function(grpId, searchKeyword) {

			var filteredMenu = menuJson[grpId].submenu.filter(function(it) {
				return it.grpId == grpId &&
						((it.name&&it.name.toLowerCase().includes(searchKeyword)) ||
							(it.desc &&it.desc.toLowerCase().includes(searchKeyword)));
			});

			return filteredMenu;
		},

		eventTooltip: function() {
			//
			$('.listArea:not(.type2) .listGroup li a').off("mouseover");
			$('.listArea:not(.type2) .listGroup li a').on({
                "mouseover":function(){
                    if($(this).siblings('.desc').offset().top < 300 ){
                        $(this).siblings('.desc').addClass('lower').removeClass('upper')
                    }else{
                        $(this).siblings('.desc').removeClass('lower').addClass('upper')
                    }
                }
            });
		},

		eventSearch: function() {
			// 메뉴 검색
			$("#txt-search").off("keyup");
			$("#txt-search").keyup(function(e){
				menu.loadMenu(menu.nowMenuName);

				if ($('#txt-search').val() == '') {
					$('.innerBox .btnSearchRemove').hide();
				} else {
					$('.innerBox .btnSearchRemove').show();
				}
			});

			$('.btnSearchRemove').off("click");
			$('.btnSearchRemove').click(function() {
				$('#txt-search').val('');
				$('.innerBox .btnSearchRemove').hide();
				menu.loadMenu(menu.nowMenuName);
			});
		},

		scrollToId: function(tagId, isTop){

			if(isTop) {
				$('#content .mCSB_container, #content .mCSB_dragger').animate({top:0}),500;
			}
			if(tagId != null && tagId != '') {
				var id = "#" + util.jq(tagId);
				$("#content").mCustomScrollbar("scrollTo", id);
			}
		},

		menuSelected: function(menuName) {
			menu.nowMenuName = menuName;
			$("#submenu a").removeClass("active");
			$("#submenu a").filter(function() {
				return $(this).text() == menuName;
			}).addClass("active");
		},

		// 메뉴명으로 메뉴 Object 찾기
		getMenu: function(menuName) {

			var keys = Object.keys(menuJson);

			for (var i = 0; i < keys.length; i++) {

				var gKey = keys[i];
				if(menuJson[gKey].name == menuName) {
					return menuJson[gKey];
				} else {

					var menu = menuJson[gKey].submenu.filter(function(it) {
						return it.name.toLowerCase() == menuName.toLowerCase();
					});

					if(menu.length > 0) {
						return menu[0];
					}
				}
			}

			return menuJson[0].submenu[0];
		},

		// 메뉴명으로 그룹 메뉴Object 찾기
		getGroupMenu: function(menuName) {

			var keys = Object.keys(menuJson);

			for (var i = 0; i < keys.length; i++) {

				var gKey = keys[i];
				if(menuJson[gKey].name == menuName) {
					return menuJson[gKey];
				} else {

					var menu = menuJson[gKey].submenu.filter(function(it) {
						return it.name.toLowerCase() == menuName.toLowerCase();
					});

					if(menu.length > 0) {
						return menuJson[gKey];
					}
				}
			}

			return menuJson[0];
		},

		// 페이지에서 처음으로 로딩할 메뉴명
		getFirstMenuName: function(defaultName) {
			var subMenuName = util.getParam("sub");
			return subMenuName == undefined || subMenuName == "" ? defaultName : subMenuName;
		}
}
