[{"name": "AreaNameControl", "path": "행정구역 표시 및 위치이동", "target": "", "grpId": 0, "desc": "행정구역 설정 클래스"}, {"name": "BasemapControl", "path": "지도생성/배경지도설정", "target": "", "grpId": 0, "desc": "배경지도 설정 클래스"}, {"name": "BasemapControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "배경지도 설정 클래스"}, {"name": "BasemapControl", "path": "사용자 지정 배경지도", "target": "", "grpId": 0, "desc": "배경지도 설정 클래스"}, {"name": "BasemapControl", "path": "레이어 다각형으로 자르기", "target": "", "grpId": 0, "desc": "배경지도 설정 클래스"}, {"name": "BookmarkControl", "path": "북마크 생성/홈 이동", "target": "", "grpId": 0, "desc": "북마크 컨트롤 생성 클래스"}, {"name": "ClearControl", "path": "그리기/측정도구", "target": "", "grpId": 0, "desc": "지도 그리기 이벤트 초기화 클래스"}, {"name": "ClearControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "지도 그리기 이벤트 초기화 클래스"}, {"name": "ClearControl", "path": "그리기/측정/초기화 컨트롤", "target": "", "grpId": 0, "desc": "지도 그리기 이벤트 초기화 클래스"}, {"name": "ColorFactory", "path": "색 추출기", "target": "", "grpId": 0, "desc": "색 생성 클래스"}, {"name": "Control", "path": "컨트롤 순서 변경", "target": "", "grpId": 0, "desc": "사용자 정의 컨트롤 클래스"}, {"name": "Control", "path": "사용자 정의 단일 컨트롤", "target": "", "grpId": 0, "desc": "사용자 정의 컨트롤 클래스"}, {"name": "Control", "path": "사용자 정의 서브 그룹 컨트롤", "target": "", "grpId": 0, "desc": "사용자 정의 컨트롤 클래스"}, {"name": "Control", "path": "분할지도-사용자정의 컨트롤 추가", "target": "", "grpId": 0, "desc": "사용자 정의 컨트롤 클래스"}, {"name": "Coordinate", "path": "지도생성/배경지도설정", "target": "", "grpId": 0, "desc": "좌표 설정 클래스"}, {"name": "Coordinate", "path": "X, Y 좌표변환", "target": "", "grpId": 0, "desc": "좌표 설정 클래스"}, {"name": "DivideMapControl", "path": "분할지도", "target": "", "grpId": 0, "desc": "지도 분할 설정 클래스"}, {"name": "DivideMapControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "지도 분할 설정 클래스"}, {"name": "DivideMapControl", "path": "사용자 정의 2분할 지도", "target": "", "grpId": 0, "desc": "지도 분할 설정 클래스"}, {"name": "DivideMapControl", "path": "사용자 정의 4분할 지도", "target": "", "grpId": 0, "desc": "지도 분할 설정 클래스"}, {"name": "DownloadControl", "path": "지도 출력/저장", "target": "", "grpId": 0, "desc": "다운로드 설정 클래스"}, {"name": "DownloadControl", "path": "저장/출력 컨트롤", "target": "", "grpId": 0, "desc": "다운로드 설정 클래스"}, {"name": "DownloadControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "다운로드 설정 클래스"}, {"name": "DrawControl", "path": "그리기/측정도구", "target": "", "grpId": 0, "desc": "그리기 도구 클래스"}, {"name": "DrawControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "그리기 도구 클래스"}, {"name": "DrawControl", "path": "그리기/측정/초기화 컨트롤", "target": "", "grpId": 0, "desc": "그리기 도구 클래스"}, {"name": "event", "path": "이벤트 추가/해제", "target": "", "grpId": 0, "desc": "이벤트 생성 클래스"}, {"name": "event", "path": "이벤트 등록 확인", "target": "", "grpId": 0, "desc": "이벤트 생성 클래스"}, {"name": "event", "path": "이벤트 강제 실행", "target": "", "grpId": 0, "desc": "이벤트 생성 클래스"}, {"name": "event", "path": "지도 이벤트", "target": "", "grpId": 0, "desc": "이벤트 생성 클래스"}, {"name": "event", "path": "마커 이벤트", "target": "", "grpId": 0, "desc": "이벤트 생성 클래스"}, {"name": "event", "path": "뷰 이벤트", "target": "", "grpId": 0, "desc": "이벤트 생성 클래스"}, {"name": "event", "path": "레이어 이벤트", "target": "", "grpId": 0, "desc": "이벤트 생성 클래스"}, {"name": "Extent", "path": "Extent 좌표변환", "target": "", "grpId": 0, "desc": "영역 관련 클래스"}, {"name": "Feature", "path": "Feature와GeoJson상호변환", "target": "", "grpId": 0, "desc": "Feature 클래스"}, {"name": "Feature", "path": "동적 막대 차트 스타일", "target": "", "grpId": 0, "desc": "Feature 클래스"}, {"name": "Feature", "path": "동적 파이 차트 스타일", "target": "", "grpId": 0, "desc": "Feature 클래스"}, {"name": "Feature", "path": "Feature 별 면적/길이 측정", "target": "", "grpId": 0, "desc": "Feature 클래스"}, {"name": "Feature", "path": "공간정보 좌표변환", "target": "", "grpId": 0, "desc": "Feature 클래스"}, {"name": "Feature", "path": "Feature 정보 조회", "target": "", "grpId": 0, "desc": "Feature 클래스"}, {"name": "Feature", "path": "Circle -> Polygon으로 변환", "target": "", "grpId": 0, "desc": "Feature 클래스"}, {"name": "Feature", "path": "레이어 편집", "target": "", "grpId": 0, "desc": "Feature 클래스"}, {"name": "FeatureFactory", "path": "Circle -> Polygon으로 변환", "target": "", "grpId": 0, "desc": "Feature 생성을 위한 FeatureFactory 클래스"}, {"name": "FeatureFactory", "path": "Geometry <-> WKT 상호변환", "target": "", "grpId": 0, "desc": "Feature 생성을 위한 FeatureFactory 클래스"}, {"name": "FeatureFactory", "path": "Feature와GeoJson상호변환", "target": "", "grpId": 0, "desc": "Feature 생성을 위한 FeatureFactory 클래스"}, {"name": "FullScreenControl", "path": "마우스 좌표 표시/전체화면", "target": "", "grpId": 0, "desc": "전체화면 생성 클래스"}, {"name": "FullScreenControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "전체화면 생성 클래스"}, {"name": "FullScreenControl", "path": "화면제어 컨트롤", "target": "", "grpId": 0, "desc": "전체화면 생성 클래스"}, {"name": "HomeControl", "path": "북마크 생성/홈 이동", "target": "", "grpId": 0, "desc": "홈 이동 클래스"}, {"name": "Layer", "path": "레이어 추가/삭제", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "KML 데이터 불러오기", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "레이어 on/off", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "레이어 Z-Index 설정", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "레이어 필터", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "레이어 편집", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "Geometry <-> WKT 상호변환", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "색 추출기", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "레이어 Extent", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "레이어 다각형으로 자르기", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "라벨 스타일", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "Layer와GeoJson상호변환", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "Layer", "path": "Feature 정보 조회", "target": "", "grpId": 0, "desc": "레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다."}, {"name": "LayerFactory", "path": "레이어 추가/삭제", "target": "", "grpId": 0, "desc": "레이어 생성 클래스"}, {"name": "LayerInfoControl", "path": "레이어 속성정보 조회", "target": "", "grpId": 0, "desc": "레이어 정보 조회 클래스"}, {"name": "Map", "path": "지도생성/배경지도 설정", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "X, Y 좌표변환", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "기본 마커 생성", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "팝업 생성", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "SLD 적용 Scale", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "레이어 편집", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "애니메이션클러스터 분석", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "사용자가 지정한 영역으로 속성정보 조회", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "Feature 정보 조회", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "Feature 별 면적/길이 측정", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "네비게이션", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "레이어 on/off", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "Map", "path": "라인으로 다각형 분할", "target": "", "grpId": 0, "desc": "지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스"}, {"name": "<PERSON><PERSON>", "path": "기본 마커 생성", "target": "", "grpId": 0, "desc": "마커 생성 클래스"}, {"name": "<PERSON><PERSON>", "path": "드래그 가능한 마커 생성", "target": "", "grpId": 0, "desc": "마커 생성 클래스"}, {"name": "<PERSON><PERSON>", "path": "오버레이 컨트롤", "target": "", "grpId": 0, "desc": "마커 생성 클래스"}, {"name": "MeasureControl", "path": "그리기/측정도구", "target": "", "grpId": 0, "desc": "지도 측정 도구 클래스"}, {"name": "MeasureControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "지도 측정 도구 클래스"}, {"name": "MeasureControl", "path": "그리기/측정/초기화 컨트롤", "target": "", "grpId": 0, "desc": "지도 측정 도구 클래스"}, {"name": "MousePositionControl", "path": "마우스 좌표 표시/전체화면", "target": "", "grpId": 0, "desc": "마우스 좌표 생성 클래스"}, {"name": "MoveControl", "path": "네비게이션", "target": "", "grpId": 0, "desc": "현재 화면 기준으로 이전/다음 화면 이동 생성 클래스"}, {"name": "MoveControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "현재 화면 기준으로 이전/다음 화면 이동 생성 클래스"}, {"name": "MoveControl", "path": "화면제어 컨트롤", "target": "", "grpId": 0, "desc": "현재 화면 기준으로 이전/다음 화면 이동 생성 클래스"}, {"name": "OverviewMapControl", "path": "인덱스맵/지도회전", "target": "", "grpId": 0, "desc": "인덱스맵 생성 클래스"}, {"name": "OverviewMapControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "인덱스맵 생성 클래스"}, {"name": "OverviewMapControl", "path": "화면도구 컨트롤", "target": "", "grpId": 0, "desc": "인덱스맵 생성 클래스"}, {"name": "Popup", "path": "팝업 생성", "target": "", "grpId": 0, "desc": "팝업 생성 클래스"}, {"name": "Popup", "path": "오버레이 컨트롤", "target": "", "grpId": 0, "desc": "팝업 생성 클래스"}, {"name": "PrintControl", "path": "지도 출력/저장", "target": "", "grpId": 0, "desc": "프린트 설정 클래스"}, {"name": "PrintControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "프린트 설정 클래스"}, {"name": "PrintControl", "path": "저장/출력 컨트롤", "target": "", "grpId": 0, "desc": "프린트 설정 클래스"}, {"name": "Projection", "path": "공간정보 좌표변환", "target": "", "grpId": 0, "desc": "좌표 변환 클래스"}, {"name": "Projection", "path": "Extent 좌표변환", "target": "", "grpId": 0, "desc": "좌표 변환 클래스"}, {"name": "Projection", "path": "X, Y 좌표변환", "target": "", "grpId": 0, "desc": "좌표 변환 클래스"}, {"name": "RotationControl", "path": "인덱스맵/지도회전", "target": "", "grpId": 0, "desc": "화면을 회전 시키는 기능\nalt + shift 드래그로 지도 회전"}, {"name": "RotationControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "화면을 회전 시키는 기능\nalt + shift 드래그로 지도 회전"}, {"name": "RotationControl", "path": "화면제어 컨트롤", "target": "", "grpId": 0, "desc": "화면을 회전 시키는 기능\nalt + shift 드래그로 지도 회전"}, {"name": "ScaleControl", "path": "축척", "target": "", "grpId": 0, "desc": "축척 표시 설정 클래스"}, {"name": "ScaleControl", "path": "화면도구 컨트롤", "target": "", "grpId": 0, "desc": "축척 표시 설정 클래스"}, {"name": "SLD", "path": "점 스타일", "target": "", "grpId": 0, "desc": "WMS 스타일 관리 클래스"}, {"name": "SLD", "path": "점 스타일", "target": "", "grpId": 0, "desc": "범례용 Element 생성(SLD)"}, {"name": "Style", "path": "단일 점 스타일", "target": "", "grpId": 0, "desc": "스타일 관리 클래스"}, {"name": "Style", "path": "동적스타일 ↔ JSON", "target": "", "grpId": 0, "desc": "스타일 관리 클래스"}, {"name": "Style", "path": "동적 파이 차트 스타일", "target": "", "grpId": 0, "desc": "스타일 관리 클래스"}, {"name": "Style", "path": "범례용 Element", "target": "", "grpId": 0, "desc": "스타일 관리 클래스"}, {"name": "StyleFactory", "path": "복합스타일", "target": "", "grpId": 0, "desc": "스타일 생성 클래스"}, {"name": "StyleFactory", "path": "범례용 Element", "target": "", "grpId": 0, "desc": "스타일 생성 클래스"}, {"name": "StyleFactory", "path": "유형별 스타일(점-심볼)", "target": "", "grpId": 0, "desc": "스타일 생성 클래스"}, {"name": "StyleFactory", "path": "범위별 스타일", "target": "", "grpId": 0, "desc": "스타일 생성 클래스"}, {"name": "StyleFunction", "path": "동적 스타일", "target": "", "grpId": 0, "desc": "스타일 Function 생성 클래스"}, {"name": "StyleFunction", "path": "동적 파이 차트 스타일", "target": "", "grpId": 0, "desc": "스타일 Function 생성 클래스"}, {"name": "SwiperControl", "path": "스와이퍼", "target": "", "grpId": 0, "desc": "지도 스와이퍼 설정 클래스"}, {"name": "SwiperControl", "path": "사용자 정의 스와이퍼", "target": "", "grpId": 0, "desc": "사용자 정의 스와이퍼"}, {"name": "ZipControl", "path": "Server없이 Layer 생성하기", "target": "", "grpId": 0, "desc": "Server없이 Layer 생성하는 클래스"}, {"name": "ZoomControl", "path": "지도생성/배경지도설정", "target": "", "grpId": 0, "desc": "지도 줌 설정클래스"}, {"name": "ZoomControl", "path": "분할지도 상세", "target": "", "grpId": 0, "desc": "지도 줌 설정클래스"}, {"name": "ZoomControl", "path": "화면제어 컨트롤", "target": "", "grpId": 0, "desc": "지도 줌 설정클래스"}]