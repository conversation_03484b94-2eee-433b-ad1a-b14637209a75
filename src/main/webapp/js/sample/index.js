/**
 * index.js
 */
$(document).ready(function() {

	const urlParams = new URLSearchParams(window.location.search);
	const sampleName = urlParams.get('sampleName');

	if (sampleName) {
		menu.init(sampleName, makeMenuJson, loadSample, true);
	}
	else {
		menu.init("지도생성", makeMenuJson, loadSample, false);
	}
});

function makeMenuJson() {

	var keys = Object.keys(menuJson);

	for (var i = 0; i < keys.length; i++) {

		var gKey = keys[i];
		var sList = menuJson[gKey].submenu;

		for (var j = 0; j < sList.length; j++) {
			sList[j].grpId = gKey;
			sList[j].target = "";
		}
	}
}

var menuJson = {

		0: {
			name : "지도제어",
			submenu : [
					{
						"name" : "프로젝트 기본 설정",
						"desc" : "프로젝트 기본 설정 값 셋팅",
						"path" : "/samplehtml/map/mapControl/odfInit.html"
					},
					{
						"name" : "지도생성",
						"desc" : "지도를 생성 기능 제공",
						"path" : "/samplehtml/map/mapControl/createMap.html"
					},
					{
						"name" : "배경지도만 키보드로 이동",
						"desc" : "키보드 화살표키를 누르면 레이어는 이동되지않고 배경지도를 10픽셀씩 이동하는 기능 제공",
						"path" : "/samplehtml/map/mapControl/keyboardMoveMap.html"
					},
					{
						"name" : "지도 좌표계 변경",
						"desc" : "지도 좌표계 변경 기능 제공",
						"path" : "/samplehtml/map/mapControl/changeMapProjection.html"
					},
					{
						"name" : "배경지도 설정",
						"desc" : "배경지도 설정 기능 제공",
						"path" : "/samplehtml/map/mapControl/basemapControl.html"
					},
					{
						"name": "배경지도 최적화",
						"desc": "배경지도 변경시 지도 좌표계 변경하여 조금 더 선명한 배경지도를 이용",
						"path": "/samplehtml/map/mapControl/basemapControl_optimization.html"
					},
					{
						"name" : "사용자 지정 배경지도",
						"desc" : "사용자 정의 배경지도 그룹/레이어 관리 기능 제공",
						"path" : "/samplehtml/map/mapControl/customizeBasemap.html"
				},
				{
					"name": "사용자 지정 배경지도(webGl 벡터타일 레이어)",
					"desc": "사용자 정의 배경지도 그룹/레이어 관리 기능 제공",
					"path": "/samplehtml/map/mapControl/customizeBasemap_vectorTile.html"
				},
				{
					"name" : "구글 지도에 ODF 레이어 추가",
					"desc" : "구글 지도에 ODF 레이어를 추가하여 사용",
					"path" : "/samplehtml/map/mapControl/customizeBasemap_google.html"
				},
				{
					"name" : "네이버 지도에 ODF 레이어 추가",
					"desc" : "네이버 지도에 ODF 레이어를 추가하여 사용",
					"path" : "/samplehtml/map/mapControl/customizeBasemap_naver.html"
				},
				{
					"name" : "카카오 지도에 ODF 레이어 추가",
					"desc" : "카카오 지도에 ODF 레이어를 추가하여 사용",
					"path" : "/samplehtml/map/mapControl/customizeBasemap_kakao.html"
				},
					{
						"name" : "축척",
						"desc" : "지도의 축척정보 조회 기능 제공",
						"path" : "/samplehtml/map/mapControl/scaleControl.html"
					},
					{
						"name" : "네비게이션",
						"desc" : "지도의  네비게이션(이전/다음 화면) 기능 제공",
						"path" : "/samplehtml/map/mapControl/navigator.html"
					},
					{
						"name" : "인덱스맵",
						"desc" : "지도를 한 눈에 볼 수 있는 인덱스맵 제공 ",
						"path" : "/samplehtml/map/mapControl/indexmapControl.html"
					},
					{
						"name" : "지도회전",
						"desc" : "지도 회전 기능 제공",
						"path" : "/samplehtml/map/mapControl/rotationControl.html"
					},
					{
						"name" : "그리기 도구",
						"desc" : "점, 선, 면, 곡선, 다각형, 사각형, 버퍼 그리기 도구 기능 제공",
						"path" : "/samplehtml/map/mapControl/drawControl.html"
					},
					{
						"name" : "측정 도구",
						"desc" : "면적/거리/측정 기능 제공",
						"path" : "/samplehtml/map/mapControl/measureControl.html"
					},
					{
						"name" : "지도 출력/다운로드",
						"desc" : "지도 출력 및 다운로드(PDF/이미지) 기능 제공",
						"path" : "/samplehtml/map/mapControl/capture&print.html"
					},
					{
						"name" : "마우스 좌표 표시/전체화면",
						"desc" : "사용자의 마우스 좌표 표출 기능과 전체화면 기능 제공",
						"path" : "/samplehtml/map/mapControl/mousePosition.html"

					},
//					{
//						"name" : "북마크 생성/홈 이동",
//						"desc" : "사용자가 지정한 북마크위치로 지도 위치 및 줌레벨 이동",
//						"path" : "/samplehtml/map/mapControl/bookmarkControl.html"
//
//					}
					/*
					 * { "name" : "지도 드래그/휠 막기", "desc" : "지도를 생성하고 배경지도 설정을 제공하고
					 * 있습니다. ", "path" : "/samplehtml/map/mapControl/drag&wheel.html" }
					 */
					]
		},
		1:{
			name : "화면 제어",
			submenu : [
				{
					"name" : "스와이퍼",
					"desc" : "지도스와이퍼 기능 제공",
					"path" : "/samplehtml/map/mapControl/swiperControl.html"

				},
				/*{
					"name" : "사용자 정의 스와이퍼",
					"desc" : "사용자 정의대로 스와이퍼를 생성하는 기능 제공",
					"path" : "/samplehtml/map/mapControl/customizeSwiperControl.html"

				},*/
				{
					"name" : "분할지도",
					"desc" : "2/3/4 분할지도 기능 제공",
					"path" : "/samplehtml/map/mapControl/dividedMap.html"

				},
				{
					"name" : "분할지도 상세",
					"desc" : "메인지도의 컨트롤/레이어 복사하여 분할된 지도에 적용하는 기능 제공",
					"path" : "/samplehtml/map/mapControl/dividedMap_detailSet.html"

				}, {
					"name" : "사용자 정의 2분할 지도",
					"desc" : "사용자 정의대로 2분할 지도를 생성하는 기능 제공",
					"path" : "/samplehtml/map/mapControl/dualDividedMap.html"

				}, {
					"name" : "사용자 정의 4분할 지도",
					"desc" : "사용자 정의대로 4분할 지도를 생성하는 기능 제공",
					"path" : "/samplehtml/map/mapControl/quadDividedMap.html"
				},
			]
		},
//
//		1:{
//			name : "TOC 제어",
//			submenu : [
//				 {
//					"name" : "TOC 기본",
//					"desc" : "TOC 기본 1레벨 (그룹추가/제거, 레이어 ON/OFF, 레이어 순서변경)",
//					"path" : "/samplehtml/map/layerControl/layerTOCOneDepth.html"
//				},
//				 {
//					"name" : "TOC 상세",
//					"desc" : "TOC 기본 2레벨 (상세보기, 필터, 스타일 지정 제공)",
//					"path" : "/samplehtml/map/layerControl/layerTOCTwoDepth.html"
//				},
//			]
//		},
		2 : {
			name : "사용자 정의 컨트롤",
			submenu : [
				{
					"name" : "컨트롤 순서 변경",
					"desc" : "사용자 정의대로 컨트롤 순서 변경하는 기능 제공",
					"path" :  "/samplehtml/map/mapControl/customControlOrder.html"

				},
				{
					"name" : "사용자 정의 단일 컨트롤",
					"desc" : "사용자 정의대로 단일 컨트롤을 생성하는 기능 제공  ",
					"path" :  "/samplehtml/map/mapControl/customControl.html"

				},
				{
					"name" : "사용자 정의 서브 그룹 컨트롤",
					"desc" : "사용자 정의대로 서브 그룹 컨트롤 생성하는 기능 제공",
					"path" :  "/samplehtml/map/mapControl/customSubGroupControl.html"

				},
				{
					"name" : "분할지도-사용자정의 컨트롤 추가",
					"desc" : "분할지도에 사용자정의 컨트롤 추가 기능 제공",
					"path" :  "/samplehtml/map/mapControl/customControlDIvidmap.html"

				},
			]

		},

//		2: {
//			name : "사용자 정의 지도제어",
//			submenu : [
//					{
//						"name" : "그리기/측정/초기화 컨트롤",
//						"desc" : "그리기 관련 컨트롤 정의",
//						"path" : "/samplehtml/map/customMapControl/drawInteraction.html"
//					},
//					{
//						"name" : "다운로드/출력 컨트롤",
//						"desc" : "추출관련 컨트롤 정의",
//						"path" : "/samplehtml/map/customMapControl/exportControl.html"
//					},
//					{
//						"name" : "오버레이 컨트롤",
//						"desc" : "오버레이 컨트롤 정의",
//						"path" : "/samplehtml/map/customMapControl/overlay.html"
//					},
//					{
//						"name" : "화면제어 컨트롤",
//						"desc" : "화면제어 컨트롤 정의",
//						"path" : "/samplehtml/map/customMapControl/viewingControl.html"
//					},
//					{
//						"name" : "화면도구 컨트롤",
//						"desc" : "화면도구 컨트롤 정의",
//						"path" : "/samplehtml/map/customMapControl/viewToolControl.html"
//					},
//					]
//		},
		3 : {
			name : "레이어 생성",
			submenu : [
				{
					"name" : "wfs 레이어 생성(geoserver 타입)",
					"desc" : "wfs 레이어 생성 기능 제공",
					"path" : "/samplehtml/map/produceLayer/wfsLayer.html"
				},
				{
					"name": "wfs 레이어 생성(geoserver 타입) -attribution 적용",
					"desc": "wfs 레이어 생성시 attribution 정의",
					"path": "/samplehtml/map/produceLayer/wfsLayer_attributions.html"
				},
				{
					"name" : "wms 레이어 생성(geoserver 타입)",
					"desc" : "wms 레이어 생성 기능 제공",
					"path" : "/samplehtml/map/produceLayer/wmsLayer.html"
				},
				{
					"name" : "wmts 레이어 생성(geoserver 타입)",
					"desc" : "wmts 레이어 생성 기능 제공",
					"path" : "/samplehtml/map/produceLayer/wmtsLayer.html"
				},
				{
					"name" : "빈 벡터 레이어 생성",
					"desc" : "빈 벡터 레이어 생성 기능 제공",
					"path" : "/samplehtml/map/produceLayer/emptyLayer.html"
				},
				{
					"name" : "geojson 레이어 생성",
					"desc" : "geojson 형식의 데이터로 레이어 생성 기능 제공",
					"path" : "/samplehtml/map/produceLayer/geojsonLayer.html"
				},
				{
					"name" : "shape 파일로 Layer 생성(미리보기)",
					"desc" : "shape 파일로 Layer 생성(미리보기) 기능 제공",
					"path" : "/samplehtml/map/produceLayer/drawLayerToFile.html"
				},
				{
					"name" : "KML 레이어 생성",
					"desc" : "KML 형식의 데이터로 도형을 생성하는 기능 제공",
					"path" : "/samplehtml/map/produceLayer/kmlLayer.html"
				},
				{
					"name" : "CSV 레이어 생성",
					"desc" : "CSV 형식의 데이터로 도형을 생성하는 기능 제공",
					"path" : "/samplehtml/map/produceLayer/csvLayer.html"
				},
				/*{
					"name": "국가공간정보 포털 WMS 레이어 생성(api 타입)",
					"desc": "국가공간정보 포털 api를 이용하여 WMS 레이어 생성 기능 제공",
					"path": "/samplehtml/map/produceLayer/nsidWMSLayer.html"
				},*/
				{
					"name": "vWorld WMS 레이어 생성(api 타입)",
					"desc": "vWorld api를 이용하여 WMS 레이어 생성 기능 제공",
					"path": "/samplehtml/map/produceLayer/vWorldWMSLayer.html"
				},
				{
					"name": "vWorld WFS 레이어 생성(api 타입)",
					"desc": "vWorld api를 이용하여  WFS 레이어 생성 기능 제공",
					"path": "/samplehtml/map/produceLayer/vWorldWFSLayer.html"
				},
				{
					"name": "kakao xyz 레이어 생성(api 타입)",
					"desc": "kakao api를 이용하여 xyz 레이어 생성 기능 제공",
					"path": "/samplehtml/map/produceLayer/kakaoXYZLayer.html"
				},
				{
					"name": "kakao 로드뷰 레이어 생성(api 타입)",
					"desc": "kakao api를 이용하여 로드뷰 레이어 생성 기능 제공",
					"path": "/samplehtml/map/produceLayer/kakaoRoadview.html"
				},
				{
					"name": "vWorld TMS 레이어 생성(api 타입)",
					"desc": "vWorld api를 이용하여 tms 레이어 생성 기능 제공",
					"path": "/samplehtml/map/produceLayer/vWorldTMSLayer.html"
				},
				{
					"name": "이미지 레이어 생성",
					"desc": "이미지 레이어 생성 기능 제공",
					"path": "/samplehtml/map/produceLayer/ImageLayer.html"
				},
				{
					"name": "타일 디버그 레이어 생성",
					"desc": "타일 디버그 레이어 생성 기능 제공",
					"path": "/samplehtml/map/produceLayer/tileDebugLayer.html"
				},
				{
					"name": "벡터 타일 레이어 (pbf)",
					"desc": "vectorTile 레이어 생성 기능",
					"path": "/samplehtml/map/produceLayer/vectorTileLayer.html"
				},
				{
					"name": "벡터 레이어 생성 속도 비교(canvas vs webGL)",
					"desc": "벡터 레이어 생성 속도 비교(canvas vs webGL)",
					"path": "/samplehtml/map/produceLayer/webGLVectorLayer_performance.html"
				},
				{
					"name": "webGL 벡터(wfs) 레이어 생성",
					"desc": "webGL 벡터(wfs) 레이어 생성 기능",
					"path": "/samplehtml/map/produceLayer/webGLVectorLayer.html"
				},
				{
					"name": "webGL 벡터(geojson) 레이어 생성",
					"desc": "webGL 벡터(geojson) 레이어 생성 기능",
					"path": "/samplehtml/map/produceLayer/webGLVectorLayer_geojson.html"
				},
				{
					"name": "webGL 타일(wms) 레이어 생성",
					"desc": "webGL 타일(wms) 레이어 생성 기능",
					"path": "/samplehtml/map/produceLayer/webGLTileLayer.html"
				},
				{
					"name": "webGL 타일(wmts) 레이어 생성",
					"desc": "webGL 타일(wmts) 레이어 생성 기능",
					"path": "/samplehtml/map/produceLayer/webGLTileLayer_wmts.html"
				},
				{
					"name": "webGL 벡터 타일(pbf) 레이어 생성",
					"desc": "webGL 타일(pbf) 레이어 생성 기능",
					"path": "/samplehtml/map/produceLayer/webGLVectorTileLayer.html"
				},
				{
					"name": "webGL GeoTiff 레이어 생성",
					"desc": "webGL GeoTiff 레이어 생성 기능",
					"path": "/samplehtml/map/produceLayer/webGLGeoTiffLayer.html"
				},
				{
					"name": "svg 레이어 생성",
					"desc": "svg 레이어 생성 기능",
					"path": "/samplehtml/map/produceLayer/svgLayer.html"
				},
				{
					"name": "카토그램 제작(svg 레이어 생성 응용)",
					"desc": "svg 레이어 생성 기능을 이용하여 카토그램 제작",
					"path": "/samplehtml/map/produceLayer/svgLayer_cartogram.html"
				}

			]
		},
		4 : {
			name : "레이어 제어",
			submenu : [

				{
					"name" : "레이어 on/off",
					"desc" : "레이어를 지도에서 on/off ",
					"path" : "/samplehtml/map/layerControl/layerSwitch.html"
				},
				{
					"name" : "레이어 Z-Index(순서) 설정",
					"desc" : "레이어의 순서를 설정",
					"path" : "/samplehtml/map/layerControl/layerZIndex.html"
				},{
					"name" : "레이어 가시 범위 설정",
					"desc" : "레이어의 가시 범위 설정",
					"path" : "/samplehtml/map/layerControl/layerSetZoom.html"
				}, {
					"name" : "레이어 필터",
					"desc" : "레이어에 필터를 적용하여 필터링 된 레이어 정보 표출",
					"path" : "/samplehtml/map/layerControl/layerDefineQuery.html"
				},
				{
					"name" : "레이어 공간검색",
					"desc" : "영역을 선택하여 레이어 필터",
					"path" : "/samplehtml/map/layerControl/layerSpatialFilter.html"
				},
				{
					"name" : "레이어 Extent",
					"desc" : "레이어 영역에 맞게 지도 영역 이동",
					"path" : "/samplehtml/map/layerControl/layerExtent.html"
				},
				{
					"name" : "Layer와GeoJson 상호변환",
					"desc" : "Layer와 GeoJson 상호변환 기능 제공",
					"path" : "/samplehtml/map/layerControl/layerInterChangeGeoJson.html"
				},
	//			{
	//				"name" : "행정구역 표시 및 위치 이동",
	//				"desc" : "행정구역을 표시 및 선택한 행정구역 위치 이동 기능 제공",
	//				"path" : "/samplehtml/map/mapControl/area.html"
	//			},
				{
					"name" : "X, Y 좌표변환",
					"desc" : "좌표값(X,Y)을 다른 좌표계 값으로 변환 기능 제공",
					"path" : "/samplehtml/map/layerControl/transformXY.html"
				},
				{
					"name" : "Extent 좌표변환",
					"desc" : "Extent의 좌표를 다른 좌표계 값으로 변환 기능 제공",
					"path" : "/samplehtml/map/layerControl/transformExtent.html"
				},
				{
					"name" : "라인으로 다각형 분할",
					"desc" : "사용자가 그린 선으로 다각형을 분할",
					"path" : "/samplehtml/map/layerControl/SplitFeature.html"
				},
				{
					"name" : "영역으로 버텍스 다중 삭제",
					"desc" : "사용자가영역으로(다각형) 피쳐 버텍스 다중 삭제",
					"path" : "/samplehtml/map/layerControl/deleteVertex.html"
				},
				{
					"name": "레이어 원형으로 자르기",
					"desc": "레이어 원형으로 자르기 기능 제공",
					"path": "/samplehtml/map/layerControl/cropCircleLayer.html"
				},
				{
					"name": "레이어 다각형으로 자르기",
					"desc": "레이어 다각형으로 자르기 기능 제공",
					"path": "/samplehtml/map/layerControl/cropPolygonLayer.html"
				},
				{
					"name": "레이어 멀티-다각형으로 자르기",
					"desc": "레이어 멀티-다각형으로 자르기 기능 제공",
					"path": "/samplehtml/map/layerControl/cropMultiPolygonLayer.html"
				}
			]
		},
		5:{
			name : "레이어 편집",
			submenu : [
				{
					"name" : "피처를 그려서 레이어에 추가",
					"desc" : "피처를 그려서 레이어에 추가하여 서버 적용 기능 제공",
					"path" : "/samplehtml/map/modifyLayer/insertFeature.html"
				},
				{
					"name" : "선택 피처 삭제",
					"desc" : "선택한 피처를 삭제하여 서버 적용 기능 제공",
					"path" : "/samplehtml/map/modifyLayer/deleteFeature.html"
				},
				{
					"name" : "드래그로 레이어/피처 이동/회전/크기조절",
					"desc" : "드래그로 레이어/피처 이동/회전/크기조절하여 서버 적용 기능 제공",
					"path" : "/samplehtml/map/modifyLayer/dragTranslate.html"
				},
				{
					"name" : "드래그로 레이어 이동",
					"desc" : "드래그로 레이어 이동하여 서버 적용 기능 제공",
					"path" : "/samplehtml/map/modifyLayer/moveLayer.html"
				},
				{
					"name" : "드래그로 피처 편집",
					"desc" : "드래그로 피처 편집하여 서버적용 기능 제공",
					"path" : "/samplehtml/map/modifyLayer/modifyFeature.html"
				},
				{
					"name" : "드래그로 피처 이동",
					"desc" : "드래그로 피처 이동하여 서버적용 기능 제공",
					"path" : "/samplehtml/map/modifyLayer/moveFeature.html"
				},
				{
					"name" : "드래그로 피처 크기 조절 및 회전",
					"desc" : "드래그로 피처 크기 조절 및 회전하여 서버적용 기능 제공",
					"path" : "/samplehtml/map/modifyLayer/transformFeature.html"
				},
				{
					"name" : "스냅",
					"desc" : "레이어를 편집/피처 추가 기능을 스냅과 함께 제공",
					"path" : "/samplehtml/map/modifyLayer/modifyLayer_Snap.html"
				},
			]
		},
		6: {
			"name": "피처(도형) 제어",
			"submenu": [
				{
					"name": "Feature 별 면적/길이 측정",
					"desc": "그리기도구로 그린 피쳐의 면적/길이값 조회 기능 제공",
					"path": "/samplehtml/map/featureControl/featureMeasure.html"
				},
				{
					"name" : "피처 속성정보 조회(팝업)",
					"desc" : "서버를 통해 피처 속성정보를 조회",
					"path" : "/samplehtml/map/featureControl/selectFeatureInfo.html"
				},
				{
					"name" : "피처 속성정보 조회(벡터레이어)",
					"desc" : "벡터레이어에 한해서 서버를 통하지 않고 선택한 Feature의 정보 조회",
					"path" : "/samplehtml/map/featureControl/selectFeatureOnClick.html"
				},
				{
					"name": "사용자가 그린 도형으로 피처 속성정보 조회",
					"desc": "사용자가 그린 도형으로 피처 속성정보 조회",
					"path": "/samplehtml/map/featureControl/selectFeatureInfo_drawFeature.html"
				},
				{
					"name" : "Feature <-> GeoJson 상호변환",
					"desc" : "Feature와 GeoJson 상호변환 기능 제공",
					"path" : "/samplehtml/map/featureControl/FeatureInterChangeGeoJson.html"
				},
				{
					"name" : "Geometry <-> WKT 상호변환",
					"desc" : "Feature의 Geomtry와 WKT 포맷 상호변환 기능 제공",
					"path" : "/samplehtml/map/featureControl/featureChangeWKT.html"
				},
				{
					"name" : "Circle -> Polygon으로 변환",
					"desc" : "Circle Geometry를 Polygon Geometry로 변환 기능 제공",
					"path" : "/samplehtml/map/featureControl/polygonFromCircle.html"
				},
				{
					"name" : "공간정보 좌표변환",
					"desc" : "Feature의 좌표를 다른 좌표계 값으로 변환 기능 제공",
					"path" : "/samplehtml/map/featureControl/transformGeom.html"
				},
				{
					"name" : "라인 위의 두 점으로 라인 자르기",
					"desc" : "라인 위의 두 점으로 라인 자르기 기능 제공",
					"path" : "/samplehtml/map/featureControl/splitLine.html"
				},
				{
					"name" : "피처 다중 선택",
					"desc" : "피처 다중 선택 샘플 제공",
					"path" : "/samplehtml/map/featureControl/selectMultiFeature.html"
				},
			]
		},
		8: {
			name : "WFS 단일/복합 스타일",
			submenu : [ {
				"name" : "단일 점 스타일",
				"desc" : "단일 점 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simplePointStyle.html"
			},
			{
				"name" : "단일 타원 스타일",
				"desc" : "단일 타원 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simplePointStyle_ellipse.html"
			},
			{
				"name" : "단일 심볼 스타일",
				"desc" : "단일 심볼 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simpleSymbolStyle.html"
			},
			{
				"name" : "사용자 정의 심볼 스타일",
				"desc" : "사용자 정의 심볼 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simpleSymbolStyle_customImage.html"
			},
			{
				"name": "정다각형(regular shapes) 스타일",
				"desc": "정다각형(regular shapes) 스타일 생성하여 벡터레이어에 적용",
				"path": "/samplehtml/map/styleControl/simpleRegularShape.html"
			},
			{
				"name" : "단일 선 스타일",
				"desc" : "단일 선 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simpleLineStyle.html"
			}, {
				"name" : "단일 면 스타일",
				"desc" : "단일 면 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simplePolygonStyle.html"
			}, {
				"name" : "단일 면 스타일(라벨 위치 조정)",
				"desc" : "단일 면 라벨 스타일 위지 조정",
				"path" : "/samplehtml/map/styleControl/simplePolygonStyle_customPosition.html"
			}
			, {
				"name" : "단일 라벨 스타일",
				"desc" : "단일 라벨 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simpleLabelStyle.html"
			},{
				"name" : "단일 라벨 스타일(서식 지정)",
				"desc" : "단일 라벨 스타일을 서식을 지정하여 생성, 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simpleLabelStyle_richText.html"
			},
			{
				"name" : "단일 패턴 스타일",
				"desc" : "단일 패턴 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simplePatternStyle.html"
			},
			{
				"name": "파이 차트 스타일",
				"desc": "파이 차트 스타일을 생성하여 벡터 레이어에 적용",
				"path": "/samplehtml/map/styleControl/pieChartStyle.html"
			},
			{
				"name": "막대 차트 스타일",
				"desc": "막대 차트 스타일을 생성하여 벡터 레이어에 적용",
				"path": "/samplehtml/map/styleControl/barChartStyle.html"
			},
			{
				"name" : "복합스타일",
				"desc" : "복합 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/complexStyle.html"
			},
			{
				"name" : "스타일 객체 ↔ JSON",
				"desc" : "스타일 객체를 JSON 형태로 변경",
				"path" : "/samplehtml/map/styleControl/simpleStyleCvt2JSON.html"
			},
			{
				"name" : "(복합스타일) 도형 버텍스 표현 스타일",
				"desc" : "도형 버텍스 표현 스타일",
				"path" : "/samplehtml/map/styleControl/vertexStyle.html"
			},
			]
		},
		9:{name : "WFS 동적스타일",
			submenu : [
				{
				"name" : "동적 스타일",
				"desc" : "동적 스타일을 생성하여 벡터 레이어에 적용",
				"path" : "/samplehtml/map/styleControl/simpleFunctionStyle.html"
				},
				{
					"name": "동적 파이 차트 스타일",
					"desc": "동적 파이 차트 스타일을 생성하여 벡터 레이어에 적용",
					"path": "/samplehtml/map/styleControl/dynamicPieChartStyle.html"
				},
				{
					"name": "동적 막대 차트 스타일(가로)",
					"desc": "동적 막대 차트 스타일을 생성하여 벡터 레이어에 적용",
					"path": "/samplehtml/map/styleControl/dynamicBarChartStyle_horizon.html"
				},
				{
					"name": "동적 막대 차트 스타일(세로)",
					"desc": "동적 막대 차트 스타일을 생성하여 벡터 레이어에 적용",
					"path": "/samplehtml/map/styleControl/dynamicBarChartStyle.html"
				},
				{
					"name" : "유형별 스타일(점-원형)",
					"desc" : "벡터레이어에 유형별 스타일 적용(점-원형)",
					"path" : "/samplehtml/map/styleControl/typeStyle.html"
				}, {
					"name" : "유형별 스타일(점-심볼)",
					"desc" : "벡터레이어에 유형별 스타일 적용(점-심볼)",
					"path" : "/samplehtml/map/styleControl/typeSymbolStyle.html"
				}, {
					"name" : "범위별 스타일",
					"desc" : "벡터레이어에 범위별로 스타일 적용",
					"path" : "/samplehtml/map/styleControl/rangeStyle.html"
				},  {
					"name" : "색 추출기",
					"desc" : "색 추출기를 이용하여 색 생성",
					"path" : "/samplehtml/map/styleControl/colorFactory.html"
				}, {
					"name" : "범례용 Element",
					"desc" : "벡터레이어 범례용 Element 생성",
					"path" : "/samplehtml/map/styleControl/createLegendElement.html"
				} , {
					"name" : "동적스타일 ↔ JSON",
					"desc" : "동적스타일객체를 JSON 형태로 변경",
					"path" : "/samplehtml/map/styleControl/functionStyleCvt2JSON.html"
				},
				{
					"name" : "화살표 스타일",
					"desc" : "방향을 스타일로 표현",
					"path" : "/samplehtml/map/styleControl/arrowStyle.html"
				},
			]
		},
		10: {
			name : "WMS 스타일(SLD)",
			submenu : [ {
				"name" : "점 스타일",
				"desc" : "WMS 점 스타일 적용",
				"path" : "/samplehtml/map/sldControl/pointStyle.html"
			},{
				"name" : "심볼 스타일",
				"desc" : "WMS 심볼 스타일 적용",
				"path" : "/samplehtml/map/sldControl/symbolStyle.html"
			},{
				"name" : "선 스타일",
				"desc" : "WMS 선 스타일 적용",
				"path" : "/samplehtml/map/sldControl/lineStyle.html"
			},{
				"name" : "면 스타일",
				"desc" : "WMS 면 스타일 적용",
				"path" : "/samplehtml/map/sldControl/polygonStyle.html"
			},{
				"name" : "라벨 스타일",
				"desc" : "WMS 라벨 스타일 적용",
				"path" : "/samplehtml/map/sldControl/labelStyle.html"
			},{
				"name" : "복합 스타일",
				"desc" : "WMS 복합 스타일(선스타일+라벨스타일) 적용",
				"path" : "/samplehtml/map/sldControl/sldComplexStyle.html"
			},{
				"name" : "유형별 스타일",
				"desc" : "WMS 유형별 스타일 적용",
				"path" : "/samplehtml/map/sldControl/sldTypeStyle.html"
			},{
				"name" : "SLD 적용 Scale",
				"desc" : "SLD 적용 Scale 조회",
				"path" : "/samplehtml/map/sldControl/selectSLDScale.html"
			},
			{
				"name" : "범례용 Element 생성(SLD)",
				"desc" : "SLD로 범례용 Elemnt 생성",
				"path" : "/samplehtml/map/sldControl/produceSLDElement.html"
			},
			{
				"name" : "wms 화살표 스타일",
				"desc" : "방향을 스타일로 표현",
				"path" : "/samplehtml/map/sldControl/wmsArrowStyle.html"
			},
			]
		},
		11: {
			name : "Flat 스타일 / 스타일 표현식",
			submenu : [
				{
					"name" : "Flat 단일 점 스타일",
					"desc" : "Flat 단일 점 스타일을 생성하여 벡터 레이어에 적용",
					"path" : "/samplehtml/map/flatStyle/simplePointStyle.html"
				},
				{
					"name" : "Flat 단일 타원 스타일",
					"desc" : "Flat 단일 타원 스타일을 생성하여 벡터 레이어에 적용",
					"path" : "/samplehtml/map/flatStyle/simplePointStyle_ellipse.html"
				},
				{
					"name" : "Flat 단일 심볼 스타일",
					"desc" : "Flat 단일 심볼 스타일을 생성하여 벡터 레이어에 적용",
					"path" : "/samplehtml/map/flatStyle/simpleSymbolStyle.html"
				},
				{
					"name": "Flat 정다각형(regular shapes) 스타일",
					"desc": "Flat 정다각형(regular shapes) 스타일 생성하여 벡터레이어에 적용",
					"path": "/samplehtml/map/flatStyle/simpleRegularShape.html"
				},
				{
					"name" : "Flat 단일 선 스타일",
					"desc" : "Flat 단일 선 스타일을 생성하여 벡터 레이어에 적용",
					"path" : "/samplehtml/map/flatStyle/simpleLineStyle.html"
				}, {
					"name" : "Flat 단일 면 스타일",
					"desc" : "Flat 단일 면 스타일을 생성하여 벡터 레이어에 적용",
					"path" : "/samplehtml/map/flatStyle/simplePolygonStyle.html"
				}, {
					"name" : "Flat 단일 라벨 스타일",
					"desc" : "Flat 단일 라벨 스타일을 생성하여 벡터 레이어에 적용",
					"path" : "/samplehtml/map/flatStyle/simpleLabelStyle.html"
				},{
					"name" : "Flat 복합스타일",
					"desc" : "Flat 복합 스타일을 생성하여 벡터 레이어에 적용",
					"path" : "/samplehtml/map/flatStyle/complexStyle.html"
				},
				{
					"name" : "Flat 스타일 객체 ↔ JSON",
					"desc" : "스타일 객체를 JSON 형태로 변경",
					"path" : "/samplehtml/map/flatStyle/simpleStyleCvt2JSON.html"
				},
				{
					"name" : "Flat 동적 스타일",
					"desc" : "동적 스타일을 생성하여 벡터 레이어에 적용",
					"path" : "/samplehtml/map/flatStyle/simpleFunctionStyle.html"
				},
				{
					"name" : "Flat 유형별 스타일(점-원형)",
					"desc" : "Flat 벡터레이어에 유형별 스타일 적용(점-원형)",
					"path" : "/samplehtml/map/flatStyle/typeStyle.html"
				},{
					"name" : "Flat 유형별 스타일(점-심볼)",
					"desc" : "Flat 벡터레이어에 유형별 스타일 적용(점-심볼)",
					"path" : "/samplehtml/map/flatStyle/typeSymbolStyle.html"
				}, {
					"name" : "Flat 범위별 스타일",
					"desc" : "Flat 벡터레이어에 범위별로 스타일 적용",
					"path" : "/samplehtml/map/flatStyle/rangeStyle.html"
				},{
					"name" : "Flat 범례용 Element",
					"desc" : "Flat 벡터레이어 범례용 Element 생성",
					"path" : "/samplehtml/map/flatStyle/createLegendElement.html"
				},
				{
					"name": "webGL GeoTiff 레이어 스타일",
					"desc": "webGL GeoTiff 레이어 스타일 적용",
					"path": "/samplehtml/map/flatStyle/webGLGeoTiffLayerStyle.html"
				},
				{
					"name": "webGL Tile 레이어 스타일(kakao xyz)",
					"desc": "webGL Tile 레이어 스타일 적용",
					"path": "/samplehtml/map/flatStyle/kakaoXYZLayerStyle.html"
				},
			]
		},
		12 : {
			name : "애니메이션",
			submenu : [
				{
					"name": "선을 따라 이동 애니메이션",
					"desc": "선을 따라 아이콘이 이동하는 애니메이션 기능 적용",
					"path": "/samplehtml/map/animation/followLineAnimation.html"
				},
				{
					"name": "파동 애니메이션",
					"desc": "마우스 포인터 아래 도형이 있으면 파동 애니메이션 기능 적용",
					"path": "/samplehtml/map/animation/waveAnimation.html"
				},
				{
					"name": "비행 애니메이션",
					"desc": "arc.js를 이용하여 출발지에서 도착지까지의 경로를 아크 형태로 애니메이션으로 표현",
					"path": "/samplehtml/map/animation/flightAnimation.html"
				},
				{
					"name": "도형 점멸 애니메이션",
					"desc": "깜빡이는 애니메이션 적용",
					"path": "/samplehtml/map/animation/blinkAnimation.html"
				},
			]
		},
		13 : {
			name : "오버레이",
			submenu : [
				{
					"name" : "기본 마커 생성",
					"desc" : "마커를 생성 기능 제공",
					"path" : "/samplehtml/map/markerControl/marker.html"
				},
				{
					"name" : "커스텀 마커 생성",
					"desc" : "커스텀 마커를 생성 기능 제공",
					"path" : "/samplehtml/map/markerControl/customMarker.html"
				},
				{
					"name" : "마커로 이미지 레이어 유사하게 제작",
					"desc" : "마커를 이용하여 이미지를 지도 위에 올려 레이어와 비슷하게 이용",
					"path" : "/samplehtml/map/markerControl/imageLayerMarker.html"
				},

				{
					"name" : "마우스 툴팁 생성",
					"desc" : "마커를 응용하여 마우스 커서를 따라다니는 툴팁 생성",
					"path" : "/samplehtml/map/markerControl/mouseTooltip.html"
				},
				{
					"name" : "팝업 생성",
					"desc" : "팝업을 생성 기능 제공",
					"path" : "/samplehtml/map/markerControl/popup.html"
				}
			]
		},
		14 : {
			name : "이벤트",
			submenu : [ {
				"name" : "이벤트 추가/해제",
				"desc" : "이벤트를 추가/해제",
				"path" : "/samplehtml/map/eventControl/basicEvent.html"
			}, {
				"name" : "지도 이벤트",
				"desc" : "지도 객체에 이벤트를 연결",
				"path" : "/samplehtml/map/eventControl/mapEvent.html"
			}, {
				"name" : "마커 이벤트",
				"desc" : "마커 객체에 이벤트를 연결",
				"path" : "/samplehtml/map/eventControl/markerEvent.html"
			}, {
				"name" : "뷰 이벤트",
				"desc" : "뷰 객체에 이벤트를 연결",
				"path" : "/samplehtml/map/eventControl/viewEvent.html"
			}, {
				"name" : "레이어 이벤트",
				"desc" : "레이어 객체에 이벤트를 연결",
				"path" : "/samplehtml/map/eventControl/layerEvent.html"
			},
			 {
				"name" : "이벤트 강제 실행",
				"desc" : "등록된 이벤트를 강제 실행",
				"path" : "/samplehtml/map/eventControl/triggerEvent.html"
			},
			 {
				"name" : "이벤트 등록 확인",
				"desc" : "이벤트가 등록되어있는지 확인",
				"path" : "/samplehtml/map/eventControl/hasEvent.html"
			}
			// {
			// "name": "인터렉션 이벤트",
			// "desc": "인터렉션 객체에 이벤트를 연결합니다.",
			// "path": "/samplehtml/map/eventControl/interactionEvent.html"
			// },
			// {
			// "name": "HTMLElemnt 이벤트",
			// "desc": "HTMLElemnt 객체에 이벤트를 연결합니다.",
			// "path": "/samplehtml/map/eventControl/HTMLElementEvent.html"
			// },
			]
		},
		15 : {
			name : "분석 레이어",
			submenu : [
			{
				"name" : "클러스터 레이어",
				"desc" : "클러스터 분석 레이어 표출 방법",
				"path" : "/samplehtml/map/analysisControl/cluster.html"
			},
			{
				"name" : "클러스터 레이어 집계 데이터 위치 변경",
				"desc" : "집계 대상 정보를 이용해 집계 포인트 위치 변경. ",
				"path" : "/samplehtml/map/analysisControl/cluster_positionChange.html"
			},
			{
				"name" : "클러스터 레이어 응용",
				"desc" : "클러스터 레이어를 이용하여 인근에 있는 도형을 묶어 표출합니다.",
				"path" : "/samplehtml/map/analysisControl/cluster_smaple.html"
			},
			{
				"name" : "geojson 타입으로 클러스터 레이어 생성",
				"desc" : "CSV 형식의 데이터로 도형을 생성하는 기능 제공",
				"path" : "/samplehtml/map/analysisControl/cluster_geojson.html"
			},
			{
				"name" : "애니메이션클러스터 레이어",
				"desc" : "애니메이션클러스터 분석 레이어 표출 방법",
				"path" : "/samplehtml/map/analysisControl/animateCluster.html"
			},
			{
				"name" : "핫스팟 분석 레이어",
				"desc" : "핫스팟 분석 레이어 표출 방법",
				"path" : "/samplehtml/map/analysisControl/hotspot.html"
			},
			{
				"name" : "밀집도 분석 레이어",
				"desc" : "밀집도 분석 레이어 표출 방법",
				"path" : "/samplehtml/map/analysisControl/heatmap.html"
			},
			{
				"name" : "포인트집계 분석 레이어",
				"desc" : "포인트집계 분석 레이어 표출 방법",
				"path" : "/samplehtml/map/analysisControl/aggregate.html"
			},
			]
		}

	};

var sampleHtml;
function loadSample(menuName, target) {
	const nowMenu = menu.getMenu(menuName);

	$.ajax({
		url : rootpath + nowMenu.path,
		type : 'GET',
		cache : false,
		async : false, // $.get(async: true) 로 부르는 경우 clipboard.js 동작 안함
		success : function(html) {

			const iDoc = $("#sample-container")[0].contentWindow.document;
			const sampleHtml = processHtml(html, nowMenu);
			const styleLinks = appendStylesheets();

			// 기본 height 지정 '550px'
			$("#sample-container")[0].style.height = '550px';

			// Iframe 크기 조정
			const observer = new MutationObserver(async (mutations, obs) => {
				if (iDoc.body && iDoc.body.children.length > 0) {
					obs.disconnect();
					await initializeMapContainer(iDoc);
				}
			});

			observer.observe(iDoc, {
				childList: true,
				subtree: true
			});

			// Iframe 생성, 완전한 HTML 구조 필요 (about:blank 회피)
			iDoc.open();
			iDoc.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <title>${nowMenu.name}</title>
                    ${styleLinks}
                </head>
                <body>
                    ${sampleHtml}
                </body>
                </html>
            `);
			iDoc.close();


		},
		error : function(xhr, status, err) {

			console.log(xhr);
			console.log(status);
			console.log(err);
		}
	});

	updateUI(nowMenu, sampleHtml, target)
}

function containerResize(){
	var iDoc = $("#sample-container")[0].contentWindow.document;
	iDoc.body.style.overflow='auto';

	var infoArea = iDoc.body.querySelector('div.infoArea');
	$("#sample-container")[0].style.height=(infoArea.offsetTop+infoArea.offsetHeight)+'px';
	iDoc.body.style.overflow='hidden';
}

function resetClipboard() {

	var copyButton = $(".code-toolbar button:last")[0];
	var clip = new ClipboardJS(copyButton, {
		text : function() {
			return sampleHtml;
		}
	});
}

function eventShowLive() {
	var html = '<iframe src="' + rootpath + '/live"></iframe>';

	$("#showLive").off("click");
	$("#showLive").click(function() {
		var popWindow = modal.showModal("modal-live", html, sampleHtml);
	});
}

function processHtml(html, nowMenu) {
	// 아래 들여쓰기는 개발자지원센터에서 볼 때 깔끔하게 보이도록 설정한 것입니다. 수정하지 말아주세요.
	let mapOption =
	`{
		center : coord,
		zoom : 11,
		projection : 'EPSG:${sampleSrid}',
		`;
	let proxyOption = proxyUseAt === 'Y' ?
		`proxyURL: 'proxyUrl.jsp',
		proxyParam: 'url',
		`
		:
		`//proxyURL: 'proxyUrl.jsp',
		//proxyParam: 'url',
		`;
	let basemapOption = BasemapUtils.generateBasemapOption(sampleBasemap, {
		baroEMapURL: baroEMapURL,
		baroEMapAirURL: baroEMapAirURL,
		baroEMapKey: baroEMapKey,
		vWorldURL: vWorldURL,
		customConfig: customBasemapConfig
	});
	let customControlOrderOption = nowMenu.path.includes('customControlOrder') ?
		`
		controlGroup : [
				[ 'basemap', 'move', 'zoom' ],
				[ 'rotate', 'fullscreen' ],
				[ 'clear', 'draw', 'measure', 'print',
						'download', 'overviewmap',
						'dividemap', ], 
		],
	}`
		:
		`
	}`;

			mapOption += proxyOption+basemapOption+customControlOrderOption;
			html = html.replace(/::mapOpt::/gi, mapOption);
			html = html.replace('var mapOption = "{','var mapOption = {');

            html = BasemapUtils.replaceBasemapInHtml(html, sampleBasemap);

            if (nowMenu.path.includes('vWorldWFSLayer')) {
            	// vWorldWFSLayer 예제 전용 중심점
            	if(sampleSrid === '5179') {
            		html = html.replace(/::coordx::/gi, 951149.7676754193);
            		html = html.replace(/::coordy::/gi, 1947331.0940337165);
            	} else {
            		// sampleSrid === '5186'
            		html = html.replace(/::coordx::/gi, 195328.99221877696);
            		html = html.replace(/::coordy::/gi, 547167.721604856);
            	}
            } else if (nowMenu.path.includes('cropCircleLayer')) {
            	// 레이어 원형으로 자르기 예제 전용 중심점
            	if(sampleSrid === '5179') {
            		html = html.replace(/::coordx::/gi, 949229.6682336009);
            		html = html.replace(/::coordy::/gi, 1934090.6680809595);
            	} else {
            		// sampleSrid === '5186'
            		html = html.replace(/::coordx::/gi, 193478.51861949265);
            		html = html.replace(/::coordy::/gi, 533912.3877249222);
            	}
            } else if (nowMenu.path.includes('FeatureChangeWKT')) {
            	// Geometry <-> WKT 상호변환 예제 전용 중심점
            	if(sampleSrid === '5179') {
            		html = html.replace(/::coordx::/gi, 949506.7679930615);
            		html = html.replace(/::coordy::/gi, 1933677.0992435084);
            	} else {
            		// sampleSrid === '5186'
            		html = html.replace(/::coordx::/gi, 193757.90979872074);
            		html = html.replace(/::coordy::/gi, 533500.1414559416);
            	}
            } else if (nowMenu.path.includes('barChartStyle')
            		|| nowMenu.path.includes('dynamicBarChartStyle')
            		|| nowMenu.path.includes('pieChartStyle')) {
            	// 막대 차트 스타일 예제,
            	// 동적 막대 차트 스타일 예제 전용 중심점
            	if(sampleSrid === '5179') {
            		html = html.replace(/::coordx::/gi, 951017.9830328645);
            		html = html.replace(/::coordy::/gi, 1934563.074053988);
            	} else {
            		// sampleSrid === '5186'
            		html = html.replace(/::coordx::/gi, 195264.96447526355);
            		html = html.replace(/::coordy::/gi, 534394.4456313944);
            	}
            } else {
            	// 그외의 예제들 전용 중심점
            	if(sampleSrid === '5179') {
            		html = html.replace(/::coordx::/gi, 955156.7761);
            		html = html.replace(/::coordy::/gi, 1951925.0984);
            	} else {
            		// sampleSrid === '5186'
            		html = html.replace(/::coordx::/gi, 199312.9996);
            		html = html.replace(/::coordy::/gi, 551784.6924);
            	}
            }

            // vWorld WMS 예제에서 사용하는 bbox 옵션
            if(sampleSrid === '5179') {
            	html = html.replace(/::bbox::/gi, '{{minx}},{{miny}},{{maxx}},{{maxy}}');
            } else {
            	// 5186
            	html = html.replace(/::bbox::/gi, '{{miny}},{{minx}},{{maxy}},{{maxx}}');
            }
			var coordinates=[];
            // 레이어 다각형으로 자르기 예제에서 사용하는 좌표값
            if(sampleSrid === '5179') {
            	coordinates = [
            		[
            		  [949301.5734418407, 1935602.8858964627],
            		  [950628.1800753145, 1936891.316799477],
            		  [951353.5189540483, 1935240.216457096],
            		  [953911.2928948466, 1934782.1076915797],
            		  [951782.9959217197, 1933675.0115082492],
            		  [951391.694684508, 1932291.141279086],
            		  [950532.7407491653, 1933340.973866727],
            		  [947125.5568056392, 1933207.3588101182],
            		  [948824.3768110948, 1934304.9110608338],
            		  [947163.7325360989, 1935784.2206161462],
            		  [949301.5734418407, 1935602.8858964627]
            		]
            	];
            } else {
            	// 5186
            	coordinates = [
            		[
	        			[193542.4288614867, 535425.5235802046],
	        		    [194862.67175751977, 536721.4507800736],
	        		    [195597.02870956803, 535073.6093982563],
	        		    [198158.1495706008, 534628.9027694712],
	        		    [196034.96004445344, 533510.122718428],
	        		    [195650.8559023522, 532123.6832622514],
	        		    [194786.0297611553, 533169.336562987],
	        		    [191378.34667804904, 533017.6090172606],
	        		    [193071.94731997466, 534124.5573893203],
	        		    [191402.86974895213, 535595.5818267891],
	        		    [193542.4288614867, 535425.5235802046]
	        		]
          		];
            }
            html = html.replace(/::polygonCoordsSet::/gi, JSON.stringify(coordinates));

            // 레이어 멀티-다각형으로 자르기 예제에서 사용하는 좌표값
            if(sampleSrid === '5179') {
            	coordinates = [[[[1710029.3523641797,3339661.957262646], [1710023.472974333,3339649.2331998562], [1710018.8276408901,3339645.6998729687], [1710020.184373234,3339643.0930822585], [1710021.3446735158,3339640.92597478], [1710022.9614809845,3339637.848674075], [1710035.885810133,3339613.5994983832], [1710048.6123168997,3339589.666289144], [1710056.746965391,3339574.5265473267], [1710056.6309790201,3339573.9347372167], [1710056.6108633634,3339573.5914383917], [1710050.704555953,3339563.8627003287], [1710046.3734284644,3339556.6436033826], [1710044.6216296954,3339553.7316305353], [1710044.2072053691,3339552.9876248254], [1710043.9836318137,3339552.583861812], [1710039.9922415572,3339545.0490867114], [1710033.8439507969,3339533.3888472286], [1710025.8141164023,3339518.317866033], [1710020.4014391664,3339508.115802366], [1710023.8243777272,3339496.870715022], [1710029.2128367717,3339479.173526544], [1710036.4722293245,3339456.117019452], [1710037.4024269092,3339453.0788522786], [1710037.4135059635,3339452.6722615487], [1710036.4035524977,3339449.1582407756], [1710031.5661580584,3339432.2392047103], [1710030.6704532579,3339429.098207159], [1710029.2856064518,3339424.308413592], [1710028.86254291,3339422.847469393], [1710028.7320844466,3339422.3806669246], [1710028.3583013664,3339421.105879496], [1710028.0485768542,3339420.017070748], [1710027.9352258001,3339419.6439367677], [1710026.957344547,3339416.22230551], [1710025.882373796,3339412.4593016235], [1710024.8731774483,3339409.0080877063], [1710023.585824284,3339404.435727285], [1710023.1617502468,3339402.973796569], [1710022.779994412,3339397.14229019], [1710022.6631993772,3339395.2402394237], [1710022.4524478072,3339391.997113198], [1710021.1625084346,3339371.695987547], [1710021.2752911188,3339370.695962065], [1710021.632918829,3339369.287527562], [1710023.2428481765,3339362.9956035605], [1710028.9367225803,3339340.799838934], [1710033.0955686518,3339324.64742672], [1710034.980526435,3339317.8827158296], [1710035.0099885818,3339317.758526175], [1710035.483864812,3339316.971958627], [1710039.423432832,3339312.3996285703], [1710042.040034003,3339309.3411402656], [1710065.4269563872,3339281.8143081563], [1710065.8908832856,3339281.5280925394], [1710069.839944514,3339275.083249189], [1710071.6608567603,3339272.1282240422], [1710073.6801863895,3339268.7324933303], [1710075.4385441872,3339265.7163047064], [1710078.2667335654,3339260.906702406], [1710080.5301743443,3339257.0416530697], [1710086.156841983,3339247.5176800415], [1710087.8692884976,3339244.594920773], [1710090.8068098258,3339239.659167979], [1710094.2769779246,3339233.750287894], [1710095.5464915219,3339231.6128244204], [1710095.607148338,3339231.4253429156], [1710096.9489372177,3339224.917943579], [1710097.3773027805,3339222.9154492742], [1710100.105954923,3339209.837191632], [1710103.4729774613,3339193.942992377], [1710107.089095596,3339176.766500365], [1710110.0587074659,3339162.8744228045], [1710120.2922625728,3339117.3432559613], [1710127.27566553,3339086.8631046005], [1710129.6643455946,3339076.5671346756], [1710130.4331329311,3339073.0625394452], [1710137.0365044903,3339038.3106803587], [1710136.7804732586,3339037.7824750636], [1710136.6049736864,3339037.4730059407], [1710132.2988208607,3339034.903395], [1710133.1819744648,3339029.2123402767], [1710139.6293709958,3338987.9721303415], [1710147.6672638273,3338934.914615602], [1710148.1453867182,3338931.819980235], [1710150.6415610984,3338917.5900692586], [1710152.0004598703,3338911.1762403445], [1710152.8870585468,3338907.0142022967], [1710152.9646083985,3338905.7339766147], [1710153.1464438164,3338902.673870054], [1710152.002523812,3338881.560073648], [1710158.3034208699,3338855.3623431344], [1710159.788640665,3338849.0719255903], [1710162.129805954,3338834.906799232], [1710164.9259205484,3338818.301343551], [1710167.5776528667,3338802.634476397], [1710169.9820991901,3338788.4995467337], [1710171.7861202122,3338777.5864579226], [1710174.054659239,3338763.858524651], [1710174.1115504103,3338763.358506152], [1710174.6526029753,3338751.305278484], [1710175.0946572977,3338741.3132919604], [1710175.2949147474,3338739.7820212375], [1710176.633460014,3338733.0878133746], [1710177.9870925187,3338726.2367330734], [1710179.5761946945,3338718.290275022], [1710181.7838396886,3338707.2165316157], [1710182.3286462002,3338704.525652902], [1710182.5940592107,3338703.1802478395], [1710182.972500464,3338700.9296881757], [1710183.781721508,3338696.893416453], [1710187.1703170212,3338680.2190063824], [1710191.1471684643,3338660.5724694463], [1710193.2911498584,3338649.4365777588], [1710196.672500533,3338632.16904815], [1710200.862154265,3338610.7723825434], [1710203.1546596964,3338599.0414942577], [1710205.2110494701,3338588.34407425], [1710207.2829711374,3338576.366277354], [1710208.0842996172,3338571.6748764785], [1710208.6969480144,3338568.1412054542], [1710211.4532879721,3338552.129540089], [1710212.6036094804,3338545.3128678673], [1710214.6264114147,3338534.3971452136], [1710218.1597086731,3338515.5668718494], [1710222.415461469,3338493.1078302483], [1710224.9979620813,3338479.5020491816], [1710229.2806631238,3338456.7929172907], [1710233.8164694207,3338433.051211697], [1710239.9428777741,3338401.3958889055], [1710242.4126839866,3338388.7891339133], [1710247.310715381,3338362.702192897], [1710249.0279377932,3338353.6936107758], [1710250.495117641,3338345.905414936], [1710252.9705059642,3338332.5185354697], [1710260.9438647209,3338289.699048741], [1710266.1871281648,3338261.1742021497], [1710270.3504651734,3338246.6435879646], [1710271.4596428303,3338242.8853277685], [1710280.0612785323,3338221.15389017], [1710292.4314843914,3338189.766090567], [1710292.8036800285,3338189.574855865], [1710293.5937071247,3338183.853968492], [1710295.5758558896,3338168.320010448], [1710296.936056465,3338158.192232587], [1710298.053732906,3338149.9089162247], [1710299.543441039,3338138.8428297862], [1710299.9153847946,3338136.0610593935], [1710300.170195406,3338127.4445978766], [1710300.6096693403,3338113.3332503834], [1710301.2427036588,3338093.32155139], [1710301.362622541,3338092.9136497052], [1710314.3709852346,3338073.064639005], [1710326.0795008256,3338055.322395512], [1710331.3641390991,3338047.0828519724], [1710334.03037277,3338043.08712602], [1710350.122053714,3338018.7069760887], [1710396.2081560832,3337947.9978934163], [1710475.7663179617,3337825.578044696], [1710476.096287822,3337824.451569669], [1710476.2296809836,3337823.9186727097], [1710476.3860757682,3337823.8858299498], [1710488.0470228978,3337815.225933316], [1710498.5952613614,3337807.453374129], [1710511.5556100244,3337797.810217281], [1710520.6188418642,3337791.1160316085], [1710527.104272319,3337786.7307569603], [1710556.6849534432,3337768.1803106987], [1710588.4962493095,3337748.2618866134], [1710603.4414136163,3337738.9064049944], [1710617.755369193,3337728.966319828], [1710632.3522712167,3337719.210675397], [1710646.7036267538,3337711.048762997], [1710649.5861512201,3337709.422165022], [1710652.3162879883,3337708.078127603], [1710712.5403521112,3337678.9547694167], [1710743.4038621308,3337660.607589005], [1710782.175154952,3337634.331919834], [1710788.889784445,3337630.8177197007], [1710863.7159918086,3337614.376371852], [1710879.5495191372,3337606.2276686095], [1710885.0070422883,3337605.8812974785], [1710904.8874561568,3337604.7374089593], [1710919.6887623456,3337604.0909247342], [1710917.8614427114,3337601.3666112986], [1710912.3162452271,3337593.100558254], [1710909.1865218785,3337588.457521749], [1710905.6589470995,3337583.1941141533], [1710904.6096856066,3337581.646840699], [1710902.1163067394,3337577.931886638], [1710898.3181410008,3337572.2661810545], [1710896.3641300125,3337569.387601983], [1710892.3758622378,3337563.4436615687], [1710890.7558939247,3337561.0284332186], [1710888.0867640255,3337557.065930103], [1710885.7348138369,3337553.5679795505], [1710880.7297101328,3337546.107333276], [1710875.5180421146,3337538.3365933215], [1710872.0711394064,3337533.229003527], [1710867.2724871892,3337526.077353233], [1710864.1264029904,3337521.4025557614], [1710861.171047728,3337516.975126688], [1710856.7221871214,3337510.350651618], [1710852.7503688466,3337504.4374707425], [1710848.6984490338,3337498.4323789384], [1710844.9326105881,3337492.7973412294], [1710842.4222561014,3337489.0824909573], [1710839.1333721625,3337484.159747174], [1710836.8621938745,3337480.817613263], [1710831.8089298697,3337473.2636713022], [1710827.8047863524,3337467.3199206754], [1710825.6605605932,3337464.1620088704], [1710823.1357347276,3337460.572265589], [1710818.3407171913,3337453.639377578], [1710800.7189736539,3337427.2930743084], [1710783.3041682127,3337401.2878170093], [1710771.9237580686,3337384.0421547145], [1710757.9265591954,3337363.2078552973], [1710742.8150183547,3337340.638216841], [1710739.3350335998,3337335.4371484863], [1710735.029600047,3337329.029650127], [1710730.0558254528,3337321.5995807587], [1710725.2418256663,3337314.417252727], [1710719.5364823504,3337305.8723981176], [1710716.073219905,3337300.7330450728], [1710714.039063998,3337297.667680461], [1710710.5441017246,3337292.4667919613], [1710709.018612021,3337290.1762555735], [1710708.0333764935,3337288.721085499], [1710706.0305596553,3337285.6873005484], [1710704.9986464586,3337284.1707755327], [1710700.8829316131,3337278.009658453], [1710696.4814761668,3337271.4155668793], [1710692.7794228012,3337265.9354493637], [1710688.2355727525,3337259.2182398057], [1710685.2329156445,3337254.7604189636], [1710679.100910091,3337245.6905115675], [1710675.1761563737,3337239.870634219], [1710664.7696591662,3337224.3917551315], [1710659.9705382083,3337217.209246074], [1710655.2676273326,3337210.212328258], [1710643.5749374095,3337192.908502124], [1710637.5208050895,3337183.836656549], [1710628.194851228,3337168.6882996894], [1710624.773636664,3337163.1429803856], [1710621.6085832708,3337158.2197395135], [1710619.654056746,3337155.215331802], [1710617.1720370222,3337151.2825279105], [1710613.9733833894,3337146.139985364], [1710610.072592731,3337139.664693705], [1710607.47608118,3337135.2638963214], [1710605.4693680136,3337131.823700516], [1710603.2212751238,3337127.9190371474], [1710600.622514389,3337123.3315164014], [1710599.1706399554,3337120.602677039], [1710597.2869976063,3337117.099081397], [1710596.344940078,3337115.331807105], [1710595.5783807668,3337113.874002232], [1710593.4860248403,3337109.8735882174], [1710590.8975993847,3337104.817568942], [1710588.707617844,3337100.5057487143], [1710586.8682719027,3337096.782911719], [1710585.2366618759,3337093.401113081], [1710584.260731103,3337091.383581838], [1710582.9959851948,3337088.7773212995], [1710582.37220553,3337087.4745868435], [1710581.9716325146,3337086.6365388706], [1710579.0902222078,3337080.5544250505], [1710577.8389183814,3337077.72929491], [1710576.2669674645,3337074.1600271664], [1710574.098919257,3337069.0989437634], [1710573.0387291582,3337066.553134972], [1710571.8173897932,3337063.5708534247], [1710570.659586099,3337060.714636974], [1710569.951555301,3337058.975502071], [1710569.2428665948,3337057.173459194], [1710567.9402748793,3337053.9125301545], [1710566.7820863375,3337051.0243610227], [1710566.1544421501,3337049.409090898], [1710565.6868248028,3337048.228309717], [1710563.49690841,3337042.512465168], [1710561.4628921119,3337036.888517281], [1710560.4615913942,3337034.09133409], [1710557.6327388254,3337025.980298046], [1710555.5113122289,3337019.483571086], [1710554.08356361,3337014.9438609546], [1710553.3370613968,3337012.5810237303], [1710552.7519706115,3337010.684715933], [1710552.069978524,3337008.4459347515], [1710551.5016086022,3337006.611242897], [1710550.5900433674,3337003.4704365786], [1710550.0204227741,3337001.449009423], [1710549.7104510171,3337000.4221209595], [1710548.682914604,3336996.6905032224], [1710548.4220024396,3336995.756898308], [1710547.1474010907,3336990.9976343233], [1710545.6320027409,3336985.6481637284], [1710544.9309276226,3336983.1599461026], [1710544.2429217342,3336980.4219047897], [1710542.8142386577,3336974.478082709], [1710541.858933929,3336970.2762222444], [1710541.49628271,3336968.689717233], [1710539.648340585,3336960.3482571538], [1710510.1870990302,3336953.804956639], [1710497.1715808935,3336951.0594157754], [1710484.4365467825,3336948.310496154], [1710465.6797191107,3336944.3227651017], [1710445.796275488,3336940.067979506], [1710434.5788829713,3336937.706336343], [1710428.524763784,3336936.436955616], [1710426.6013500565,3336936.0237074858], [1710424.6453568074,3336935.6417105417], [1710420.5476036426,3336934.7852808423], [1710387.0092641264,3336927.948445522], [1710373.2439749008,3336925.0561426855], [1710345.7895439444,3336919.3005791707], [1710333.2442043382,3336916.7051644856], [1710324.4206588631,3336914.8449763795], [1710317.3680178798,3336913.587622689], [1710315.6958694456,3336913.3900554692], [1710299.742512525,3336911.584690761], [1710293.5703483317,3336910.87897985], [1710261.224397654,3336892.823848649], [1710266.4194790977,3336908.2715462833], [1710279.3634636537,3336923.2514046016], [1710293.8910741755,3336940.1167415166], [1710295.3629731564,3336941.8466763254], [1710295.1766226587,3336941.8798796474], [1710293.9151296217,3336942.1137827663], [1710255.6760164592,3336949.126938112], [1710237.0673942314,3336956.2166157747], [1710224.261964912,3336961.801308578], [1710219.2865489987,3336965.9496776443], [1710206.2049266095,3336974.5636547864], [1710204.2232925897,3336975.8668154962], [1710188.8144643323,3337003.7968557365], [1710187.056762543,3337005.533736118], [1710186.5167813685,3337004.729323972], [1710180.695719616,3337011.134778177], [1710160.9930000668,3337032.280676623], [1710157.3275113576,3337036.3193933032], [1710154.1709736763,3337039.8217876945], [1710154.0786161963,3337039.947733432], [1710145.4091622399,3337049.5701475055], [1710144.9003242948,3337050.107567046], [1710145.1067846017,3337055.565784754], [1710140.739435575,3337059.612956179], [1710139.165088174,3337061.0679019783], [1710138.517203034,3337061.66891375], [1710130.5095462524,3337069.1922502243], [1710130.0305318302,3337069.635435965], [1710123.627530701,3337075.6415444217], [1710121.7919550873,3337077.380262129], [1710120.1248665024,3337078.9293009685], [1710117.406121985,3337078.6493681166], [1710092.4079666417,3337076.017109238], [1710082.205864631,3337074.953386668], [1710080.7223722707,3337076.1576716322], [1710075.4023506856,3337076.2216555816], [1710071.6170360954,3337078.0148182223], [1710063.1024658338,3337082.3305549375], [1710055.9527200449,3337086.03674365], [1710051.4383756747,3337088.2441465682], [1710053.8393284706,3337090.587056916], [1710046.073307818,3337093.6135871224], [1710045.5766813848,3337093.8382772496], [1710027.2351618398,3337101.0494786436], [1710025.9151397336,3337101.5647124243], [1710027.1249599275,3337106.168969649], [1710026.0690397501,3337106.5560889933], [1710017.5071095326,3337109.5932056475], [1710009.3039876935,3337112.5629844144], [1710000.6030099816,3337115.663693495], [1709992.2451373371,3337118.728212497], [1709981.058006676,3337122.70164398], [1709979.0842172666,3337123.412502284], [1709970.1921037268,3337126.140914417], [1709961.485855902,3337128.804171753], [1709952.6715280802,3337131.5317469207], [1709943.9668953577,3337134.3208169085], [1709939.8160982123,3337135.6190498304], [1709938.7862011353,3337139.501287508], [1709937.0081515242,3337146.1068123407], [1709936.2326394988,3337145.1494451603], [1709931.2825452588,3337147.486931347], [1709927.6373956357,3337149.309365204], [1709933.9228851441,3337159.439021161], [1709933.1095149997,3337163.1319000884], [1709930.7686174717,3337176.0796439983], [1709930.328877807,3337178.456773445], [1709926.3747927889,3337200.0064878743], [1709926.0991814304,3337201.7574738623], [1709925.8835685276,3337203.257970675], [1709928.8878395676,3337205.1882549278], [1709927.043846481,3337212.794239478], [1709925.103698906,3337220.119758484], [1709922.8334085967,3337229.884002867], [1709920.6821722696,3337239.0855627595], [1709919.4360374906,3337244.499263894], [1709918.670432813,3337249.5955926576], [1709913.879985638,3337271.6856751153], [1709909.8431537177,3337268.176954112], [1709902.5260484081,3337277.314811498], [1709898.9494417417,3337282.257253033], [1709898.0896522864,3337283.4230671506], [1709896.8754195133,3337284.935693511], [1709894.925300565,3337287.611542917], [1709891.164604636,3337292.774910108], [1709887.23507028,3337298.1899778275], [1709885.0397127261,3337301.150505317], [1709883.4120931802,3337303.3231367716], [1709882.8895161427,3337304.0464742947], [1709874.631819814,3337315.4436613144], [1709868.415787865,3337324.0379643855], [1709857.871494207,3337338.614388402], [1709855.5383390863,3337341.8251442653], [1709844.0952164158,3337358.2229788927], [1709841.3337314827,3337362.1879944466], [1709840.3971278658,3337367.348301491], [1709837.6459587177,3337382.548989592], [1709837.1639529932,3337385.23921148], [1709834.9974836009,3337397.155332777], [1709832.522979022,3337396.3421672285], [1709823.8266820353,3337393.3569521196], [1709822.885942977,3337393.0257420074], [1709830.9328676364,3337409.4377614474], [1709834.7203063439,3337410.3908028975], [1709844.7660875213,3337417.8847189345], [1709851.4297718192,3337423.17237739], [1709853.0942794022,3337425.3054508637], [1709858.1837990764,3337430.736805668], [1709869.75330368,3337443.0189469247], [1709869.832691943,3337443.142823875], [1709887.4613546594,3337464.83830488], [1709889.1092185692,3337468.2508702064], [1709889.2049680976,3337468.406507379], [1709877.36419274,3337482.873719002], [1709869.153257926,3337486.5296825804], [1709850.8691933681,3337494.614141525], [1709834.0053406868,3337501.464117839], [1709811.5893814354,3337509.7549440106], [1709803.5775181642,3337510.5374438064], [1709798.256138496,3337513.067156251], [1709796.4651751583,3337513.369356592], [1709793.522280823,3337513.873083245], [1709793.2414434985,3337513.938383713], [1709773.9703661657,3337517.759443052], [1709768.8713572659,3337519.349727588], [1709763.2626270964,3337523.9740099516], [1709753.1732587474,3337525.8111622417], [1709739.491513634,3337531.2815976297], [1709736.2993667568,3337529.1669268683], [1709728.173459703,3337533.4150757906], [1709722.514034821,3337537.7274865406], [1709709.6618818413,3337542.095387294], [1709699.2343466058,3337545.622263479], [1709690.7185532455,3337548.502042807], [1709689.2257863302,3337549.0193590634], [1709681.9531604133,3337551.4786038743], [1709680.3679038314,3337552.0279930104], [1709672.9392676759,3337554.5520333145], [1709671.9445107956,3337554.8765995447], [1709671.5886637191,3337555.0985956863], [1709664.479889589,3337559.5533003276], [1709663.226164723,3337560.3492611595], [1709662.2187995615,3337560.9536060086], [1709655.1730683986,3337562.8489668593], [1709653.710750482,3337563.241083017], [1709625.4177757134,3337570.789698247], [1709608.4482206358,3337575.332035008], [1709607.49917282,3337574.219967582], [1709594.2061533232,3337578.2187807905], [1709590.396716501,3337579.419031142], [1709586.856519387,3337580.8347461317], [1709565.062871185,3337589.055266139], [1709555.7896259786,3337592.568338534], [1709543.0882640774,3337595.1867578807], [1709543.2794985753,3337595.5589542883], [1709543.8567934167,3337598.1425421834], [1709544.515825883,3337601.03672953], [1709533.234445988,3337602.326892917], [1709523.2915587192,3337603.383222415], [1709517.1983455336,3337604.0177768976], [1709516.614853817,3337602.2461799076], [1709504.4051353391,3337604.234609809], [1709497.5207103884,3337605.253196032], [1709474.1550304708,3337608.655112137], [1709461.9970466006,3337609.707167838], [1709457.6013089994,3337610.07160653], [1709451.2254928714,3337610.6166895055], [1709450.507928194,3337610.6562925186], [1709444.70601862,3337610.850920248], [1709441.4300987609,3337610.984259177], [1709440.7750680805,3337610.9921501707], [1709435.2805594215,3337610.8395331632], [1709434.9999746312,3337610.842913299], [1709430.5853650724,3337610.958911187], [1709424.0838886998,3337616.59172065], [1709423.9296216192,3337616.7183126165], [1709422.3253806708,3337617.0182640487], [1709417.8469872964,3337616.9793381356], [1709415.771671561,3337616.972281753], [1709406.1423810348,3337616.8694755174], [1709394.565609084,3337617.008738395], [1709390.5741654322,3337622.4245597133], [1709390.2057460183,3337622.929330341], [1709389.6983982204,3337623.5904681855], [1709389.437915703,3337623.936149074], [1709389.2837820179,3337624.1567140454], [1709387.7329293157,3337626.2356476546], [1709386.1999140857,3337628.311370393], [1709388.9084796794,3337631.725142072], [1709390.7635832324,3337634.019702377], [1709387.7356507862,3337635.5141337886], [1709378.989307292,3337639.8236892885], [1709368.2608345994,3337645.3954708856], [1709368.2438356602,3337645.3936783355], [1709366.8519415457,3337646.7406712547], [1709366.1418948807,3337647.4043508964], [1709365.6176939267,3337647.9099995703], [1709362.3928579157,3337650.8828336857], [1709357.0840546298,3337655.783234545], [1709352.4100787514,3337660.333345033], [1709349.3879440015,3337663.3038372574], [1709347.1356218304,3337665.5149565516], [1709340.719559839,3337671.7719044816], [1709340.41178589,3337672.088195119], [1709339.258429085,3337673.5061160866], [1709328.3408470363,3337686.9316011653], [1709325.3427119418,3337690.6508052126], [1709323.6669308743,3337692.730145763], [1709323.1748148538,3337693.3292827513], [1709318.8555828892,3337698.7180955946], [1709312.99673016,3337705.9659006633], [1709308.2382687621,3337711.204212957], [1709307.0378504023,3337712.5289261686], [1709306.9158016685,3337712.842979432], [1709306.066797386,3337714.912359996], [1709303.8108991305,3337720.557041788], [1709302.2154551852,3337724.165373441], [1709296.2777718073,3337737.71871724], [1709295.411673726,3337739.6955276034], [1709285.9879766647,3337747.5484292023], [1709277.6606453063,3337754.483530007], [1709276.4089164704,3337755.528136068], [1709275.0695950217,3337757.0113136624], [1709268.4004148296,3337764.2998407967], [1709262.702290771,3337770.5481419354], [1709260.192132568,3337773.324618156], [1709256.0653484,3337777.868238425], [1709247.3184803154,3337787.522673253], [1709242.7416091533,3337789.762796254], [1709234.2728295801,3337793.984124964], [1709232.4428249719,3337794.941922857], [1709225.9143937468,3337798.32905782], [1709221.7271870032,3337800.5015706066], [1709221.5457200415,3337800.9401745647], [1709196.9255444133,3337805.075155979], [1709189.777159165,3337811.4647647133], [1709185.8493216992,3337815.6944048107], [1709156.6815713053,3337799.8479985343], [1709149.1675851662,3337802.997340096], [1709149.0108170584,3337802.9992286973], [1709143.3682124084,3337804.72089988], [1709133.9363734294,3337809.328332102], [1709125.4348815351,3337813.4871399207], [1709117.1053893957,3337817.5820582258], [1709114.571184028,3337818.3624878787], [1709114.1051262044,3337818.554853445], [1709104.8818105636,3337822.316898269], [1709104.5707765105,3337822.4454788314], [1709095.8095715265,3337827.0138737606], [1709093.5749239307,3337828.0394628732], [1709093.0779239042,3337828.233199889], [1709070.035718376,3337836.4368213853], [1709053.9883458456,3337842.341129834], [1709052.1243790817,3337843.04967032], [1709036.8159613432,3337857.247004929], [1709017.6468768378,3337867.057862567], [1709017.4762616088,3337867.1537928144], [1709015.072116023,3337868.3680752963], [1709008.4963788248,3337871.724823671], [1709006.2322791605,3337872.8755016085], [1708995.497521014,3337878.0918416632], [1708995.0906457617,3337876.722575926], [1708978.3036690205,3337880.920184383], [1708976.6730425097,3337882.843290589], [1708964.923454392,3337891.940598031], [1708948.1544056498,3337888.4912878554], [1708924.5753668183,3337898.7305755913], [1708919.9685348999,3337903.623524948], [1708916.397468456,3337909.0342891426], [1708910.1203341663,3337906.0828477796], [1708902.2984719789,3337903.149017412], [1708900.1242898626,3337902.644917432], [1708895.9044724398,3337902.1024457715], [1708895.907335684,3337902.0914259236], [1708901.1179442003,3337879.150457696], [1708906.2062757926,3337848.8697518446], [1708914.3325815015,3337831.033805131], [1708915.7489453326,3337823.917308538], [1708920.2221770845,3337801.443614265], [1708926.258534808,3337782.725955872], [1708932.2747851214,3337762.330877304], [1708941.6617564675,3337736.9797470407], [1708944.239464121,3337730.020032849], [1708947.577995909,3337719.474023246], [1708947.7318653772,3337718.9828221397], [1708949.7713684957,3337712.526928629], [1708950.7056292691,3337709.5845823186], [1708924.5637127976,3337721.374709332], [1708900.4770499286,3337732.244272459], [1708865.4793872877,3337748.05067355], [1708824.5246585472,3337766.675079895], [1708795.1135505908,3337783.7255358538], [1708737.7939417083,3337816.9654750396], [1708727.9444460077,3337808.9388906155], [1708720.3131072742,3337799.761182526], [1708710.3380825263,3337787.7723934394], [1708709.395705264,3337787.2224944243], [1708708.4686787624,3337786.7033692077], [1708695.9325751467,3337779.738776777], [1708686.819052979,3337770.5478648907], [1708685.6972584815,3337769.437876717], [1708686.7406455942,3337767.9273038646], [1708678.712090116,3337762.094826983], [1708667.8978963424,3337756.888118954], [1708665.9714558257,3337756.224242512], [1708662.863647425,3337753.4843840385], [1708644.3721701603,3337750.8047182863], [1708642.3290226562,3337750.0693449136], [1708642.3062551096,3337748.171149644], [1708639.2413043547,3337746.841793959], [1708635.2743780725,3337745.8210074776], [1708635.209413338,3337746.2312443485], [1708634.9806131918,3337747.9716845676], [1708634.9353783545,3337748.3617103808], [1708616.3867338677,3337743.921080901], [1708600.2718411777,3337740.0604198202], [1708597.2932954181,3337739.3462018007], [1708598.63143045,3337736.7695940556], [1708589.38925526,3337733.238687896], [1708572.9485363474,3337727.4415374957], [1708567.199131413,3337726.10956733], [1708552.7536622393,3337721.6395791415], [1708549.335862288,3337720.60708378], [1708547.0607319642,3337719.9304302544], [1708531.6896305974,3337715.3058135505], [1708527.3942482723,3337714.12809781], [1708525.6132668252,3337713.6012836667], [1708508.5681458865,3337708.375660948], [1708496.434630675,3337704.73767409], [1708478.341600264,3337699.1551670586], [1708471.4065899276,3337696.9706341154], [1708450.213000842,3337689.9133931166], [1708446.301963049,3337688.8150348924], [1708409.1283425926,3337680.8025300964], [1708392.3891000464,3337678.00497986], [1708391.4457712222,3337677.956423747], [1708390.8982549158,3337678.018945023], [1708394.0159058238,3337680.4151446954], [1708405.9218000602,3337694.5957315024], [1708410.78394021,3337700.624047923], [1708417.875181775,3337711.6488173474], [1708424.1518497942,3337727.4270787397], [1708430.0764959147,3337742.5843136893], [1708430.4539618655,3337742.830432396], [1708431.082729227,3337744.5385715626], [1708433.0266058608,3337749.2278743098], [1708432.6688487031,3337753.195902615], [1708430.4200692216,3337776.2850639527], [1708430.469957295,3337777.939257993], [1708431.587283954,3337802.0804123734], [1708432.9983677554,3337807.805661683], [1708436.4432031144,3337820.466236531], [1708442.8439295054,3337831.031945346], [1708443.0344066992,3337831.341235474], [1708444.4713713978,3337835.318601421], [1708464.3989361478,3337847.1246808735], [1708518.3940800205,3337847.505721473], [1708544.2776140473,3337883.5515468013], [1708582.8823674722,3337950.995962266], [1708583.8243882107,3337956.664044222], [1708589.5698723563,3337980.2511984184], [1708590.4193103136,3337991.2571830433], [1708590.553148037,3337992.0045724157], [1708593.4769714337,3338006.7615335905], [1708592.9192468557,3338030.455483555], [1708582.384962574,3338048.4333046693], [1708569.7000337704,3338060.226313561], [1708557.4201448078,3338071.9844832295], [1708532.2643762105,3338082.1479084636], [1708527.6695141897,3338084.138586341], [1708507.6271042428,3338082.2885357672], [1708491.0525825978,3338067.7897047484], [1708467.7364721186,3338036.299840846], [1708451.453672345,3338025.2917367048], [1708434.4773908458,3338024.122787566], [1708409.7500921225,3338033.627021782], [1708396.8059662946,3338051.1034883903], [1708390.6427890474,3338065.345662629], [1708377.3324344927,3338065.412943336], [1708353.2055430245,3338058.587792579], [1708322.8299721251,3338042.287545068], [1708308.9609847073,3338030.8759561125], [1708300.4084997657,3338024.2378719626], [1708281.600706356,3338004.3970898623], [1708264.5977989277,3337999.609281089], [1708253.8646496136,3338005.043320955], [1708250.9802352178,3338006.514157611], [1708235.784569974,3338019.710491305], [1708228.9594438295,3338033.461309382], [1708230.5529941781,3338058.2528625075], [1708227.0170169692,3338080.859206764], [1708211.2606387485,3338105.7965687616], [1708210.2162707157,3338109.8037301553], [1708207.5599431214,3338119.853289346], [1708210.2203216376,3338142.509917359], [1708210.6503486095,3338143.222780129], [1708231.0861878325,3338176.5258881757], [1708231.5162028093,3338177.237752366], [1708240.0067993891,3338189.0256220317], [1708246.1498030522,3338195.100522124], [1708273.821844929,3338216.362668913], [1708285.161455362,3338225.0593845695], [1708293.4348194217,3338235.7263660296], [1708311.986987161,3338246.02004187], [1708327.502689124,3338245.1473244075], [1708343.3702697875,3338239.744397181], [1708350.9263239894,3338227.138236088], [1708356.9536090824,3338215.799829269], [1708357.055675354,3338215.236348656], [1708361.7740331793,3338189.80860357], [1708358.0560949764,3338167.3205160983], [1708376.6530586516,3338158.014964493], [1708399.4352709358,3338165.886937379], [1708405.9895597855,3338180.1628473364], [1708403.428196439,3338194.2068355875], [1708397.0398987276,3338206.6730936384], [1708392.8607409117,3338214.744656406], [1708392.4341540919,3338215.560715436], [1708386.851430588,3338223.6790484944], [1708389.3286719657,3338238.909771215], [1708400.7273810678,3338251.193991488], [1708406.0538266094,3338256.8092521187], [1708408.5015823566,3338259.2145178625], [1708423.8741753136,3338273.759883664], [1708408.8578755185,3338284.9247645577], [1708397.4231729861,3338309.9050310897], [1708383.3525770989,3338327.0516352104], [1708362.1018490852,3338340.2279516323], [1708346.3198241582,3338337.140261105], [1708329.9960700637,3338327.8813270433], [1708311.3323046467,3338310.87803902], [1708311.0633165922,3338310.6006536228], [1708287.8689201698,3338286.662160294], [1708268.893806344,3338282.709041661], [1708249.5572527172,3338286.4050941356], [1708236.5513484497,3338284.4713728516], [1708220.7061414383,3338276.048548038], [1708212.5957293045,3338271.214730155], [1708212.359211286,3338271.062785878], [1708195.4013107303,3338260.9676111024], [1708188.7454462484,3338262.888248815], [1708183.757210618,3338265.8823386817], [1708174.5374726227,3338273.76386767], [1708168.6994562158,3338276.2678621793], [1708158.517084426,3338280.6666432675], [1708152.9535580818,3338281.326782208], [1708145.7196886288,3338280.5390004897], [1708135.0011858153,3338279.3886400713], [1708129.4409016487,3338275.0874473765], [1708127.1383079167,3338270.4653813606], [1708126.3162334901,3338257.835219549], [1708128.8387087116,3338247.037269331], [1708133.4848596486,3338235.029410865], [1708134.9988912288,3338218.1896731975], [1708132.212651293,3338204.491629127], [1708128.3678445602,3338194.956710768], [1708113.7013898338,3338176.845663583], [1708106.299462946,3338169.975108509], [1708090.332627126,3338160.4921568725], [1708078.3980952594,3338155.5496136337], [1708052.8804227295,3338147.4298638455], [1708031.8945838406,3338144.936051026], [1708027.8908932055,3338145.5144799813], [1708024.2928576646,3338146.0262040775], [1708014.9535492365,3338151.8499156213], [1708009.9608164784,3338159.618601742], [1708007.020584688,3338165.5830297414], [1708006.1197442617,3338169.807272348], [1708006.9667747347,3338175.3825301686], [1708009.9306373536,3338186.988320973], [1708014.472737289,3338194.8920018217], [1708016.187583011,3338202.454244569], [1708016.1886820197,3338209.1022788994], [1708014.1891485883,3338211.4982104907], [1708010.282385197,3338216.2259455854], [1708005.7241626987,3338219.9319004016], [1708001.502058451,3338223.1045105867], [1707993.038830981,3338226.544828525], [1707984.9801385324,3338229.8254789338], [1707977.0619865865,3338233.166453654], [1707974.6864090327,3338234.099769525], [1707971.9093767118,3338236.691819898], [1707969.7173349054,3338238.6845111037], [1707967.8270830992,3338245.0117901545], [1707968.2594988386,3338251.1533961166], [1707972.1015852676,3338260.47063989], [1707972.9769907307,3338261.9271397823], [1707977.808063611,3338267.9538337137], [1707984.3796413601,3338275.864022333], [1707988.6069448488,3338280.931377207], [1707992.213408527,3338286.3496545143], [1707996.0899187755,3338293.2936423877], [1707996.2778101126,3338306.3359095193], [1707996.3265876702,3338310.392819181], [1707992.6797614014,3338321.2033200692], [1707987.738443924,3338335.9001632235], [1707979.703459052,3338346.296055576], [1707969.971232915,3338354.49524875], [1707966.6492709275,3338357.2816152032], [1707960.6460144452,3338360.1621037396], [1707955.7606011154,3338362.6556218043], [1707945.9331214477,3338370.732128163], [1707940.0876636207,3338379.0095277834], [1707936.6134939324,3338392.4713175786], [1707936.194031817,3338402.930352923], [1707936.9444535705,3338405.7006114023], [1707942.4533897885,3338421.331239959], [1707948.8306285776,3338428.6196996886], [1707951.1291377835,3338431.6509341304], [1707955.5064168824,3338433.657454118], [1707955.7102375706,3338433.7479746565], [1707964.7177005066,3338437.946816029], [1707969.083433913,3338438.986762141], [1707970.5339063024,3338438.9373291093], [1707988.7827354518,3338438.218428022], [1707996.6534224865,3338441.3074610676], [1708000.7539426112,3338447.530804891], [1708003.979460038,3338457.478545779], [1708003.839638144,3338466.6868715696], [1708000.2975155385,3338471.847633195], [1707995.5459416294,3338475.0885423855], [1707960.519224659,3338483.2486510654], [1707958.434207428,3338483.680230779], [1707951.1645071087,3338485.140790215], [1707946.7975215623,3338489.218945301], [1707944.766842457,3338492.925509186], [1707943.7287845202,3338498.7741458323], [1707944.2070272379,3338504.8233222985], [1707945.6255393676,3338511.1726553678], [1707948.3533397345,3338517.3505209303], [1707949.7162191523,3338520.392029145], [1707962.9857908944,3338541.672706045], [1708006.5045596447,3338583.0928249555], [1708002.3177357307,3338602.1487641213], [1707988.118159581,3338613.8041571155], [1707983.447852964,3338619.977183339], [1707975.849934116,3338651.09002441], [1707957.9378269012,3338677.144938456], [1707935.322125962,3338700.136680262], [1707897.1835770393,3338725.8128303997], [1707888.592781316,3338731.5955783166], [1707868.9168818663,3338752.4916471727], [1707857.2230285266,3338764.897916672], [1707852.4425570786,3338766.9837158658], [1707846.7041410503,3338767.302424863], [1707825.5058767782,3338760.1923435153], [1707789.6535515434,3338733.441203625], [1707788.3735990245,3338743.7558167186], [1707786.3624079695,3338759.5398720857], [1707786.0976069646,3338760.8533186167], [1707774.7690665016,3338815.3237946583], [1707775.0859601744,3338815.4328262275], [1707776.688920537,3338816.0208039787], [1707777.7044096554,3338816.5877974685], [1707800.56376635,3338829.8645281103], [1707820.446071998,3338841.3995939987], [1707828.5589368874,3338845.8557804795], [1707847.8495640506,3338856.3493700363], [1707866.7812220347,3338866.8971176455], [1707867.0929306017,3338867.0731225642], [1707873.4767809608,3338870.591417405], [1707868.6723386324,3338882.9628147217], [1707863.039282828,3338899.5184396915], [1707861.487505767,3338909.1542352117], [1707857.54172438,3338930.6430366524], [1707859.1826321667,3338948.3295951467], [1707860.7906022226,3338971.5790458904], [1707862.4568979293,3338991.3724933313], [1707879.1545560895,3339046.976711192], [1707890.784583121,3339086.4537380068], [1707904.056894112,3339132.102632004], [1707905.7203694354,3339137.555302461], [1707905.7234853317,3339137.5652516205], [1707905.7264688741,3339137.5642169993], [1707906.1157613834,3339137.465651161], [1707913.596376588,3339135.441188001], [1707915.982687072,3339139.2194659123], [1707916.5576112843,3339144.174832307], [1707913.2721744557,3339160.2858256595], [1707913.1689179484,3339160.7504529916], [1707913.1689781095,3339160.755445618], [1707915.099455406,3339167.572078596], [1707924.4523196719,3339199.556550633], [1707924.132310222,3339199.52045983], [1707916.7243131478,3339201.347210937], [1707917.8169426501,3339205.009152649], [1707925.3934666319,3339203.8994135647], [1707925.6613840559,3339203.7563713696], [1707937.181199641,3339241.536880134], [1707944.2657911745,3339260.96553374], [1707957.6982640512,3339307.4813328343], [1707957.7043755238,3339307.4912458956], [1707959.5620104903,3339310.1522891056], [1707977.3129905201,3339335.530419684], [1707978.4776863432,3339338.9488154193], [1707981.9274795498,3339348.1138883163], [1707988.2698177812,3339374.6579220183], [1707988.7123386879,3339410.105090083], [1707988.1014913751,3339415.10580301], [1707987.9164662515,3339416.575079448], [1707985.857445372,3339427.053773295], [1707984.8716783202,3339432.0280453437], [1707984.7166568101,3339433.3351756404], [1707984.7186779254,3339433.337148628], [1707989.979013672,3339449.136541941], [1707991.0720925038,3339452.2472112267], [1707998.4708563956,3339473.2988087796], [1707999.5692735114,3339476.529254094], [1708011.6928405925,3339510.6993742706], [1708023.9155517337,3339546.143600321], [1708023.9136870413,3339546.154608164], [1708026.0133474357,3339548.1896632807], [1708028.3447397528,3339549.971159787], [1708035.417131573,3339555.4415378193], [1708097.6294695046,3339595.575115841], [1708130.008731012,3339604.67269564], [1708141.369625783,3339613.8984235013], [1708147.371471705,3339619.943052513], [1708159.7590269994,3339632.6517532924], [1708167.6764178914,3339647.505436995], [1708171.8288947642,3339660.624759896], [1708173.034482108,3339664.7926621884], [1708173.3806796805,3339668.9080044585], [1708173.4451868138,3339670.3742734846], [1708173.9772053636,3339680.884757296], [1708166.1981650977,3339701.014519031], [1708164.4502460482,3339703.5631169314], [1708160.9174086202,3339712.062328996], [1708156.2686364744,3339718.703481194], [1708153.043969838,3339726.9193521785], [1708152.789928944,3339735.597761318], [1708154.7988056308,3339741.784282443], [1708167.1906359599,3339754.930346279], [1708181.1736771464,3339760.2226670804], [1708191.960608093,3339759.3438727064], [1708204.0269018747,3339758.3877434223], [1708208.3409988033,3339757.711686033], [1708221.7512370716,3339753.087224868], [1708230.3664554819,3339749.332466555], [1708231.0187200583,3339749.0120225213], [1708231.4357177927,3339748.7263710853], [1708232.1797261233,3339748.3119451567], [1708244.1352873049,3339738.1505119205], [1708247.1595485527,3339735.2739496296], [1708249.7275346103,3339733.401555811], [1708258.186469732,3339727.119949182], [1708292.9208179,3339719.305923067], [1708296.382254541,3339717.7352465214], [1708329.0658486919,3339695.121680054], [1708330.65311863,3339694.82202595], [1708331.7057216582,3339695.4025511765], [1708339.8397487695,3339737.216503908], [1708348.250618889,3339767.105998256], [1708348.377571233,3339769.8508102456], [1708346.398427542,3339777.8329602564], [1708343.6055140505,3339793.3767580604], [1708342.7643505344,3339806.4633772094], [1708343.3689333145,3339821.685705844], [1708344.5342974693,3339827.8194732927], [1708349.0385792474,3339837.720931286], [1708357.7131903442,3339851.8474376216], [1708376.120486413,3339873.502569578], [1708382.8866797984,3339883.314850541], [1708386.4793000715,3339888.826156455], [1708395.2166127511,3339923.768076736], [1708396.3158442136,3339928.2177847256], [1708398.794310322,3339937.0820685867], [1708401.0932362457,3339945.29338873], [1708406.3770019864,3339964.204337049], [1708407.9256430375,3339969.7402747553], [1708409.4257287073,3339975.059087597], [1708410.3976352816,3339976.733129294], [1708416.953701958,3339989.9174521086], [1708421.5264599258,3339999.03702267], [1708425.1245486997,3340006.3278908515], [1708428.737635385,3340013.462785723], [1708430.590956827,3340017.091586898], [1708432.9729549494,3340021.837520117], [1708433.5956255575,3340023.0473943697], [1708433.7409301316,3340023.420144365], [1708433.7741346797,3340023.6064954293], [1708435.5713373828,3340034.196700248], [1708436.26214833,3340038.400663684], [1708438.1970215314,3340042.3410655092], [1708449.1586536996,3340045.5177543676], [1708453.0580969783,3340046.6561813103], [1708460.6213133088,3340048.8431987585], [1708461.9358901689,3340049.2018573093], [1708465.7251346507,3340050.3116521537], [1708466.7274137181,3340050.611158152], [1708466.9777909429,3340050.670058257], [1708469.5613987835,3340051.4199815206], [1708471.3934645858,3340051.9591547716], [1708475.1054375381,3340053.037923378], [1708475.6844631815,3340053.186837599], [1708478.800163562,3340054.086040927], [1708503.8609422902,3340060.5882570203], [1708504.580034525,3340060.7664419664], [1708505.8597698698,3340060.720060517], [1708517.4630067854,3340060.205923104], [1708527.9611284393,3340053.494378562], [1708529.3997539007,3340051.1361598102], [1708534.301779971,3340039.6555004995], [1708538.4439220396,3340028.5894588903], [1708538.5659695896,3340028.2754045115], [1708538.6239268072,3340026.6209094524], [1708538.6544438577,3340025.34124613], [1708537.6681293058,3340025.040549073], [1708537.796229362,3340023.9774196716], [1708539.2071096483,3340011.6019821903], [1708535.952561676,3340003.0596374776], [1708536.6008199956,3340002.489674054], [1708545.6227030049,3339992.3634948647], [1708546.1933276579,3339991.732449686], [1708546.939036239,3339992.7850484867], [1708569.6967463563,3339966.1704833964], [1708583.5058443143,3339951.867202329], [1708611.7489212132,3339931.117732398], [1708619.8382591936,3339922.5007981765], [1708622.42888194,3339914.791908649], [1708640.631123459,3339905.054533072], [1708642.7532977387,3339903.811680455], [1708646.5876750285,3339902.111775349], [1708650.0808090186,3339900.602633919], [1708650.5451099905,3339900.3473712457], [1708650.948874018,3339900.123796862], [1708663.6141983713,3339893.2624049336], [1708687.8448460707,3339880.0810744176], [1708687.836194093,3339879.3631355856], [1708688.239717676,3339879.202480284], [1708690.1509883287,3339878.524420225], [1708703.8541197567,3339873.5848447317], [1708706.8011585213,3339873.4245958207], [1708715.9162497404,3339872.284222075], [1708746.055555314,3339858.658285575], [1708764.2413484496,3339847.548042385], [1708769.495177181,3339844.550738524], [1708770.1606862927,3339844.1692162342], [1708772.7018423807,3339842.639690336], [1708778.647454074,3339833.6430349005], [1708792.1402636217,3339828.081927728], [1708805.2578004727,3339817.2194154887], [1708822.8938220714,3339804.492963421], [1708836.7344283983,3339791.5614849627], [1708844.6515879412,3339790.7172735366], [1708868.8473665174,3339790.2392391404], [1708903.6549355448,3339793.659255201], [1708916.143208011,3339790.263285662], [1708929.5972540118,3339784.046522754], [1708944.7273279661,3339779.6829648176], [1708947.5265652547,3339768.476326789], [1708965.5438462705,3339749.847148684], [1708982.4515631665,3339741.4986541755], [1708982.6371559496,3339741.4025427722], [1708985.4458064511,3339740.026485515], [1708988.4546557528,3339738.523281996], [1708991.7589474372,3339736.9236419825], [1709004.7572147888,3339729.1833125474], [1709015.200097369,3339723.0347967558], [1709016.0062534153,3339722.55670637], [1709018.5158396526,3339721.059419724], [1709024.4351424244,3339717.5867221695], [1709032.777769115,3339711.930793656], [1709042.7767747045,3339705.1633600085], [1709065.4093911746,3339694.1244738037], [1709067.2895091334,3339692.104480149], [1709067.566341094,3339691.789659441], [1709070.2932766494,3339688.85366878], [1709073.1467070861,3339682.235162179], [1709079.8358151563,3339675.350825565], [1709090.0178235432,3339673.418737139], [1709089.5560826971,3339672.6433428684], [1709108.2452367465,3339665.771287596], [1709139.2927657894,3339692.6740650814], [1709138.9351234515,3339666.775118879], [1709139.348417502,3339667.4252647026], [1709153.3167351205,3339681.9564323057], [1709165.8082227344,3339694.258392809], [1709166.89816785,3339695.3687597285], [1709167.0721640089,3339695.5534138745], [1709167.765670055,3339696.1692241384], [1709186.3836126344,3339713.17208698], [1709227.6301033513,3339747.3480857266], [1709259.500811318,3339769.9647350977], [1709271.5797232105,3339779.1817841195], [1709277.8991694665,3339784.3176763733], [1709278.497935669,3339784.7788358885], [1709278.6860637595,3339784.901402247], [1709286.0749674614,3339790.773507092], [1709287.0038724265,3339791.4483976867], [1709313.5433855515,3339810.6338507663], [1709316.9114101122,3339812.997055989], [1709325.213888426,3339819.606152609], [1709360.9913601412,3339847.856034374], [1709387.5672932672,3339868.8206685707], [1709389.4579465205,3339870.3268442247], [1709400.697758874,3339879.8655849006], [1709401.5487910304,3339880.5424123034], [1709409.1293473889,3339885.4125382593], [1709413.7698011808,3339888.540367622], [1709438.3279058973,3339906.4703958607], [1709453.2016582023,3339917.1517581837], [1709461.4662862052,3339923.106181454], [1709481.2078215403,3339937.630773478], [1709483.9639789602,3339939.688767918], [1709487.6456805188,3339942.2339433786], [1709494.8246354428,3339947.515365066], [1709522.4228622958,3339967.780584951], [1709549.9100309524,3339987.95416505], [1709556.9032404479,3339993.393616035], [1709586.835697632,3340014.4416177007], [1709586.9616440958,3340014.53397459], [1709597.652783446,3340022.4564861325], [1709615.0807711957,3340035.3541603275], [1709619.845730811,3340039.697861934], [1709631.942606311,3340046.510988904], [1709675.5063791107,3340079.9730178006], [1709716.6004860415,3340110.529721883], [1709724.5171189406,3340116.114731325], [1709822.5472054908,3340190.521398804], [1709878.7040360053,3340222.738290581], [1709879.41088269,3340223.1352304164], [1709912.0260778966,3340241.467554245], [1709912.7478145645,3340241.86531324], [1709930.691112903,3340252.1033034166], [1709976.2748193434,3340275.741133603], [1709991.0182293456,3340283.3971853796], [1709998.9692926477,3340258.147243646], [1710003.5080530895,3340243.7059511393], [1710012.3012359662,3340215.824461718], [1710017.8455397491,3340198.125385011], [1710019.9494488072,3340191.3900881736], [1710024.0804730067,3340178.077210843], [1710029.0537470966,3340162.226558093], [1710033.395168751,3340148.2240630565], [1710034.4318072463,3340144.9030873734], [1710034.3192137927,3340144.5928603793], [1710038.5697821449,3340130.84102738], [1710049.0361274262,3340098.102935871], [1710050.2466110117,3340080.767576051], [1710049.1562611174,3340067.9229857093], [1710049.0407073162,3340063.555312396], [1710052.437811428,3340059.2082216744], [1710052.2701087818,3340046.602178585], [1710052.6202987023,3340045.9108756958], [1710052.8489343263,3340045.4087870317], [1710060.5955978208,3340038.20012834], [1710062.7043262506,3340035.8338405993], [1710071.9185389346,3340023.5213876627], [1710072.5703634613,3340021.921657868], [1710073.3164220492,3340020.351751064], [1710077.299334535,3340007.7587029715], [1710080.5672701085,3339996.6711727325], [1710084.1936555058,3339985.4865466803], [1710088.767616236,3339971.3883766094], [1710094.1970361117,3339954.4396901373], [1710096.7174619022,3339946.044585767], [1710096.7923381552,3339945.794016815], [1710097.3336436725,3339945.3820347562], [1710102.1683078895,3339937.428513648], [1710108.7023142537,3339926.7081808043], [1710108.9421612676,3339925.8933745837], [1710115.8561627655,3339887.0811723038], [1710116.516576873,3339884.9511473924], [1710132.7785116741,3339837.1323700664], [1710134.4348038188,3339832.119178232], [1710130.2358543752,3339811.3227222143], [1710122.097039848,3339805.3657910717], [1710116.6506272485,3339801.4058029233], [1710110.9864438125,3339800.10079692], [1710080.9454613163,3339793.3469284372], [1710069.8519666002,3339790.8269614675], [1710067.9894582888,3339790.412989611], [1710067.7860123832,3339790.35352405], [1710062.7003813854,3339787.8241712013], [1710062.3285073363,3339786.7990276488], [1710056.9678470327,3339771.789858777], [1710058.1319371457,3339768.6859563505], [1710059.628517839,3339764.6733551733], [1710059.9455037415,3339763.795702046], [1710060.4436693967,3339762.4474909715], [1710063.2856726726,3339754.7985103666], [1710067.7745152232,3339742.7606181502], [1710073.6232529064,3339726.961339159], [1710073.740576033,3339722.4349679872], [1710073.8640937693,3339717.0967062814], [1710073.681446039,3339716.194115642], [1710073.6151578682,3339715.9142892295], [1710073.0549237407,3339714.6727076517], [1710071.2744563632,3339710.6994985705], [1710068.9345009539,3339705.5466161873], [1710060.1393080847,3339693.013484994], [1710055.845392452,3339690.224923648], [1710047.2737504183,3339684.648704244], [1710047.225315774,3339684.5244546253], [1710029.3523641797,3339661.957262646]]]];
            } else {
            	coordinates = [[[[190522.1347,544893.7679],[190516.401,544880.9559],[190511.7921,544877.3618],[190513.1821,544874.7679],[190514.3701,544872.6119],[190516.0262,544869.55],[190529.2604,544845.4244],[190542.2927,544821.6128],[190550.6209,544806.551],[190550.5119,544805.957],[190550.4959,544805.613],[190544.6991,544795.8],[190540.4493,544788.519],[190538.7303,544785.582],[190538.3243,544784.832],[190538.1053,544784.425],[190534.1995,544776.832],[190528.1837,544765.082],[190520.325,544749.894],[190515.0282,544739.613],[190518.5914,544728.3942],[190524.2006,544710.7384],[190531.7479,544687.7387],[190532.716,544684.7077],[190532.732,544684.3007],[190531.7631,544680.7698],[190527.1234,544663.7698],[190526.2644,544660.6138],[190524.9355,544655.8009],[190524.5295,544654.3329],[190524.4045,544653.8639],[190524.0456,544652.5829],[190523.7486,544651.4889],[190523.6396,544651.1139],[190522.7017,544647.6759],[190521.6707,544643.8949],[190520.7018,544640.4269],[190519.4679,544635.8329],[190519.0609,544634.3639],[190518.749,544628.52],[190518.655,544626.614],[190518.4831,544623.364],[190517.4364,544603.0202],[190517.5614,544602.0202],[190517.9365,544600.6142],[190519.6245,544594.3333],[190525.5938,544572.1766],[190529.9531,544556.0528],[190531.9222,544549.3018],[190531.9532,544549.1778],[190532.4372,544548.3959],[190536.4372,544543.865],[190539.0942,544540.834],[190562.8445,544513.5526],[190563.3125,544513.2716],[190567.3446,544506.8658],[190569.2036,544503.9288],[190571.2666,544500.5529],[190573.0637,544497.5539],[190575.9537,544492.772],[190578.2668,544488.9291],[190584.0159,544479.4603],[190585.7659,544476.5543],[190588.7669,544471.6474],[190592.313,544465.7725],[190593.61,544463.6475],[190593.673,544463.4605],[190595.0951,544456.9606],[190595.5482,544454.9606],[190598.4383,544441.8978],[190602.0016,544426.023],[190605.8298,544408.8672],[190608.971,544394.9924],[190619.7676,544349.5239],[190627.1281,544319.0873],[190629.6442,544308.8064],[190630.4563,544305.3064],[190637.4878,544270.5878],[190637.2378,544270.0558],[190637.0658,544269.7438],[190632.7849,544267.1188],[190633.7379,544261.4308],[190640.6915,544220.2133],[190649.3803,544167.1819],[190649.8964,544164.0889],[190652.5676,544149.8701],[190654.0057,544143.4641],[190654.9437,544139.3072],[190655.0368,544138.0262],[190655.2558,544134.9642],[190654.3651,544113.8084],[190660.9905,544087.6517],[190662.5536,544081.3708],[190665.0688,544067.215],[190668.069,544050.6211],[190670.9133,544034.9653],[190673.4915,544020.8405],[190675.4296,544009.9346],[190677.8668,543996.2157],[190677.9298,543995.7157],[190678.617,543983.6529],[190679.1802,543973.6529],[190679.3992,543972.122],[190680.8203,543965.435],[190682.2584,543958.5911],[190683.9455,543950.6532],[190686.2897,543939.5913],[190686.8677,543936.9034],[190687.1497,543935.5594],[190687.5558,543933.3104],[190688.4148,543929.2785],[190692.0091,543912.6227],[190696.2283,543892.9979],[190698.5095,543881.873],[190702.1037,543864.6232],[190706.5571,543843.2485],[190708.9942,543831.5296],[190711.1824,543820.8427],[190713.4016,543808.8739],[190714.2606,543804.1859],[190714.9167,543800.6549],[190717.8699,543784.6551],[190719.104,543777.8432],[190721.2612,543766.9373],[190725.0264,543748.1245],[190729.5588,543725.6868],[190732.3089,543712.094],[190736.8713,543689.4062],[190741.6996,543665.6875],[190748.2161,543634.0638],[190750.8413,543621.47],[190756.0606,543595.4073],[190757.8888,543586.4074],[190759.4519,543578.6265],[190762.0921,543565.2516],[190770.5927,543522.4711],[190776.1871,543493.9714],[190780.5313,543479.4716],[190781.6873,543475.7217],[190790.5626,543454.065],[190803.328,543422.7845],[190803.703,543422.5975],[190804.5631,543416.8785],[190806.7353,543401.3477],[190808.2195,543391.2228],[190809.4386,543382.9419],[190811.0638,543371.879],[190811.4698,543369.098],[190811.8289,543360.4731],[190812.4392,543346.3482],[190813.3145,543326.3174],[190813.4395,543325.9104],[190826.7047,543306.1918],[190838.6429,543288.5671],[190844.034,543280.3803],[190846.752,543276.4114],[190863.1593,543252.1928],[190910.16,543181.9452],[190991.3013,543060.3215],[190991.6453,543059.1975],[190991.7853,543058.6655],[190991.9423,543058.6345],[191003.7233,543050.1037],[191014.3794,543042.448],[191027.4734,543032.9483],[191036.6295,543026.3545],[191043.1765,543022.0416],[191073.0205,543003.8232],[191105.1146,542984.2619],[191120.1926,542975.0742],[191134.6456,542965.2935],[191149.3797,542955.7009],[191163.8487,542947.7012],[191166.7547,542946.1072],[191169.5047,542944.7943],[191230.1606,542916.3585],[191261.2867,542898.3591],[191300.4268,542872.516],[191307.1928,542869.0781],[191382.3174,542853.5174],[191398.2704,542845.5488],[191403.7394,542845.2678],[191423.6602,542844.3622],[191438.4891,542843.8934],[191436.6922,542841.1434],[191431.2393,542832.7994],[191428.1614,542828.1124],[191424.6926,542822.7994],[191423.6606,542821.2374],[191421.2087,542817.4874],[191417.4738,542811.7683],[191415.5519,542808.8623],[191411.63,542802.8623],[191410.037,542800.4243],[191407.4121,542796.4243],[191405.0992,542792.8933],[191400.1774,542785.3623],[191395.0525,542777.5183],[191391.6626,542772.3623],[191386.9438,542765.1432],[191383.8499,542760.4242],[191380.944,542755.9552],[191376.5691,542749.2682],[191372.6633,542743.2992],[191368.6784,542737.2372],[191364.9755,542731.5492],[191362.5066,542727.7991],[191359.2727,542722.8301],[191357.0388,542719.4561],[191352.0699,542711.8311],[191348.1321,542705.8311],[191346.0231,542702.6431],[191343.5382,542699.0181],[191338.8204,542692.0181],[191321.4929,542665.424],[191304.3685,542639.1739],[191293.1809,542621.7679],[191279.4163,542600.7369],[191264.5568,542577.9548],[191261.1349,542572.7048],[191256.901,542566.2368],[191252.0102,542558.7368],[191247.2764,542551.4868],[191241.6665,542542.8617],[191238.2606,542537.6737],[191236.2607,542534.5797],[191232.8238,542529.3297],[191231.3239,542527.0177],[191230.3549,542525.5487],[191228.386,542522.4867],[191227.371,542520.9557],[191223.3241,542514.7367],[191218.9963,542508.0807],[191215.3554,542502.5486],[191210.8865,542495.7676],[191207.9336,542491.2676],[191201.9028,542482.1116],[191198.043,542476.2366],[191187.8093,542460.6115],[191183.0904,542453.3615],[191178.4656,542446.2985],[191166.966,542428.8305],[191161.0132,542419.6735],[191151.8575,542404.3924],[191148.4986,542398.7984],[191145.3887,542393.8304],[191143.4678,542390.7984],[191141.0299,542386.8304],[191137.889,542381.6424],[191134.0611,542375.1114],[191131.5142,542370.6734],[191129.5463,542367.2044],[191127.3423,542363.2674],[191124.7954,542358.6424],[191123.3745,542355.8924],[191121.5306,542352.3614],[191120.6086,542350.5804],[191119.8586,542349.1114],[191117.8117,542345.0804],[191115.2808,542339.9864],[191113.1399,542335.6424],[191111.343,542331.8924],[191109.75,542328.4864],[191108.7971,542326.4544],[191107.5621,542323.8294],[191106.9532,542322.5174],[191106.5622,542321.6734],[191103.7503,542315.5484],[191102.5314,542312.7044],[191101.0004,542309.1114],[191098.8905,542304.0174],[191097.8596,542301.4554],[191096.6726,542298.4544],[191095.5477,542295.5804],[191094.8597,542293.8304],[191094.1718,542292.0174],[191092.9068,542288.7364],[191091.7819,542285.8304],[191091.1729,542284.2054],[191090.7189,542283.0174],[191088.595,542277.2675],[191086.6261,542271.6115],[191085.6572,542268.7985],[191082.9224,542260.6425],[191080.8765,542254.1115],[191079.5016,542249.5485],[191078.7826,542247.1735],[191078.2196,542245.2676],[191077.5637,542243.0176],[191077.0167,542241.1736],[191076.1418,542238.0176],[191075.5958,542235.9866],[191075.2978,542234.9546],[191074.3139,542231.2056],[191074.0639,542230.2676],[191072.845,542225.4866],[191071.3921,542220.1117],[191070.7201,542217.6117],[191070.0642,542214.8617],[191068.7053,542208.8927],[191067.7994,542204.6737],[191067.4554,542203.0807],[191065.7056,542194.7058],[191036.2839,542187.7984],[191023.2841,542184.8922],[191010.5652,542181.986],[190991.8314,542177.7667],[190971.9727,542173.2664],[190960.7688,542170.7663],[190954.7219,542169.4222],[190952.8009,542168.9852],[190950.8469,542168.5791],[190946.754,542167.6721],[190913.2533,542160.4216],[190899.5045,542157.3594],[190872.0828,542151.265],[190859.552,542148.5148],[190850.7391,542146.5457],[190843.6922,542145.2016],[190842.0202,542144.9836],[190826.0673,542142.9834],[190819.8954,542142.2023],[190787.724,542123.7329],[190792.7397,542139.2639],[190805.5203,542154.4199],[190819.8639,542171.483],[190821.3169,542173.233],[190821.1299,542173.264],[190819.8639,542173.483],[190781.4891,542180.0443],[190762.7701,542186.919],[190749.8802,542192.3567],[190744.8481,542196.4506],[190731.6451,542204.9183],[190729.6451,542206.1993],[190713.8788,542233.9808],[190712.0978,542235.6988],[190711.5668,542234.8868],[190705.6607,542241.2306],[190685.6766,542262.1671],[190681.9575,542266.167],[190678.7545,542269.636],[190678.6605,542269.761],[190669.8634,542279.2917],[190669.3474,542279.8237],[190669.4883,542285.2917],[190665.0663,542289.2916],[190663.4723,542290.7295],[190662.8163,542291.3235],[190654.7072,542298.7603],[190654.2222,542299.1983],[190647.7382,542305.1352],[190645.8792,542306.8541],[190644.1912,542308.3851],[190641.4722,542308.072],[190616.4724,542305.1347],[190606.2695,542303.9465],[190604.7695,542305.1345],[190599.4416,542305.1344],[190595.6296,542306.8843],[190587.0516,542311.1031],[190579.8476,542314.728],[190575.3006,542316.8839],[190577.6765,542319.2589],[190569.8636,542322.1958],[190569.3636,542322.4148],[190550.9106,542329.4144],[190549.5826,542329.9144],[190550.7385,542334.5394],[190549.6765,542334.9143],[190541.0665,542337.8522],[190532.8166,542340.727],[190524.0666,542343.7269],[190515.6606,542346.6947],[190504.4106,542350.5385],[190502.4256,542351.2265],[190493.4887,542353.8513],[190484.7387,542356.4131],[190475.8797,542359.038],[190467.1298,542361.7258],[190462.9578,542362.9757],[190461.8797,542366.8507],[190460.0196,542373.4436],[190459.2546,542372.4756],[190454.2697,542374.7565],[190450.5977,542376.5374],[190456.7694,542386.7564],[190455.9104,542390.4444],[190453.4102,542403.3812],[190452.9412,542405.7562],[190448.7219,542427.287],[190448.4248,542429.037],[190448.1908,542430.5369],[190451.1758,542432.506],[190449.2376,542440.0999],[190447.2065,542447.4118],[190444.8154,542457.1617],[190442.5503,542466.3496],[190441.2372,542471.7555],[190440.4091,542476.8494],[190435.3458,542498.9112],[190431.3459,542495.3491],[190423.9088,542504.4109],[190420.2678,542509.3168],[190419.3928,542510.4738],[190418.1587,542511.9738],[190416.1737,542514.6297],[190412.3457,542519.7546],[190408.3456,542525.1295],[190406.1116,542528.0675],[190404.4556,542530.2234],[190403.9236,542530.9414],[190395.5174,542542.2542],[190389.1894,542550.785],[190378.4552,542565.2537],[190376.0802,542568.4406],[190364.424,542584.7223],[190361.611,542588.6593],[190360.6109,542593.8152],[190357.6727,542609.003],[190357.1576,542611.691],[190354.8445,542623.5969],[190352.3765,542622.7528],[190343.7046,542619.6587],[190342.7666,542619.3157],[190350.6263,542635.8467],[190354.4073,542636.8467],[190364.3761,542644.4718],[190370.9849,542649.8469],[190372.6259,542652.0029],[190377.6567,542657.5029],[190389.0935,542669.941],[190389.1715,542670.066],[190406.562,542692.0031],[190408.1709,542695.4401],[190408.2649,542695.5971],[190396.2338,542709.9408],[190387.9678,542713.5026],[190369.5618,542721.3773],[190352.5928,542728.033],[190330.0469,542736.0645],[190322.0149,542736.7514],[190316.6559,542739.2203],[190314.8589,542739.5013],[190311.906,542739.9702],[190311.624,542740.0322],[190292.2811,542743.6259],[190287.1561,542745.1568],[190281.4841,542749.7196],[190271.3591,542751.4375],[190257.5931,542756.7502],[190254.4222,542754.5942],[190246.2342,542758.75],[190240.5152,542762.9999],[190227.5932,542767.2186],[190217.1092,542770.6244],[190208.5473,542773.4053],[190207.0463,542773.9053],[190199.7343,542776.2801],[190198.1403,542776.8111],[190190.6713,542779.2489],[190189.6713,542779.5619],[190189.3123,542779.7799],[190182.1403,542784.1548],[190180.8753,542784.9367],[190179.8593,542785.5297],[190172.7813,542787.3426],[190171.3123,542787.7176],[190142.8905,542794.935],[190125.8435,542799.2787],[190124.9066,542798.1537],[190111.5476,542801.9975],[190107.7186,542803.1534],[190104.1566,542804.5283],[190082.2347,542812.4969],[190072.9067,542815.9028],[190060.1568,542818.3715],[190060.3438,542818.7465],[190060.8907,542821.3405],[190061.5157,542824.2465],[190050.2037,542825.4023],[190040.2348,542826.3401],[190034.1258,542826.902],[190033.5629,542825.121],[190021.3129,542826.9648],[190014.407,542827.9017],[189990.9691,542831.0263],[189978.7822,542831.9331],[189974.3762,542832.245],[189967.9853,542832.7139],[189967.2663,542832.7449],[189961.4543,542832.8698],[189958.1724,542832.9638],[189957.5164,542832.9638],[189952.0164,542832.7447],[189951.7354,542832.7447],[189947.3135,542832.8076],[189940.7354,542838.3695],[189940.5794,542838.4944],[189938.9694,542838.7754],[189934.4855,542838.6824],[189932.4075,542838.6503],[189922.7666,542838.4312],[189911.1727,542838.431],[189907.1106,542843.8059],[189906.7356,542844.3069],[189906.2196,542844.9628],[189905.9546,542845.3058],[189905.7976,542845.5248],[189904.2196,542847.5878],[189902.6595,542849.6478],[189905.3305,542853.0988],[189907.1604,542855.4188],[189904.1104,542856.8787],[189895.3004,542861.0885],[189884.4904,542866.5383],[189884.4734,542866.5363],[189883.0634,542867.8683],[189882.3444,542868.5243],[189881.8134,542869.0243],[189878.5484,542871.9622],[189873.1734,542876.8051],[189868.4383,542881.3049],[189865.3763,542884.2429],[189863.0943,542886.4298],[189856.5942,542892.6177],[189856.2822,542892.9307],[189855.1102,542894.3366],[189844.0161,542907.6483],[189840.9691,542911.3363],[189839.266,542913.3982],[189838.766,542913.9922],[189834.376,542919.3361],[189828.4219,542926.5229],[189823.5939,542931.7108],[189822.3759,542933.0228],[189822.2499,542933.3358],[189821.3748,542935.3977],[189819.0478,542941.0227],[189817.4067,542944.6166],[189811.2976,542958.1164],[189810.4065,542960.0854],[189800.8755,542967.8351],[189792.4534,542974.679],[189791.1874,542975.7099],[189789.8284,542977.1789],[189783.0624,542984.3967],[189777.2813,542990.5846],[189774.7343,542993.3345],[189770.5472,542997.8344],[189761.6722,543007.3962],[189757.0622,543009.5841],[189748.5312,543013.7089],[189746.6872,543014.6459],[189740.1092,543017.9588],[189735.8902,543020.0837],[189735.7032,543020.5207],[189711.0003,543024.3642],[189703.7653,543030.6761],[189699.7812,543034.864],[189670.7657,543018.6446],[189663.2037,543021.7075],[189663.0467,543021.7075],[189657.3758,543023.3634],[189647.8758,543027.8632],[189639.3128,543031.925],[189630.9228,543035.9249],[189628.3758,543036.6758],[189627.9068,543036.8628],[189618.6258,543040.5186],[189618.3128,543040.6436],[189609.4848,543045.1124],[189607.2348,543046.1124],[189606.7348,543046.3004],[189583.5629,543054.237],[189567.4229,543059.9556],[189565.5479,543060.6426],[189550.0478,543074.6742],[189530.7348,543084.2669],[189530.5628,543084.3609],[189528.1408,543085.5478],[189521.5158,543088.8297],[189519.2348,543089.9546],[189508.4228,543095.0484],[189508.0319,543093.6724],[189491.1719,543097.6731],[189489.5159,543099.5791],[189477.6409,543108.5468],[189460.8911,543104.8906],[189437.1571,543114.8591],[189432.4851,543119.703],[189428.844,543125.0779],[189422.5941,543122.0468],[189414.7972,543119.0147],[189412.6262,543118.4837],[189408.4073,543117.8896],[189408.4103,543117.8786],[189413.9046,543094.9699],[189419.365,543064.7102],[189427.7173,543046.9485],[189429.2214,543039.8396],[189433.9717,543017.3899],[189440.2419,542998.7201],[189446.5122,542978.3704],[189456.2175,542953.0987],[189458.8826,542946.1608],[189462.3528,542935.641],[189462.5128,542935.151],[189464.6329,542928.7111],[189465.6039,542925.7761],[189439.2849,542937.2666],[189415.035,542947.8601],[189379.8,542963.2654],[189338.566,542981.4206],[189308.91,542998.139],[189251.113,543030.7318],[189241.3472,543022.5757],[189233.8164,543013.2937],[189223.9727,543001.1686],[189223.0357,543000.6066],[189222.1137,543000.0756],[189209.6449,542992.9505],[189200.6301,542983.6374],[189199.5202,542982.5124],[189200.5832,542981.0124],[189192.6143,542975.0753],[189181.8485,542969.7312],[189179.9275,542969.0432],[189176.8486,542966.2622],[189158.3648,542963.3559],[189156.3278,542962.5949],[189156.3279,542960.6939],[189153.2749,542959.3258],[189149.315,542958.2558],[189149.245,542958.6658],[189148.9949,542960.4058],[189148.9449,542960.7958],[189130.4251,542956.1255],[189114.3353,542952.0653],[189111.3614,542951.3142],[189112.7324,542948.7503],[189103.5205,542945.1032],[189087.1278,542939.1],[189081.3868,542937.6969],[189066.976,542933.0467],[189063.5661,542931.9716],[189061.2961,542931.2666],[189045.9603,542926.4504],[189041.6734,542925.2193],[189039.8964,542924.6703],[189022.8916,542919.2321],[189010.7858,542915.4429],[188992.736,542909.6347],[188985.8181,542907.3636],[188964.6814,542900.0413],[188960.7784,542898.8943],[188923.6519,542890.4227],[188906.9241,542887.4195],[188905.9801,542887.3595],[188905.4311,542887.4155],[188908.524,542889.8525],[188920.2747,542904.1956],[188925.0706,542910.2906],[188932.0383,542921.4156],[188938.133,542937.2906],[188943.8827,542952.5395],[188944.2577,542952.7905],[188944.8667,542954.5085],[188946.7566,542959.2275],[188946.3505,542963.1965],[188943.8202,542986.2893],[188943.8502,542987.9463],[188944.6778,543012.1331],[188946.0217,543017.883],[188949.3184,543030.602],[188955.6002,543041.259],[188955.7872,543041.571],[188957.1781,543045.571],[188976.9898,543057.6332],[189031.0523,543058.6661],[189056.5355,543095.0722],[189094.3781,543163.0722],[189095.253,543168.7592],[189100.7216,543192.4471],[189101.4394,543203.478],[189101.5644,543204.228],[189104.3141,543219.0399],[189103.4698,543242.7587],[189092.7046,543260.6334],[189079.8605,543272.2891],[189067.4224,543283.9148],[189042.1105,543293.7883],[189037.4855,543295.7262],[189017.4387,543293.6319],[189001.017,543278.9138],[188978.0497,543247.1007],[188961.878,543235.8815],[188944.8932,543234.5062],[188920.0183,543243.7248],[188906.8461,543261.0684],[188900.5029,543275.2552],[188887.174,543275.162],[188863.0973,543268.0367],[188832.8779,543251.3483],[188819.1281,543239.7542],[188810.6443,543233.0041],[188792.0508,543212.91],[188775.083,543207.9107],[188764.27,543213.2225],[188761.364,543214.6605],[188745.9889,543227.6911],[188738.9888,543241.3779],[188740.2854,543266.2217],[188736.472,543288.8155],[188720.3938,543313.596],[188719.2997,543317.5959],[188716.5186,543327.6268],[188718.9092,543350.3457],[188719.3312,543351.0647],[188739.3925,543384.6587],[188739.8145,543385.3767],[188748.1742,543397.2827],[188754.2521,543403.4398],[188781.7045,543425.0641],[188792.9543,543433.9092],[188801.11,543444.6902],[188819.5627,543455.2214],[188835.1096,543454.5347],[188851.0635,543449.316],[188858.7817,543436.7842],[188864.9538,543425.5034],[188865.0628,543424.9404],[188870.0942,543399.5357],[188866.6426,543376.9728],[188885.3766,543367.8792],[188908.0942,543376.0365],[188914.485,543390.4105],[188911.7508,543404.4423],[188905.2036,543416.8481],[188900.9215,543424.88],[188900.4845,543425.692],[188894.7964,543433.7538],[188897.0932,543449.0347],[188908.3589,543461.4728],[188913.6247,543467.1598],[188916.0467,543469.5978],[188931.2643,543484.348],[188916.0933,543495.3466],[188904.342,543520.2222],[188890.0458,543537.2219],[188868.6078,543550.1594],[188852.842,543546.8772],[188836.6082,543537.409],[188818.1247,543520.1579],[188817.8587,543519.8769],[188794.9222,543495.6267],[188775.9695,543491.4394],[188756.5626,543494.9071],[188743.5627,543492.8139],[188727.798,543484.1887],[188719.7351,543479.2506],[188719.5001,543479.0956],[188702.6414,543468.7824],[188695.9535,543470.6253],[188690.9225,543473.5632],[188681.5954,543481.344],[188675.7194,543483.7809],[188665.4704,543488.0627],[188659.8915,543488.6566],[188652.6575,543487.7805],[188641.9386,543486.4993],[188636.4228,543482.1253],[188634.1729,543477.4693],[188633.5021,543464.8124],[188636.1582,543454.0305],[188640.9554,543442.0627],[188642.6746,543425.2188],[188640.0499,543411.4689],[188636.315,543401.8749],[188621.8475,543383.5628],[188614.5186,543376.5938],[188598.6449,543366.9056],[188586.7541,543361.8125],[188561.3004,543353.3741],[188540.3167,543350.6238],[188536.3007,543351.1547],[188532.6917,543351.6237],[188523.2697,543357.3425],[188518.1766,543365.0613],[188515.1605,543370.9982],[188514.2075,543375.2172],[188514.9884,543380.8101],[188517.8162,543392.4671],[188522.269,543400.4361],[188523.8949,543408.0291],[188523.8158,543414.686],[188521.7847,543417.061],[188517.8157,543421.7479],[188513.2067,543425.4038],[188508.9407,543428.5297],[188500.4247,543431.8725],[188492.3157,543435.0603],[188484.3467,543438.3102],[188481.9567,543439.2161],[188479.1447,543441.7781],[188476.9257,543443.747],[188474.9566,543450.0599],[188475.3155,543456.2149],[188479.0503,543465.5909],[188479.9093,543467.0599],[188484.6741,543473.1529],[188491.159,543481.1529],[188495.3308,543486.278],[188498.8767,543491.747],[188502.6746,543498.747],[188502.7054,543511.8089],[188502.7053,543515.8718],[188498.9232,543526.6527],[188493.798,543541.3095],[188485.6269,543551.6223],[188475.7828,543559.715],[188472.4228,543562.465],[188466.3768,543565.2769],[188461.4548,543567.7148],[188451.5168,543575.6835],[188445.5637,543583.9014],[188441.9225,543597.3392],[188441.3763,543607.8071],[188442.0943,543610.5901],[188447.422,543626.308],[188453.7198,543633.6831],[188455.9848,543636.7461],[188460.3437,543638.8081],[188460.5467,543638.9012],[188469.5155,543643.2143],[188473.8745,543644.3083],[188475.3275,543644.2763],[188493.6093,543643.7766],[188501.4532,543646.9647],[188505.4841,543653.2458],[188508.5939,543663.2457],[188508.3428,543672.4646],[188504.7337,543677.5895],[188499.9367,543680.7774],[188464.7649,543688.5258],[188462.6719,543688.9328],[188455.3749,543690.3076],[188450.9529,543694.3385],[188448.8748,543698.0255],[188447.7648,543703.8694],[188448.1707,543709.9324],[188449.5145,543716.3073],[188452.1714,543722.5263],[188453.4994,543725.5883],[188466.5299,543747.0574],[188509.6069,543789.0577],[188505.1846,543808.0885],[188490.8255,543819.5881],[188486.0745,543825.713],[188478.0911,543856.7756],[188459.8408,543882.6491],[188436.9176,543905.3986],[188398.4185,543930.6488],[188389.7465,543936.3356],[188369.7923,543957.0221],[188357.9332,543969.3038],[188353.1212,543971.3347],[188347.3713,543971.5846],[188326.2306,543964.2093],[188290.6533,543936.99],[188289.2472,543947.3029],[188287.0429,543963.0837],[188286.7619,543964.3957],[188274.7611,544018.802],[188275.0771,544018.915],[188276.6751,544019.5231],[188277.6851,544020.1031],[188300.4147,544033.6733],[188320.1843,544045.4636],[188328.2542,544050.0236],[188347.4439,544060.7639],[188366.2735,544071.5541],[188366.5835,544071.7341],[188372.9334,544075.3341],[188367.9733,544087.664],[188362.133,544104.1737],[188360.4629,544113.8036],[188356.2526,544135.2734],[188357.6823,544153.0033],[188359.0119,544176.3031],[188360.4416,544196.143],[188376.4906,544252.0227],[188387.6598,544291.6926],[188400.399,544337.5624],[188401.9989,544343.0424],[188402.0019,544343.0524],[188402.0049,544343.0514],[188402.3959,544342.9574],[188409.9109,544341.0205],[188412.2548,544344.8326],[188412.7707,544349.8015],[188409.2865,544365.8943],[188409.1775,544366.3583],[188409.1775,544366.3633],[188411.0283,544373.2123],[188420.0077,544405.3522],[188419.6877,544405.3122],[188412.2478,544407.052],[188413.2977,544410.732],[188420.8977,544409.7122],[188421.1677,544409.5722],[188432.247,544447.542],[188439.1066,544467.082],[188451.9957,544513.8218],[188452.0017,544513.8318],[188453.8297,544516.5188],[188471.2981,544542.1449],[188472.4231,544545.5819],[188475.7669,544554.8008],[188481.7974,544581.4567],[188481.8128,544616.9564],[188481.1408,544621.9564],[188480.9378,544623.4254],[188478.7496,544633.8932],[188477.7025,544638.8622],[188477.5315,544640.1692],[188477.5335,544640.1712],[188482.6102,544656.0551],[188483.6672,544659.1831],[188490.8218,544680.352],[188491.8827,544683.6],[188503.6101,544717.9619],[188515.4214,544753.6008],[188515.4194,544753.6118],[188517.4973,544755.6749],[188519.8103,544757.4869],[188526.8261,544763.0499],[188588.637,544803.9876],[188620.9496,544813.488],[188632.2143,544822.8631],[188638.1512,544828.9882],[188650.4019,544841.8633],[188658.1506,544856.8323],[188662.1503,544870.0192],[188663.3072,544874.2072],[188663.6042,544878.3322],[188663.6511,544879.8012],[188664.057,544890.3321],[188656.0247,544910.3948],[188654.2437,544912.9257],[188650.6036,544921.3936],[188645.8685,544927.9875],[188642.5404,544936.1754],[188642.1813,544944.8623],[188644.1182,544951.0813],[188656.3679,544964.3944],[188670.3057,544969.8625],[188681.1176,544969.1127],[188693.2115,544968.3009],[188697.5395,544967.676],[188711.0234,544963.2072],[188719.6954,544959.5514],[188720.3524,544959.2384],[188720.7734,544958.9574],[188721.5234,544958.5514],[188733.6175,544948.5207],[188736.6805,544945.6768],[188739.2745,544943.8329],[188747.8205,544937.645],[188782.6954,544930.2397],[188786.1804,544928.7087],[188819.1804,544906.4594],[188820.7734,544906.1785],[188821.8204,544906.7725],[188829.4607,544948.7403],[188837.5221,544978.7711],[188837.6161,544981.5211],[188835.538,544989.49],[188832.5538,545005.0208],[188831.5536,545018.1147],[188831.9753,545033.3646],[188833.0682,545039.5206],[188837.459,545049.4896],[188845.9747,545063.7396],[188864.1452,545085.6457],[188870.802,545095.5527],[188874.3329,545101.1147],[188882.6602,545136.2086],[188883.7072,545140.6775],[188886.082,545149.5835],[188888.2849,545157.8335],[188893.3475,545176.8334],[188894.8314,545182.3954],[188896.2693,545187.7394],[188897.2223,545189.4274],[188903.628,545202.7084],[188908.0968,545211.8953],[188911.6117,545219.2393],[188915.1435,545226.4273],[188916.9555,545230.0833],[188919.2834,545234.8643],[188919.8923,545236.0833],[188920.0333,545236.4583],[188920.0643,545236.6453],[188921.7361,545247.2713],[188922.3771,545251.4892],[188924.267,545255.4582],[188935.2049,545258.7714],[188939.0958,545259.9584],[188946.6427,545262.2396],[188947.9547,545262.6146],[188951.7356,545263.7716],[188952.7356,545264.0836],[188952.9856,545264.1456],[188955.5636,545264.9277],[188957.3916,545265.4897],[188961.0955,545266.6147],[188961.6735,545266.7708],[188964.7825,545267.7088],[188989.7982,545274.5221],[188990.5161,545274.7092],[188991.7981,545274.6782],[189003.423,545274.3034],[189014.0161,545267.7096],[189015.4851,545265.3656],[189020.5322,545253.9288],[189024.8134,545242.898],[189024.9394,545242.585],[189025.0174,545240.929],[189025.0634,545239.648],[189024.0794,545239.335],[189024.2205,545238.272],[189025.7826,545225.8971],[189022.6268,545217.3041],[189023.2828,545216.7412],[189032.4389,545206.7104],[189033.0179,545206.0854],[189033.7519,545207.1484],[189056.8611,545180.773],[189070.8612,545166.6173],[189099.3923,545146.181],[189107.5964,545137.6502],[189110.2835,545129.9623],[189128.6275,545120.4316],[189130.7675,545119.2127],[189134.6275,545117.5568],[189138.1435,545116.0878],[189138.6115,545115.8378],[189139.0185,545115.6188],[189151.7835,545108.9011],[189176.2055,545095.9946],[189176.2055,545095.2756],[189176.6115,545095.1196],[189178.5335,545094.4637],[189192.3145,545089.6829],[189195.2674,545089.558],[189204.4084,545088.5261],[189234.7523,545075.2457],[189253.0964,545064.3401],[189258.3934,545061.4022],[189259.0644,545061.0282],[189261.6274,545059.5273],[189267.6895,545050.5904],[189281.2674,545045.1847],[189294.5335,545034.466],[189312.3466,545021.9354],[189326.3617,545009.1537],[189334.2996,545008.4039],[189358.5334,545008.2172],[189393.3461,545012.0618],[189405.892,545008.812],[189419.439,545002.7493],[189434.6419,544998.5625],[189437.5801,544987.3747],[189455.8462,544968.9381],[189472.8772,544960.7825],[189473.0642,544960.6885],[189475.8932,544959.3445],[189478.9242,544957.8756],[189482.2522,544956.3137],[189495.3612,544948.7199],[189505.8922,544942.6892],[189506.7052,544942.2202],[189509.2362,544940.7512],[189515.2053,544937.3453],[189523.6273,544931.7825],[189533.7213,544925.1267],[189556.5173,544914.3462],[189558.4243,544912.3462],[189558.7053,544912.0343],[189561.4713,544909.1273],[189564.4084,544902.5344],[189571.1895,544895.7216],[189581.4084,544893.9098],[189580.9554,544893.1278],[189599.7524,544886.4721],[189630.5167,544913.7854],[189630.4711,544887.8476],[189630.8771,544888.5036],[189644.6887,544903.2227],[189657.0484,544915.6918],[189658.1264,544916.8168],[189658.2984,544917.0038],[189658.9854,544917.6288],[189677.423,544934.879],[189718.3121,544969.5983],[189749.9524,544992.6297],[189761.9362,545002.0048],[189768.2021,545007.2238],[189768.7961,545007.6928],[189768.983,545007.8178],[189776.3109,545013.7869],[189777.2329,545014.4739],[189803.5763,545034.0052],[189806.9203,545036.4122],[189815.1541,545043.1303],[189850.6384,545071.8496],[189876.9968,545093.1629],[189878.8718,545094.6939],[189890.0115,545104.381],[189890.8555,545105.069],[189898.3874,545110.0371],[189902.9963,545113.2251],[189927.3708,545131.4754],[189942.1355,545142.3505],[189950.3393,545148.4126],[189969.9319,545163.1948],[189972.6669,545165.2888],[189976.3228,545167.8818],[189983.4476,545173.2569],[190010.8381,545193.8822],[190038.1185,545214.4144],[190045.0554,545219.9455],[190074.7738,545241.3828],[190074.8988,545241.4768],[190085.5086,545249.5389],[190102.8042,545262.6641],[190107.5231,545267.0711],[190119.5539,545274.0393],[190162.772,545308.0717],[190203.5522,545339.1651],[190211.412,545344.8531],[190308.675,545420.5421],[190364.518,545453.4797],[190365.221,545453.8857],[190397.6585,545472.6361],[190398.3764,545473.0431],[190416.2201,545483.5113],[190461.5794,545507.7308],[190476.2501,545515.575],[190484.5165,545490.3873],[190489.2356,545475.9815],[190498.377,545448.1689],[190504.1423,545430.5131],[190506.3303,545423.7942],[190510.6275,545410.5134],[190515.7987,545394.7016],[190520.3149,545380.7328],[190521.393,545377.4199],[190521.284,545377.1079],[190525.7062,545363.389],[190536.5816,545330.7335],[190538.0029,545313.3896],[190537.0661,545300.5147],[190537.0031,545296.1398],[190540.4572,545291.8279],[190540.4414,545279.203],[190540.8004,545278.515],[190541.0354,545278.015],[190548.8794,545270.8902],[190551.0195,545268.5462],[190560.3946,545256.3285],[190561.0666,545254.7345],[190561.8326,545253.1715],[190565.9728,545240.6097],[190569.3789,545229.5468],[190573.1451,545218.391],[190577.8953,545204.3292],[190583.5365,545187.4234],[190586.1616,545179.0475],[190586.2396,545178.7975],[190586.7866,545178.3915],[190591.7237,545170.4857],[190598.3958,545159.8299],[190598.6458,545159.0169],[190606.0374,545120.2363],[190606.7244,545118.1114],[190623.5851,545070.425],[190625.3041,545065.4251],[190621.3505,545044.5502],[190613.2727,545038.4871],[190607.8668,545034.4561],[190602.2108,545033.081],[190572.2112,545025.9556],[190561.1333,545023.2984],[190559.2733,545022.8614],[190559.0703,545022.7994],[190554.0084,545020.2053],[190553.6484,545019.1743],[190548.4617,545004.0804],[190549.6648,545000.9864],[190551.2118,544996.9865],[190551.5398,544996.1115],[190552.0549,544994.7675],[190554.993,544987.1426],[190559.6331,544975.1428],[190565.6803,544959.393],[190565.8524,544954.862],[190566.0405,544949.5181],[190565.8685,544948.6121],[190565.8055,544948.3311],[190565.2595,544947.0811],[190563.5246,544943.0811],[190561.2437,544937.8931],[190552.588,544925.2371],[190548.322,544922.393],[190539.8062,544916.7059],[190539.7592,544916.5809],[190522.1347,544893.7679]]]];
            }
            html = html.replace(/::multiPolygonCoordsSet::/gi, JSON.stringify(coordinates));

            // kml 데이터 불러오기 예제에서 사용하는 값
            if(sampleSrid === '5179') {
            	var kmlText = '<?xml version="1.0" encoding="UTF-8"?><kml xmlns="http://www.opengis.net/kml/2.2"  xmlns:gx="http://www.google.com/kml/ext/2.2" xmlns:kml="http://www.opengis.net/kml/2.2"        xmlns:atom="http://www.w3.org/2005/Atom"><Document><name><![CDATA[odf-emptyLayer-vector1605257609045f9x2gq94bc]]></name><description><![CDATA[odf-emptyLayer-vector1605257609045f9x2gq94bc]]></description><visibility>1</visibility><open>1</open><Style id="area"><LineStyle><color>ff0000ff</color><width>3</width></LineStyle><PolyStyle><color>55ffff55</color></PolyStyle></Style><Folder><description>Polygon</description><visibility>1</visibility><open>0</open><name>Polygon</name><Placemark><styleUrl>#area</styleUrl><MultiGeometry><Polygon><outerBoundaryIs><LinearRing><coordinates>1102899.2389631933,1717024.0130509103 1102999.45025565,1717133.768275982 1102975.5904241127,1716985.8373204507 1103142.6092448737,1716947.661589991 1102985.1343567276,1716899.9419269164 1103018.5381208798,1716732.9231061554 1102927.870761038,1716847.4502975345 1102846.7473338114,1716728.151139848 1102832.4314348889,1716866.5381627642 1102698.81637828,1716966.749455221 1102899.2389631933,1717024.0130509103 </coordinates></LinearRing></outerBoundaryIs></Polygon><Polygon><outerBoundaryIs><LinearRing><coordinates>1103548.2263810078,1717004.9251856806 1103572.0862125452,1717181.4879390565 1103681.8414376169,1717033.5569835252 1103901.35188776,1717071.7327139848 1103786.8246963809,1716938.117657376 1103872.7200899152,1716842.6783312266 1103724.789134384,1716880.8540616864 1103610.2619430048,1716670.8875441581 1103605.4899766974,1716866.538162764 1103385.9795265542,1716871.3101290714 1103548.2263810078,1717004.9251856806 </coordinates></LinearRing></outerBoundaryIs></Polygon><Polygon><outerBoundaryIs><LinearRing><coordinates>1103223.7326721007,1716346.393835251 1103223.7326721007,1716494.324790782 1103319.1719982498,1716351.1658015584 1103424.1552570139,1716379.7975994032 1103385.9795265542,1716260.4984417167 1103467.1029537811,1716198.4628797197 1103323.9439645573,1716212.778778642 1103228.504638408,1716088.707654648 1103199.8728405633,1716212.778778642 1103047.1699187246,1716260.4984417167 1103223.7326721007,1716346.393835251 </coordinates></LinearRing></outerBoundaryIs></Polygon></MultiGeometry></Placemark><Placemark><styleUrl>#area</styleUrl><Polygon><outerBoundaryIs><LinearRing><coordinates>1099959.7077177984,1716260.498441717 1100098.0947407146,1716608.8519821614 1100675.5026639171,1716794.9586681523 1101190.875025123,1716561.132319087 1101114.5235642034,1716308.2181047916 1100661.1867649949,1715859.6532718902 1099773.6010318075,1715530.3875966757 1099353.667996751,1716160.2871492603 1099277.3165358317,1716584.992150624 1099329.8081652136,1717028.785017218 1099730.6533350402,1717119.4523770595 1100083.7788417924,1717024.0130509103 1100145.8144037894,1716713.8352409257 1099959.7077177984,1716260.498441717 </coordinates></LinearRing></outerBoundaryIs></Polygon></Placemark></Folder></Document></kml>';
            	var newKmlText = '<?xml version="1.0" encoding="UTF-8"?><kml xmlns="http://www.opengis.net/kml/2.2"  xmlns:gx="http://www.google.com/kml/ext/2.2" xmlns:kml="http://www.opengis.net/kml/2.2"        xmlns:atom="http://www.w3.org/2005/Atom"><Document><name><![CDATA[odf-emptyLayer-vector16054860583025tj71on5y7q]]></name><description><![CDATA[odf-emptyLayer-vector16054860583025tj71on5y7q]]></description><visibility>1</visibility><open>1</open><Style id="line"><LineStyle><color>ffff55ff</color><width>3</width></LineStyle></Style><Folder><description>LineString</description><visibility>1</visibility><open>0</open><name>LineString</name><Placemark><styleUrl>#line</styleUrl><MultiGeometry><LineString><coordinates>1097802.7789468267,1717572.789176268 1097621.4442271432,1717505.9816479636 1097535.5488336089,1717315.1029956653 1097549.8647325314,1717071.7327139848 1097821.8668120564,1716947.6615899908 1098093.8688915817,1717033.5569835252 1098198.8521503457,1717367.5946250472 1098031.8333295847,1717477.3498501189 1097745.5153511371,1717472.5778838114 1097630.988159758,1717291.243164128 1097645.3040586805,1717133.7682759818 1097812.3228794415,1717086.048612907 1097974.5697338951,1717210.119736901 1097955.4818686654,1717353.278726125 1097817.094845749,1717310.3310293579 1097840.9546772863,1717233.9795684384 </coordinates></LineString><LineString><coordinates>1098590.1533875575,1717768.4397948738 1098337.2391732621,1717749.3519296441 1098284.74754388,1717568.0172099606 1098408.818667874,1717200.5758042862 1098833.523669238,1717114.6804107518 1099105.5257487632,1717319.874961973 1099134.157546608,1717658.6845698026 1098924.1910290797,1717725.492098107 1098509.0299603308,1717653.9126034952 1098370.6429374146,1717458.2619848894 1098518.5738929457,1717281.6992315133 1098809.6638377008,1717286.4711978207 1099014.8583889215,1717525.0695131938 1098876.4713660053,1717630.0527719578 1098628.3291180173,1717534.6134458086 1098614.0132190948,1717381.91052397 1098709.452545244,1717381.91052397 1098823.9797366231,1717510.7536142713 </coordinates></LineString></MultiGeometry></Placemark></Folder></Document></kml>';
            } else {
            	// 5186
            	var kmlText = '<?xml version="1.0" encoding="UTF-8"?><kml xmlns="http://www.opengis.net/kml/2.2"  xmlns:gx="http://www.google.com/kml/ext/2.2" xmlns:kml="http://www.opengis.net/kml/2.2"        xmlns:atom="http://www.w3.org/2005/Atom"><Document><name><![CDATA[odf-emptyLayer-vector1605257609045f9x2gq94bc]]></name><description><![CDATA[odf-emptyLayer-vector1605257609045f9x2gq94bc]]></description><visibility>1</visibility><open>1</open><Style id="area"><LineStyle><color>ff0000ff</color><width>3</width></LineStyle><PolyStyle><color>55ffff55</color></PolyStyle></Style><Folder><description>Polygon</description><visibility>1</visibility><open>0</open><name>Polygon</name><Placemark><styleUrl>#area</styleUrl><MultiGeometry><Polygon><outerBoundaryIs><LinearRing><coordinates>348341.16877107765,317546.76542237087 348440.87701884354,317657.0862041316 348417.7539141214,317508.9562804093 348585.0543068804,317471.606384655 348427.73796672677,317423.0638758287 348462.00533595134,317256.1260568197 348370.71001547645,317370.25448247424 348290.1480204803,317250.4814506946 348275.12362449843,317388.86901050346 348140.9304595561,317488.45640355605 348341.16877107765,317546.76542237087 </coordinates></LinearRing></outerBoundaryIs></Polygon><Polygon><outerBoundaryIs><LinearRing><coordinates>348990.59572056436,317530.95480594813 349013.5738008211,317707.7317597135 349124.13639170415,317560.27854761016 349343.56956894725,317599.5863739955 349229.65860022954,317465.3205207228 349316.08284831326,317370.2657885603 349167.8802819,317407.7124158793 349054.35602576856,317197.05478407396 349048.59055438277,317392.78467551357 348828.93990337395,317396.44730663986 348990.59572056436,317530.95480594813 </coordinates></LinearRing></outerBoundaryIs></Polygon><Polygon><outerBoundaryIs><LinearRing><coordinates>348669.2659557767,316870.431898716 348668.51673572906,317018.4410049893 348764.73153901356,316875.689746514 348869.62526844663,316904.86837356904 348832.03355906974,316785.3128235816 348913.5140380589,316723.65531999257 348770.2068890133,316737.25376687385 348675.3454776023,316612.6337498995 348646.0702133008,316736.62541748927 348493.04496221716,316783.5969286894 348669.2659557767,316870.431898716 </coordinates></LinearRing></outerBoundaryIs></Polygon></MultiGeometry></Placemark><Placemark><styleUrl>#area</styleUrl><Polygon><outerBoundaryIs><LinearRing><coordinates>345403.957613916,316767.96066325175 345540.6529796694,317117.1978647465 346117.42149209057,317306.32679423504 346634.2491010425,317074.987823537 346634.2491010425,317074.987823537 346107.8349196625,316370.45745448215 345221.4505811997,316036.5243986719 344798.1076479622,316664.62763924105 344719.5652615204,317089.16846819373 344769.836580976,317533.4597051921 345170.432592698,317626.2049896196 345524.2267658382,317532.5042893005 345587.86596171546,317222.4779133214 345403.957613916,316767.96066325175</coordinates></LinearRing></outerBoundaryIs></Polygon></Placemark></Folder></Document></kml>';
            	var newKmlText = '<?xml version="1.0" encoding="UTF-8"?><kml xmlns="http://www.opengis.net/kml/2.2"  xmlns:gx="http://www.google.com/kml/ext/2.2" xmlns:kml="http://www.opengis.net/kml/2.2"        xmlns:atom="http://www.w3.org/2005/Atom"><Document><name><![CDATA[odf-emptyLayer-vector16054860583025tj71on5y7q]]></name><description><![CDATA[odf-emptyLayer-vector16054860583025tj71on5y7q]]></description><visibility>1</visibility><open>1</open><Style id="line"><LineStyle><color>ffff55ff</color><width>3</width></LineStyle></Style><Folder><description>LineString</description><visibility>1</visibility><open>0</open><name>LineString</name><Placemark><styleUrl>#line</styleUrl><MultiGeometry><LineString><coordinates>343239.25296839885,318070.0131920804 343058.162007007,318002.25218212046 342973.18870161235,317810.83878650825 342988.74483724905,317567.4140021711 343261.51736649184,317444.65584457427 343533.22646041354,317531.9738455833 343636.5725443422,317866.7178348983 343468.91046553955,317975.68435212784 343182.46711830236,317969.4594890284 343068.79870519694,317787.44995027414 343083.91975253145,317629.965373028 343251.2674921944,317583.06679473707 343412.9706261575,317708.0245584695 343393.1476182272,317851.16164215433 343254.9058702591,317807.49050740525 343279.1649192402,317731.2200348791 </coordinates></LineString><LineString><coordinates>344026.0477638006,318269.75485473574 343773.0979927789,318249.3757334411 343721.4975513258,318067.6802999702 343847.49485989986,317700.67528993613 344272.85712596856,317616.88625244866 344543.96219874086,317823.56603787665 344570.8926629524,318162.6981125325 344360.4777684243,318228.47693229414 343945.46210058685,318154.7567917441 343807.9938438578,317958.3028224513 343956.8965529421,317782.3971012619 344248.1146137911,317788.646085127 344452.20791466313,318028.40873840754 344313.21661995805,318132.7459050735 344065.4280110307,318035.99958738516 344051.87817209994,317883.14426423487 344147.3674284466,317883.6277241969 344261.3018687884,318013.1183957111 </coordinates></LineString></MultiGeometry></Placemark></Folder></Document></kml>';
            }
            html = html.replace(/::kmlText1::/gi, kmlText);
        	html = html.replace(/::kmlText2::/gi, newKmlText);

            // 레이어 추가/삭제 예제에서 사용하는 값
            if(sampleSrid === '5179') {
            	var wmtsCenterCoord =[993030.9322298958, 1837208.6851813043];
            	// 중심 좌표값
            	var centrCoord1 = [ 949506.7679930615, 1933677.0992435084 ];
            	var centrCoord2 = [ 993030.9322298958, 1837208.6851813043 ];
            	var centrCoord3 = [ 949000.9395644, 1937795.902662 ];

            	// geoJson 레이어 좌표값
            	var pointLayerCoords = [ 947433.34863240704, 1938772.515392246 ];
            	var lineStringLayerCoords = [
						[ 947941.44863240704, 1938165.615392246 ],
						[ 947761.4213161081, 1938904.2663995156 ]
					];
            	var polygonLayerCoords = [
						[
							[ 948034.6163872102, 1938934.7622466995 ],
							[ 948507.0410516487, 1938352.5823571896 ],
							[ 948764.7272322515, 1938681.8480324042 ],
							[ 948034.6163872102, 1938934.7622466995 ]
						],
					];
            	var multiLineStringLayerCoords = [
						[
							[ 948273.6163872102, 1937684.7622466995 ],
							[ 948435.0410516487, 1937202.5823571896 ]
						],
						[
							[ 947600.7272322515, 1937531.8480324042 ],
							[ 947781.7272322515, 1936744.8480324042 ]
						],
					];
            	var multiPolygonLayerCoords = [
						[
							[
								[ 949389.8548185286, 1938996.7978086965 ],
								[ 949256.2397619198, 1938109.2120755091 ],
								[ 950506.4949344742, 1938729.567695479 ],
								[ 949389.8548185286, 1938996.7978086965 ]
							]
						],
						[
							[
								[ 949867.0514492746, 1937522.2602196916 ],
								[ 949442.3464479108, 1936677.6221832712 ],
								[ 950697.3735867726, 1936639.4464528116 ],
								[ 950754.6371824621, 1936400.8481374385 ],
								[ 949867.0514492746, 1937522.2602196916 ]
							]
						],
					];
            } else {
            	var wmtsCenterCoord =[237805.02689211597,437223.62942290655];
            	// 중심 좌표값
            	var centrCoord1 = [ 193757.90979872074, 533500.1414559416 ];
            	var centrCoord2 = [ 237805.0268921159, 437223.6294229054 ];
            	var centrCoord3 = [ 193230.05260357965, 537617.7232242593 ];

            	// geoJson 레이어 좌표값
            	var pointLayerCoords = [ 191656.72376444907, 538586.361370021 ];
            	var lineStringLayerCoords = [
						[ 192168.2244630141, 537981.9439016379 ],
						[ 191984.21292240333, 538719.900346032 ]
					];
            	var polygonLayerCoords = [
						[
							[ 192257.3426558891, 538751.8571505237 ],
							[ 192733.02462497575, 538169.4714438635 ],
							[ 192989.05440125664, 538502.728928784 ],
							[ 192257.3426558891, 538751.8571505237 ]
						],
					];
            	var multiLineStringLayerCoords = [
						[
							[ 192503.0616316357, 537502.6835821404 ],
							[ 192667.1021656067, 537021.1896764012 ]
						],
						[
							[ 191830.74614380737, 537346.1444391314 ],
							[ 192015.9861092515, 536559.8269382792 ]
						],
					];
            	var multiPolygonLayerCoords = [
						[
							[
								[ 193612.7318996982, 538821.1086728972 ],
								[ 193483.78057418767, 537932.4986993872 ],
								[ 194731.18761190848, 538559.7108477298 ],
								[ 193612.7318996982, 538821.1086728972 ]
							]
						],
						[
							[
								[ 194097.92419074813, 537348.5799549741 ],
								[ 193677.5501501346, 536501.3882261913 ],
								[ 194933.22632586374, 536469.8577990088 ],
								[ 194991.77624693274, 536231.4782485802 ],
								[ 194097.92419074813, 537348.5799549741 ]
							]
						],
					];
            }



			const airOptions = BasemapUtils.getAirBasemapOption(sampleBasemap, {
				url: sampleBasemap === 'BAROEMAP' ? baroEMapAirURL : vWorldURL
			});
			html = html.replace(/::basemapAir::/, airOptions.basemap);
			html = html.replace(/::basemapAirUrlDefine::/, airOptions.urlDefine);

			html = html.replace(/::TifUrl::/gi, TifUrl);
			html = html.replace(/::PbfUrl::/gi, PbfUrl);
			html = html.replace(/::developerUrl::/gi, DeveloperUrl);
			html = html.replace(/::proxyOption::/gi, proxyOption);
			html = html.replace(/::wmtsCenterCoord::/gi, JSON.stringify(wmtsCenterCoord));
			html = html.replace(/::centrCoord1::/gi, JSON.stringify(centrCoord1));
			html = html.replace(/::centrCoord2::/gi, JSON.stringify(centrCoord2));
			html = html.replace(/::centrCoord3::/gi, JSON.stringify(centrCoord3));
			html = html.replace(/::pointLayerCoords::/gi, JSON.stringify(pointLayerCoords));
			html = html.replace(/::lineStringLayerCoords::/gi, JSON.stringify(lineStringLayerCoords));
			html = html.replace(/::polygonLayerCoords::/gi, JSON.stringify(polygonLayerCoords));
			html = html.replace(/::multiLineStringLayerCoords::/gi, JSON.stringify(multiLineStringLayerCoords));
			html = html.replace(/::multiPolygonLayerCoords::/gi, JSON.stringify(multiPolygonLayerCoords));

			html = html.replace(/"::viewOption::"/, viewOption);
			html = html.replace(/"::zoomOption::"/, zoomOption);
			html = html.replace(/::polygonLayer1::/, polygonLayer1);
			html = html.replace(/::polygonLayer2::/, polygonLayer2);
			html = html.replace(/::pointLayer::/gi, pointLayer);
			html = html.replace(/::lineLayer::/, lineLayer);
			html = html.replace(/::testLineLayer1::/gi, testLineLayer1);
			html = html.replace(/::wmtsLayer::/, wmtsLayer);
			html = html.replace(/::hotspotLayer::/, hotspotLayer);
			html = html.replace(/::srid::/gi, sampleSrid);
			html = html.replace(/::baroEMapURL::/gi, baroEMapURL);
			html = html.replace(/::baroEMapAirURL::/gi, baroEMapAirURL);
			html = html.replace(/::baroEMapKey::/gi, baroEMapKey ? `
					baroEMapKey : '${baroEMapKey}',` : '');

			html = html.replace(/::vWorldURL::/gi, vWorldURL);
			html = html.replace(/::vWorldApiKey::/gi, vWorldApiKey);
			html = html.replace(/::vWorldDomain::/gi, vWorldDomain);
			html = html.replace(/::nsdiApiKey::/gi, nsdiApiKey);

			html = html.replace(/::basemapType::/gi, basemapType);
			html = html.replace(/::basemap_base::/gi, basemap_base);
			html = html.replace(/::basemap_air::/gi, basemap_air);
			html = html.replace(/::basemap_white::/gi, basemap_white);
			html = html.replace(/::basemap_color::/gi, basemap_color);
			html = html.replace(/::basemap_etc::/gi, basemap_etc);

			if (input) {
				html = html.replace("}\";", "	, baroEMapURL: 'http://map.ngii.go.kr/openapi/Gettile.do' \n		, baroEMapKey: '3C0B5534B557FF993B74EE0E86CA22DD' \n	};");
			} else {
				html = html.replace('}";', '};');
			}

			sampleHtml = html.replace(/::OdfUrl::/gi, OdfUrl)
								.replace(/::APIGW::/gi, APIGW)
								.replace(/::DeveloperUrl::/gi, DeveloperUrl)
								.replace(/::WfsAPI::/gi, WfsAPI)
								.replace(/::WmsAPI::/gi, WmsAPI)
								.replace(/::WmtsAPI::/gi, WmtsAPI)
								.replace(/::crtfckey::/gi, crtfckey)
				                .replace(/::kakaoAppKey::/gi, kakaoAppKey);

			return sampleHtml
}

function appendStylesheets() {
	const styleSheets = Array.from(document.styleSheets);

	return styleSheets
		.filter(ss => ss.href)
		.map(ss => `<link type="text/css" rel="stylesheet" href="${ss.href}">`)
		.join('\n');
}

function updateUI(nowMenu, sampleHtml, target) {
	$("#sample-code").text(sampleHtml);
	Prism.highlightAll();
	resetClipboard();
	menu.scrollToId(target, true);
	eventShowLive();
	$("#sample-title").text(nowMenu.name);
	$("#sample-desc").text(nowMenu.desc);
	menu.menuSelected(nowMenu.name);
}

function initializeMapContainer(iDoc) {
	return new Promise((resolve) => {
		window.setTimeout(() => {
			// 메인 맵과 일반 맵 요소 선택
			const divMainMaps = iDoc.querySelectorAll('.mainMap .odf-map');
			const maps = iDoc.getElementsByClassName("odf-map");

			// 타겟 결정 (메인맵이 하나면 메인맵, 아니면 일반 맵)
			const target = (divMainMaps.length === 1 ? divMainMaps[0] : maps);

			// 맵 요소들의 높이 설정
			// if (target.length) {
			// 	// 여러 맵이 있는 경우
			// 	Array.from(target).forEach(map => {
			// 		setMapHeight(map);
			// 	});
			// } else {
			// 	// 단일 맵인 경우
			// 	setMapHeight(target);
			// }

			// 추가 스타일 설정을 위한 지연 실행
			window.setTimeout(() => {
				// body overflow 설정
				iDoc.querySelector('body').style.overflow = 'hidden';

				// iframe 컨테이너 높이 설정
				$("#sample-container")[0].style.height = iDoc.body.scrollHeight + 'px';

				// divide map 컨테이너 높이 설정
				const divideMapContainer = iDoc.getElementsByClassName("odf-dividemap-container")[0];
				if (divideMapContainer) {
					divideMapContainer.style.height = '550px';
				}

				resolve();
			}, 100);
		}, 100);
	});
}

// function setMapHeight(mapElement) {
// 	if (!mapElement) return;
//
// 	// 맵 컨테이너 높이 설정
// 	mapElement.style.height = "550px";
//
// 	// viewport 높이 설정
// 	const viewport = mapElement.querySelector(".ol-viewport");
// 	if (viewport) {
// 		viewport.style.height = "550px";
// 	}
// }
