	var target = document.getElementsByClassName('onoffOnlyBtn toggle');
	for(var i=0; i<target.length;i++){
		target[i].classList.forEach(function(e){
			if(e.includes('grp')){
				target[i].addEventListener('click',function(){
					toggleFunc(this, e);
				});
			}
		});
	}
	function toggleFunc (element, group){
		for(k=0;k<target.length;k++){
			target[k].classList.forEach(function(e){
				if(e.includes(group)){
					target[k].classList.remove('active');
				}
			});
		};
		var classlist = element.classList;

		if(flag){
			element.classList.remove('active');
		}else{
			element.classList.add('active');
		}
		flag = false;
	};
	var targetOne = document.getElementsByClassName('onoffBtn toggle');
	var flag = false;
	for(var i=0; i<targetOne.length;i++){


		
				targetOne[i].addEventListener('click',function(){
					toggleFuncOne(this);
				});
		
	}
	function toggleFuncOne (element, group){
		var classlist = element.classList;
		classlist.forEach(function(e){
			if(e.includes('active')){
				flag = true;
			}
		});
		if(flag){
			element.classList.remove('active');
		}else{
			element.classList.add('active');
		}
		flag = false;
	};


