/**
 * 
 */
var mapTarget;
var Json;
var tocJson;
var array = [];
var count = 0;
var flag = false;
$(document).ready(function() {
	wizardChangeCode();
    
	$('input:checkbox').change(function(){
		wizardChangeCode(this.id);
	});
	
	$(".mapsize").change(function(e) {
		wizardChangeCode();
	});
	
	$("#mapLevel").change(function(e) {
		wizardChangeCode(this.id);
	});
	$('#tocAddLayer').click(function(e){
		if($("#tocTypControl").is(":checked") === false){
			alert('Toc를 먼저 생성해주세요.');
		}
		else{
		count += 1;
		flag = true;
		wizardChangeCode("tocTypControl");
		flag = false;
		}
	});
	$('input:radio[name=tocLevel]').change(function(e){
		wizardChangeCode("tocTypControl");
	});
	$("#layerType").change(function(e) {
		$('input:checkbox[id=styleTypControl]').attr("checked",false);
		if ($("#layerTypControl").is(":checked")) {
			wizardChangeCode("layerTypControl");
		}
	});
	
	$("#serverLayerType").change(function(e) {
		$('input:checkbox[id=styleTypControl]').attr("checked",false);
		if ($("#layerTypControl").is(":checked")) {
			wizardChangeCode("layerTypControl");
		}
	});
	$('#textUseYn').click(function(){
		if($('#styleTypControl').is(":checked") == false){
			$('#textUseYn').attr("checked",false);
			alert('스타일을 먼저 생성해주세요');
		}else{
			if ($("#textUseYn").is(":checked")){
				$('#textOptions').show();
			}
		}
	});

	
	$('.styleChangeOption').change(function(e){
		wizardChangeCode("styleTypControl");
	});
	
	$('#markerType').change(function(e){
		wizardChangeCode("markerTypControl");
	});
	
	$('#serverType').change(function(e){
		$('input:checkbox[id=layerTypControl]').attr("checked",false);
		$('input:checkbox[id=styleTypControl]').attr("checked",false);
		 if($('#serverType option:selected')[0].value == 'GeoServer'){
			 $('#layerType option').prop('selected', function(){
				 return this.defaultSelected;
			 })
			 
			 $(this).removeClass("active");
			 $('#geoServerOpt').show();
			 $('#layerType').hide();
		 }
		 else if($('#serverType option:selected')[0].value == 'GeoJson'){
			 //$('#geoServerUrl').val('http://100.10.50.111:8000/map/geoserver');
			 $('#layerName').val('nsid_dev:L100000254');
			 $('#serverLayerType option').prop('selected', function(){
				 return this.defaultSelected;
			 })
			 
			 $(this).addClass('active');
			 $('#layerType').show();
			 $('#geoServerOpt').hide();
		 }
	});

//	// 메뉴 접기
//	$('.close').click(function(e) {
//		$(this).hide();
//		$(this).prev().show();
//		$(this).parent().parent().find('.control').hide('fast');
//	});
//	
//	// 메뉴 펼치기
//	$('.open').click(function(e) {
//		$(this).hide();
//		$(this).next().show();
//		$(this).parent().parent().find('.control').show('fast');
//	});
	
	$('.listArea.type2 .titList').on({
        "click":function(){
    		if($(this).siblings('.menuList')[0] == undefined){
    		   $(this).parent().siblings('.menuList').toggle();
               $(this).toggleClass('active');
               if($(this).parent().siblings('.menuList')[0].style.display == 'none'){
            	   $('#textOptions').hide();
               } else {
            	   $('#textOptions').show();
               }
    		} else {
    			$(this).siblings('.menuList').toggle();
                $(this).toggleClass('active');
    		}
        }
    });
	
});

mapLevelChange = () =>{
	let mapLevel = $('#mapLevel').val();
	
	if(parseInt(mapLevel)>23 || parseInt(mapLevel)<1){
		alert('지도 레벨은 7-23 레벨 사이로 입력해주세요');
	}
	else{
	let setZoomCode = '	map.setZoom';
	$('#wizard-content #wizard_script').append("\n");
	$('#wizard-content #wizard_script').append('	//지도 줌레벨 설정');
	$('#wizard-content #wizard_script').append("\n");
	$('#wizard-content #wizard_script').append(setZoomCode+'(' + mapLevel + ');');
	$('#wizard-content #wizard_script').append("\n");
	}

}
var changehtml;
var sethtml;
wizardChangeCode = (targetId = null, bools = true) =>{
	let url = rootpath + "/samplehtml/widget/wizard.html"
	//크기 셋팅
	let width = $('#mapWidth').val();
	let height = $('#mapHeight').val();
	
	$.ajax({
		url : url,
		type : 'GET',
		cache: false,
		async : false,		// $.get(async: true) 로 부르는 경우 clipboard.js 동작 안함
		success : function(html) {
			sethtml = html.replace(/::OdfUrl::/gi, OdfUrl)
							.replace(/::DeveloperUrl::/gi, DeveloperUrl);
			$('#wizard-content').html(sethtml);
			$('#wizard-content #wizard_script').append("\n");
			if ($("#divideMapTypControl").is(":checked") == false) {
			$('#wizard-content #wizard_script').append('\n	//map size 조정');
			$('#wizard-content #wizard_script').append('\n	document.getElementsByClassName("odf-map")[0].style.width = "'+width+'px";');
			$('#wizard-content #wizard_script').append('\n	document.getElementsByClassName("odf-map")[0].style.height = "'+height+'px";');
			$('#wizard-content #wizard_script').append('\n	map.updateSize();\n');
			}else { 
				$('#wizard-content').css('height',height);
				$('#wizard-content #wizard_script').append('\n	document.getElementsByClassName("odf-map")[0].parentElement.style.maxWidth = "'+width+'px";');
				$('#wizard-content #wizard_script').append('\n	document.getElementsByClassName("odf-map")[0].parentElement.style.maxHeight = "'+height+'px";');
			}
			if($('#tocTypControl').is(":checked")==false){
				array = [];
				count=0;
			}
			Array.from($('input:checkbox:checked')).forEach(function(itm, idx){
				
				if($('input:checkbox[id='+itm.id+']').is(":checked")==true){
					
					createCode(itm.id.replace('TypControl', ''), bools);
				}
			});
			mapLevelChange();
			changehtml =  $("#wizard-content").html();
			$("#wizard-content").html(changehtml);
			$("#wizard-code").text(changehtml);	
			Prism.highlightAll();
			resetClipboard();
			if($('input:checkbox[id="mousePositionTypControl"]').is(":checked")){
				$('#coordDiv').show();
			}
			else{
				$('#coordDiv').hide();
			}
		},
		error : function(xhr, status, err) {
			console.log(xhr);
			console.log(status);
			console.log(err);
		}
	});
}

	var html = '<iframe src="' + rootpath + '/live"></iframe>';
	$("#showLive").off("click");
	$("#showLive").click(function() {
		modal.showModal("modal-live", html, changehtml);
	});


createCode = (targetId, bools = true) => {
	let type = typeCheck(targetId);
	let declaration = null;
	let department = null;
	let variable = null;
	let subDepartment = null;
	let stylevariable = null;
	
	if (type=== 'control') { // 툴바 컨트롤러 대상 한정
		declaration = "	var "+targetId+"Control = new odf."+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"Control();";
		if(targetId == 'swiper'){
			declaration = "	var swiperLayerList = new odf.BasemapControl();\n" +
					"	var "+targetId+"Control = new odf."+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"Control({\n" +
					"	layers: [swiperLayerList.getBaseLayer('eMapBasic'),swiperLayerList.getBaseLayer('eMapAIR')],\n" +
					"	size : '200'," +
					"});";
		}
		else if(targetId == 'mousePosition'){
			declaration = "	var " + targetId+"Control = new odf."+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"Control({\n" +
		        "	//특정 element에 좌표값 표시 시\n"+
				"	element: document.querySelector('#coordDiv'),\n" +
				"	   //callback: function (position) {\n" +
				"	   // console.log(position);\n" +
				"  //},\n" +
		      "	});";
		}
		else if(targetId == 'divideMap'){
			declaration = "	var " + targetId + "Control = new odf."+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"Control({\n" +
			"	dualMap : [\n" +
			"	{position: 1,\n" +
			"	mapOption : {\n"+
			"	basemap : {baroEMap : ['eMapBasic','eMapAIR']},\n"+
			"	},\n"+
			"	controlOption: {//사용할 컨트롤 지정\n" +
			"	overviewmap: true,\n"+
		    "			},\n"+
		    "      },\n"+
		    "	],\n"+
		    "    quadMap: [\n"+
		    "      {\n"+
		    "        // position: 1, //지정안하면 기본 1\n"+
		    "        mapOption: {\n"+
		    "          basemap: {baroEMap : ['eMapBasic','eMapAIR']},\n"+
		    "        },\n"+
		    "      },\n"+
		    "      {\n"+
		    "        //position: 2, //지정안하면 기본 3\n"+
		    "        mapOption: {\n"+
		    "          basemap: {baroEMap : ['eMapBasic','eMapAIR']} ,\n"+
		    "        },\n"+
		    "      },\n"+
		    "      {\n"+
		    "        //position: 4,//지정안하면 기본 4\n"+
		    "        mapOption: {\n"+
		    "          basemap: {baroEMap : ['eMapBasic','eMapAIR']} ,\n"+
		    "        },\n"+
		    "        controlOption: {//사용할 컨트롤 지정\n"+
		    "         download: true,\n"+
		    "        },\n"+
		    "      },\n"+
		    "    ],\n"+
		    "  });";
		}
		department = "	" + targetId+"Control.setMap(map);";

		$('#wizard-content #wizard_script').append("\n");
		
		$('#wizard-content #wizard_script').append(declaration);
		$('#wizard-content #wizard_script').append("\n");
		
		$('#wizard-content #wizard_script').append(department);
		$('#wizard-content #wizard_script').append("\n");
	}
	else if(type==='map'){// 맵 드래그,휠 설정
		if(targetId == 'wheel'){
			targetId = 'zoom';
		}
		if(targetId == 'resize'){
			targetId = 'resiz';
			department = "	map.set"+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"able(true);";
		}else{
			department = "	map.set"+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"able(false);";
		}
		$('#wizard-content #wizard_script').append("\n");
		
		$('#wizard-content #wizard_script').append(department);
		$('#wizard-content #wizard_script').append("\n");

	} else if (type === 'layer') {
		
		
		let _selected = $('#serverType option:selected')[0].id;
		
		if (_selected === 'geoserver') {
			let _geoServerUrl = $('#geoServerUrl').val();//+'/'+$('#serverLayerType').val();
			let _layerName = $('#layerName').val();
			let _serverLayerType = $('#serverLayerType').val();
			
			variable = "	var layer = odf.LayerFactory.produce('"+_selected+"',{\n"+
			"	method: 'get',\n"+
			"	server:'"+_geoServerUrl+"',\n"+
			"	layer:'"+_layerName+"',\n"+
			"	service:'"+_serverLayerType+"',\n"+
			"	});";
			$('#wizard-content #wizard_script').append("\n");
			$('#wizard-content #wizard_script').append(variable);
			$('#wizard-content #wizard_script').append("\n");
			subDepartment = "	layer.setMap(map);";
			$('#wizard-content #wizard_script').append("\n");
			$('#wizard-content #wizard_script').append(subDepartment);
			
			/*mapTarget = "	layer.fit();// 레이어가 모두 보이는 extent 값으로 화면 조정";*/ 
			$('#wizard-content #wizard_script').append("\n");
			/*$('#wizard-content #wizard_script').append(mapTarget);*/
			$('#wizard-content #wizard_script').append("\n");
					
		} else if (_selected === 'geojson') {
			let _layerType = $('#layerType option:selected')[0].id;
			
			if (_layerType === 'point') {
				variable = "	var geoJsonObject = "
				+" {\n"
				+"			type: 'FeatureCollection',\n"
				+"			features: [\n"
				+"			{\n"
				+"				type: 'Feature',\n"
				+"				geometry: {\n"
				+"					type: 'Point',\n"
				+"					coordinates: [947433.34863240704, 1938772.515392246]\n"
				+"				},\n"
				+"				properties: {\n"
				+"					name: 'Point'\n"
				+"					}\n"
				+"				}\n"
				+"			]\n"
				+" 	};";
				
				$('#wizard-content #wizard_script').append("\n");
				$('#wizard-content #wizard_script').append(variable);
			} else if (_layerType === 'linestring') {
				variable = "	var geoJsonObject = "
					+" {\n"
					+"			type: 'FeatureCollection',\n"
					+"			features: [\n"
					+"				{\n"
					+"					type: 'Feature',\n"
					+"					geometry: {\n"
					+"						type: 'LineString',\n"
					+"						coordinates: [\n"
					+"			  				[947941.44863240704, 1938165.615392246],\n"
					+"			   				[947761.4213161081, 1938904.2663995156],\n"
					+"			  			],\n"
					+"			 		},\n"
					+"			  		properties: {\n"
					+"			    		name: 'LineString',\n"
					+"			   		}\n"
					+"			   }\n"
					+"			]\n"
					+" 	};";	
					
					$('#wizard-content #wizard_script').append("\n");
					$('#wizard-content #wizard_script').append(variable);
			} else if (_layerType === 'polygon') {
				variable = "	var geoJsonObject = "
					+" {\n"
					+"			type: 'FeatureCollection',\n"
					+"			features: [\n"
					+"				{\n"
					+"					type: 'Feature',\n"
					+"					geometry: {\n"
					+"			  			type: 'Polygon',\n"
					+"			  			coordinates: [\n"
					+"			    			[\n"
					+"			      				[948034.6163872102, 1938934.7622466995],\n"
					+"			 					[948507.0410516487, 1938352.5823571896],\n"
					+"								[948764.7272322515, 1938681.8480324042],\n"
					+"			 					[948034.6163872102, 1938934.7622466995],\n"
					+"			 				],\n"
					+"			    		],\n"
					+"					},\n"
					+"			 		properties: {\n"
					+"			  			name: 'Polygon',\n"
					+"			  		}\n"
					+"			 	}\n"
					+"			]\n"
					+" 	};";
					
					$('#wizard-content #wizard_script').append("\n");
					$('#wizard-content #wizard_script').append(variable);
			}
			
			$('#wizard-content #wizard_script').append("\n");
			declaration = "	var layer = odf.LayerFactory.produce('" + _selected + "', {data: geoJsonObject});";
			$('#wizard-content #wizard_script').append(declaration);
			$('#wizard-content #wizard_script').append("\n");
			subDepartment = "	layer.setMap(map);\n" + 
							"	map.setCenter([949000.9395644, 1937795.902662])"; 
			$('#wizard-content #wizard_script').append(subDepartment);
			
			
		}
		
		
	} else if (type === 'style') {
		//두번 이상 val값을 불러올 때에는 변수화하고 한번은 바로 val값 삽입
		let _styleType = $('#styleType option:selected')[0].id;
		let _lineStyle = $('#lineStyleSelectBox option:selected').val();
		let _lineCap = $('#lineCapSelectBox option:selected').val();
		let _lineJoin = $('#lineJoinSelectBox option:selected').val();
		let _lineThick = $('#lineThickInputBox').val();
		let _lineColorRed = $('#lineColorRedInputBox').val();
		let _lineColorGreen = $('#lineColorGreenInputBox').val();
		let _lineColorBlue = $('#lineColorBlueInputBox').val();
		let _lineColorOpacity = $('#lineColorOpacityInputBox').val();
		let _fillColorRed = $('#fillColorRedInputBox').val();
		let _fillColorGreen = $('#fillColorGreenInputBox').val();
		let _fillColorBlue = $('#fillColorBlueInputBox').val();
		let _fillColorOpacity = $('#fillColorOpacityInputBoxBasic').val();
		let _text = $('#text').val();
		let _font = $('#font option:selected').val();
		let _fontSize = $('#fontSize').val();
		let _fontFamily = $('#fontFamily option:selected').val();
		let _fontColorRed = $('#fontColorRedInputBox').val();
		let _fontColorGreen = $('#fontColorGreenInputBox').val();
		let _fontColorBlue = $('#fontColorBlueInputBox').val();
		let _fontColorOpacity = $('#fontColorOpacityInputBox').val();
		let _fontLineColorRed = $('#fontLineColorRedInputBox').val();
		let _fontLineColorGreen = $('#fontLineColorGreenInputBox').val();
		let _fontLineColorBlue = $('#fontLineColorBlueInputBox').val();
		let _fontLineColorOpacity = $('#fontLineColorOpacityInputBox').val();
		let _textOffsetX = $('#textOffsetX').val();
		let _textOffsetY = $('#textOffsetY').val();
		let _textPlacement = $('#textPlacement option:selected').val();
		let _textRotation = $('#textRotation').val();
		
	
		if(_styleType == 'point'){
			stylevariable = "\n	var pointStyle =odf.StyleFactory.produce({\n"
				+"		image : {\n"
				+"			circle : {\n"
				+"				radius:"+$('#pointRadius').val()+",//크기\n"
				+"				fill:{\n"
				+"					color: ["+_fillColorRed+","+_fillColorGreen+","+_fillColorBlue+","+_fillColorOpacity+"]//채우기 색\n" //_fillColor
				+"				},//채우기\n"
				+"				stroke: {//윤곽선\n"
				+"					color: ["+_lineColorRed+","+_lineColorGreen+","+_lineColorBlue+","+_lineColorOpacity+"],//테두리 색\n" //_lineColor
				+"					width:"+ _lineThick +",//굵기\n" //_lineThick
				+"					lineDash:"+ _lineStyle +",//점선 설정\n" //_lineStyle
				+"					lineCap:'"+ _lineCap +"',//선모양 설정\n" //_lineCap
				+"					lineJoin:'"+ _lineJoin +"',//선의 접선 설정\n" //_lineJoin
				+"				},\n"
				+"			}\n"
				+"		},\n";
				if($('input:checkbox[id=textUseYn]').is(':checked') == true){
					$('#textOptions').show();
					stylevariable+=
					"		text :{\n"
					+"			text:'"+_text+"',\n"
					+"			offsetX :"+ _textOffsetX + ",\n"
					+"			offsetY :"+ _textOffsetY + ",\n"
					+"			rotation : (Math.PI/180)*"+ _textRotation + ",\n"
					if($('input:checkbox[id=obliqueYn]').is(':checked') == true){
						stylevariable+=
							"			font : '"+_font+" oblique "+_fontSize+"px "+_fontFamily+"',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)\n"
					}else{
						stylevariable+=
							"			font : '"+_font+" "+_fontSize+"px "+_fontFamily+"',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)\n"
					}
					stylevariable+=
					"			fill : {color:["+_fontColorRed+","+_fontColorGreen+","+_fontColorBlue+","+_fontColorOpacity+"]},\n"
					+"			stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x\n"
					+"				color:["+_fontLineColorRed+","+_fontLineColorGreen+","+_fontLineColorBlue+","+_fontLineColorOpacity+"],\n"
					+"			},\n"		
//					+"			padding : [0.5,0.5,0.5,0.5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용\n"
//					+"			backgroundStroke : {color:'black'},//placement :'line' 일경우 미적용\n"
//					+"			backgroundFill : {color:'white'},//placement :'line' 일경우 미적용\n"
//					+"			scale : 1, //텍스트 크기를 정해진 값의 n배로 셋팅\n"
//					+"			rotateWithView: true//지도가 회전할때 텍스트도 적절하게 회전할지 여부\n"
					+"		}\n"
				}else{
					$('#textOptions').hide();
				}
				stylevariable+="	});\n"
				+"	layer.setStyle(pointStyle);";	
		}
		else if(_styleType == 'linestring'){
			stylevariable = "\n	var lineStyle =odf.StyleFactory.produce({\n"
				+"		stroke : {\n"
				+"			color:["+_lineColorRed+","+_lineColorGreen+","+_lineColorBlue+","+_lineColorOpacity+"],\n"
				+"			lineCap : 'square',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))\n"
				+"			lineJoin : 'round',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))\n"
				+"			lineDash : "+ _lineStyle +",//점선의 간격 크기\n"
				+"			lineCap:'"+ _lineCap +"',//선모양 설정\n" //_lineCap
				+"			lineJoin:'"+ _lineJoin +"',//선의 접선 설정\n" //_lineJoin
				+"			width:"+ _lineThick +"\n"
				+"		},\n"
				if($('input:checkbox[id=textUseYn]').is(':checked') == true){
					$('#textOptions').show();
					stylevariable+=
					"		text :{\n"
					+"			text:'"+_text+"',\n"
					+"			offsetX :"+ _textOffsetX + ",\n"
					+"			offsetY :"+ _textOffsetY + ",\n"
					+"			rotation : (Math.PI/180)*"+ _textRotation + ",\n"
					if($('input:checkbox[id=obliqueYn]').is(':checked') == true){
						stylevariable+=
							"			font : '"+_font+" oblique "+_fontSize+"px "+_fontFamily+"',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)\n"
					}else{
						stylevariable+=
							"			font : '"+_font+" "+_fontSize+"px "+_fontFamily+"',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)\n"
					}
					stylevariable+=
					"			fill : {color:["+_fontColorRed+","+_fontColorGreen+","+_fontColorBlue+","+_fontColorOpacity+"]},\n"
					+"			stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x\n"
					+"				color:["+_fontLineColorRed+","+_fontLineColorGreen+","+_fontLineColorBlue+","+_fontLineColorOpacity+"],\n"
					+"			},\n"
					+"			placement :'" + _textPlacement + "',\n"
					+"			overflow : true,\n"
//					+"			placement :'line',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지\n"
//					+"			maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용\n"
//					+"			overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부\n"
//					+"			scale : 1, //텍스트 크기를 정해진 값의 n배로 셋팅\n"
//					+"			rotateWithView: true//지도가 회전할때 텍스트도 적절하게 회전할지 여부\n"
					+"		}\n"
				}else{
					$('#textOptions').hide();
				}
				stylevariable+="		});\n"
				+"	layer.setStyle(lineStyle);\n";
		}
		else if(_styleType == 'polygon'){
			stylevariable = "\n	var polygonStyle =odf.StyleFactory.produce({\n"
				+"		fill : {\n"
				+"			color:["+_fillColorRed+","+_fillColorGreen+","+_fillColorBlue+","+_fillColorOpacity+"]\n"
				+"		},\n"
				+"		stroke : {\n"
				+"			color:["+_lineColorRed+","+_lineColorGreen+","+_lineColorBlue+","+_lineColorOpacity+"],\n"
				+"			lineCap:'"+ _lineCap +"',//선모양 설정\n" //_lineCap
				+"			lineJoin:'"+ _lineJoin +"',//선의 접선 설정\n" //_lineJoin
				+"			lineDash : "+ _lineStyle +",//점선의 간격 크기\n"
				+"			width:"+ _lineThick +"\n"
				+"		},\n"
				if($('input:checkbox[id=textUseYn]').is(':checked') == true){
					$('#textOptions').show();
					stylevariable+=
					"		text :{\n"
					+"			text:'"+_text+"',\n"
					+"			offsetX :"+ _textOffsetX + ",\n"
					+"			offsetY :"+ _textOffsetY + ",\n"
					+"			rotation : (Math.PI/180)*"+ _textRotation + ",\n"
					if($('input:checkbox[id=obliqueYn]').is(':checked') == true){
						stylevariable+=
							"			font : '"+_font+" oblique "+_fontSize+"px "+_fontFamily+"',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)\n"
					}else{
						stylevariable+=
							"			font : '"+_font+" "+_fontSize+"px "+_fontFamily+"',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)\n"
					}
					stylevariable+=
					"			fill : {color:["+_fontColorRed+","+_fontColorGreen+","+_fontColorBlue+","+_fontColorOpacity+"]},\n"
					+"			stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x\n"
					+"				color:["+_fontLineColorRed+","+_fontLineColorGreen+","+_fontLineColorBlue+","+_fontLineColorOpacity+"],\n"
					+"			},\n"
					+"			placement :'" + _textPlacement + "',\n"
					+"			overflow : true,\n"
//					+"			padding : [5,5,5,5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용\n"
//					+"			placement :'point',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지\n"
//					+"			maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용\n"
//					+"			overflow : false,//placement :'line' 일 경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부\n"
//					+"			scale : 0.8, //텍스트 크기를 정해진 값의 n배로 셋팅\n"
//					+"			rotateWithView: true//지도가 회전할 때 텍스트도 적절하게 회전할지 여부\n"
					+"		}\n"
				}else{
					$('#textOptions').hide();
				}
				stylevariable+="	});\n"
				+"	layer.setStyle(polygonStyle);\n";
		}
			
		if($('input:checkbox[id=layerTypControl]').is(":checked") == false){
			$('input:checkbox[id=styleTypControl]').attr("checked",false);
			alert('레이어를 먼저 생성해주세요.');
		}
		else{
			if( ($('#serverType option:selected')[0].id == 'geoserver' && $('#serverLayerType option:selected')[0].id =='wfs') || $('#styleType option:selected')[0].id == $('#layerType option:selected')[0].id ){
				$('#wizard-content #wizard_script').append("\n");
				$('#wizard-content #wizard_script').append(stylevariable);
			}
			else{
				alert('생성된 레이어의 지오메트리와 일치하는 스타일만 적용가능하며 WFS 레이어만 스타일 적용이 가능합니다.');
				$('input:checkbox[id=styleTypControl]').attr("checked",false);
			}
		}	
	}
	else if(type == 'marker'){
		let _markerType = $('#markerType option:selected')[0].id;
		declaration = "	var "+targetId+"= new odf."+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"({\n"+
						"	position : [947819.151443904, 1938178.1779454942]});";
		if(_markerType == 'dragmarker'){
			declaration = "	var "+targetId+"= new odf."+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"({\n"+
			"	position : [947819.151443904, 1938178.1779454942]," +
			"	draggable : true});";
		}
		else if(_markerType == 'custommarker'){
			declaration ="	var "+targetId+"= new odf."+targetId.replace(targetId.charAt(0), targetId.charAt(0).toUpperCase())+"({\n"+
			"	position : [947819.151443904, 1938178.1779454942],\n" +	
			"	style : {\n " +
			"	 width : '20px', //너비\n" + 
			"	 height : '40px', //높이\n" +
			"	 src :  'images/smileIcon.png', //이미지 경로\n" +
			"		}\n" +
			"	});";			
		}
		
		department ="	"+ targetId+".setMap(map);\n"+
						"	"+targetId+".setPosition(map.getCenter());";	
		
		$('#wizard-content #wizard_script').append("\n");
		
		$('#wizard-content #wizard_script').append(declaration);
		$('#wizard-content #wizard_script').append("\n");
		
		$('#wizard-content #wizard_script').append(department);
		$('#wizard-content #wizard_script').append("\n");
	}
	else if(type == 'toc'){
		let serverType = 'geoserver'
		let tocLevel = $('input:radio[name=tocLevel]:checked').val();
		let serverUrl = $('#tocGeoServerUrl').val()+'/'+$('#tocLayerType option:selected')[0].id;
		let tocLayerName = $('#tocLayerName').val();
		let tocName = $('#tocSetLayerName').val();
		//let tocGeometryType = $('#tocGeometryType option:selected')[0].id;
		let tocLayerType = $('#tocLayerType option:selected')[0].id;
		let wfsServerUrl = $('#tocGeoServerUrl').val()+'/wfs';
		let setValue = '	var serverType = "'+serverType+'";\n'+
					'	var serverUrl = "'+serverUrl+'";\n'+
					'	var wfsServerUrl = "'+wfsServerUrl+'";\n';
			if(count > 0 && count<8 ){
				if(flag==true && bools==true){
					var addLayerToToc = {id:count, parent:"", text:tocName, lyrId:tocLayerName, onOff: true, lyrNm : tocName, service: tocLayerType, labelOnoff : false, symbolCndCn : "", filterCndCn : "" ,serverType : serverType,serverUrl : serverUrl, wfsServerUrl : wfsServerUrl};
					array.push(addLayerToToc);
				}
			}
			var Json = '	var tocJson = '+ JSON.stringify(array);
			
			if(tocLevel==='one'){
			department  = "	var " + targetId +" = new TOC(map,tocJson,{\n" +
				"		level1 : {//레이어 목록\n"+
				"	useFlag :true,\n" +
				"	 option : {\n" +
				"	topButton : {\n" +
				"	addGroup : false,//default 값 false\n"+
				"	addLayer : false,//default 값 false\n"+
				"	exportData : false,//default 값 false\n"+
				"	},\n"+
				"	layerButton : {\n"+
				"	label : false,//default 값 false\n"+
				"	onoff: true,//default 값 true 고정\n"+
				"	remove : false,//default 값 false\n"+
				"		}\n"+
				"	}\n"+
				"	}\n"+
				"})";
			}else{
				department  = "	var " + targetId +" = new TOC(map,tocJson,{\n" +
				"		level1 : {//레이어 목록\n"+
				"	useFlag :true,\n" +
				"	 option : {\n" +
				"	topButton : {\n" +
				"	addGroup : false,//default 값 false\n"+
				"	addLayer : false,//default 값 false\n"+
				"	exportData : false,//default 값 false\n"+
				"	},\n"+
				"	layerButton : {\n"+
				"	label : false,//default 값 false\n"+
				"	onoff: true,//default 값 true 고정\n"+
				"	remove : false,//default 값 false\n"+
				"		}\n"+
				"	}\n"+
				"	},\n"+
				"	level2 : {//레이어 상세\n"+
				"	useFlag :true,\n"+
				"	 option : {\n"+
				"		tab : {\n"+
				"			info : true,\n"+
				"			filter : true,\n"+
				"			 style : true,\n"+
				"			 legend : true,\n"+
				"		},\n"+
				"		imageUrl : '"+DeveloperUrl+"'\n"+
				"		},\n"+
				"	},\n"+
				"})";
			}
		$('#wizard-content #wizard_script').append("\n");
		$('#wizard-content #wizard_script').append(setValue);
		
		$('#wizard-content #wizard_script').append("\n");
		$('#wizard-content #wizard_script').append(Json);
		$('#wizard-content #wizard_script').append("\n");
		
		$('#wizard-content #wizard_script').append(department);
		$('#wizard-content #wizard_script').append("\n");
		
	}
}


typeCheck = (targetId) => {
	let wizard = {
		control:['basemap', 'zoom','overviewMap',,'scale','move','mousePosition', 'draw','measure','swiper', 'clear','divideMap','download','print'],
		map:['dragg', 'wheel', 'resize'],
		toc:['toc'],
		layer:['layer'],
		style:['style'],
		marker:['marker']
	};
	
	let result = null;
	Object.keys(wizard).forEach(function(item, idx) {
		
		let typevalue;
		let value = wizard[item].filter(function(subItm, subIdx){ 
			
			if(subItm == targetId){
				typevalue = true;
				return typevalue
			};
		});
		if(typevalue == true){
		result = value.length != 0 ?  item : null;
		}
	});
	return result;
}

function resetClipboard() {
	
	var copyButton = $(".code-toolbar button:last")[0];
	var clip = new ClipboardJS(copyButton, {
        text: function() {
            return changehtml;
        }
    });
}
