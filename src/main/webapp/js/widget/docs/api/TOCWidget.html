<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: TOCWidget</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AddressSearchWidget.html">AddressSearchWidget</a></span><span class="nav-desc"><p>주소 검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AdministrativeDistrictSearchWidget.html">AdministrativeDistrictSearchWidget</a></span><span class="nav-desc"><p>행정구역 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AttributeEditorWidget.html">AttributeEditorWidget</a></span><span class="nav-desc"><p>속성 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapWidget.html">BasemapWidget</a></span><span class="nav-desc"><p>배경지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookMarkControlWidget.html">BookMarkControlWidget</a></span><span class="nav-desc"><p>북마크컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CCTVControlWidget.html">CCTVControlWidget</a></span><span class="nav-desc"><p>CCTV컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ChartWidget.html">ChartWidget</a></span><span class="nav-desc"><p>차트 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControlWidget.html">ClearControlWidget</a></span><span class="nav-desc"><p>초기화컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ConditionFilterWidget.html">ConditionFilterWidget</a></span><span class="nav-desc"><p>조건식 편집기 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CreateLayerWidget.html">CreateLayerWidget</a></span><span class="nav-desc"><p>레이어 생성 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapWidget.html">DivideMapWidget</a></span><span class="nav-desc"><p>분할지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControlWidget.html">DownloadControlWidget</a></span><span class="nav-desc"><p>다운로드컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControlWidget.html">DrawControlWidget</a></span><span class="nav-desc"><p>그리기컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureAttributeFormWidget.html">FeatureAttributeFormWidget</a></span><span class="nav-desc"><p>피쳐 속성 폼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControlWidget.html">FullScreenControlWidget</a></span><span class="nav-desc"><p>전체화면컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GeocodingGridWidget.html">GeocodingGridWidget</a></span><span class="nav-desc"><p>지오코딩 그리드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GridWidget.html">GridWidget</a></span><span class="nav-desc"><p>속성테이블 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControlWidget.html">HomeControlWidget</a></span><span class="nav-desc"><p>홈이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LabelStyleWidget.html">LabelStyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerSearchWidget.html">LayerSearchWidget</a></span><span class="nav-desc"><p>레이어검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerUploadWidget.html">LayerUploadWidget</a></span><span class="nav-desc"><p>레이어업로드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LegendWidget.html">LegendWidget</a></span><span class="nav-desc"><p>범례 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControlWidget.html">MeasureControlWidget</a></span><span class="nav-desc"><p>측정컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControlWidget.html">MousePositionControlWidget</a></span><span class="nav-desc"><p>현재위치컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControlWidget.html">MoveControlWidget</a></span><span class="nav-desc"><p>이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OneParcelWidget.html">OneParcelWidget</a></span><span class="nav-desc"><p>일필지 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverViewMapControlWidget.html">OverViewMapControlWidget</a></span><span class="nav-desc"><p>지도오버뷰컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupSettingsWidget.html">PopupSettingsWidget</a></span><span class="nav-desc"><p>팝업 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupWidget.html">PopupWidget</a></span><span class="nav-desc"><p>지도 위 클릭 시 피쳐 정보 표출 팝업 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControlWidget.html">PrintControlWidget</a></span><span class="nav-desc"><p>출력컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RoadViewWidget.html">RoadViewWidget</a></span><span class="nav-desc"><p>로드뷰(카카오) 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControlWidget.html">RotationControlWidget</a></span><span class="nav-desc"><p>회전컨트롤 위젯 (생성 후 지도 객체에서 Alt + Shift + 지도 객체 Drag로 사용)</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControlWidget.html">ScaleControlWidget</a></span><span class="nav-desc"><p>축척컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SpatialAnalysisWidget.html">SpatialAnalysisWidget</a></span><span class="nav-desc"><p>공간분석 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleWidget.html">StyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControlWidget.html">SwiperControlWidget</a></span><span class="nav-desc"><p>스와이퍼컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperWidget.html">SwiperWidget</a></span><span class="nav-desc"><p>스와이퍼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TimeSliderControlWidget.html">TimeSliderControlWidget</a></span><span class="nav-desc"><p>타임슬라이더컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TOCWidget.html">TOCWidget</a></span><span class="nav-desc"><p>TOC 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControlWidget.html">ZoomControlWidget</a></span><span class="nav-desc"><p>지도 확대/축소 조작 위젯</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">TOCWidget</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>TOCWidget</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>TOC 위젯 생성자</p></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>TOC 위젯</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="TOCWidget">new TOCWidget<span class="signature">(object)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">object</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">odf</span>
            

            
                


    <span class="param-type">
        <code>odf</code>
    </span>
    

            

            

            

            <div class="param-description"><p>odf 모듈</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">target</span>
            

            
                


    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    

            

            

            

            <div class="param-description"><p>TOC 위젯을 생성할 영역</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>TOC 위젯 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">alertList</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>TOC 위젯 내부에서 사용하는 알림 정보 표현 정의</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">customAlert</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_TOCWidget_customAlert">oui_TOCWidget_customAlert</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>사용자 정의 알림 Function(기본값 (message)=&gt;{alert(message);})</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">maxDepth</span>
            

            
                


    <span class="param-type">
        <code>Integer</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>TOC 최대 뎁스 (최소값 1)(ex 2인 경우 그룹&gt;레이어 까지 가능하며 3인 경우 그룹&gt;그룹&gt;레이어 까지 가능)(기본값 : 2)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">groupHeight</span>
            

            
                


    <span class="param-type">
        <code>Integer</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>TOC에 표출되는 그룹의 높이 (기본값: 62)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerHeight</span>
            

            
                


    <span class="param-type">
        <code>Integer</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>TOC에 표출되는 레이어의 높이 (기본값: 62)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">setContentsElement</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>콘텐츠에 직접 element 추가할 수 있는 콜백함수(콘텐츠를 렌더링 할때마다 함수를 타며, 파라미터: 해당 콘텐츠 정보 , 리턴값 : string html)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHeader</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>해더부분 사용 여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useDragAndDrop</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>드래그앤드랍(z-index 조정 기능) 사용 여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">toc</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>toc 내부 UI ON/OFF 정보</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">setVisibleAll</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>전체 레이어 가시성 조정 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">deleteAll</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>전체 레이어 삭제 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">addGroup</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>그룹추가 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerSearch</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>검색 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerUpload</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>업로드 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">icon</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>아이콘 표출 여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">setGroupName</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>그룹명 변경 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">setVisible</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>가시성 조정 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">setLabel</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>라벨 셋팅 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">attributeGrid</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>속성 그리드 셋팅 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">delete</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>삭제 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerDetail</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>레이어 상세 버튼 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">attributePopup</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>레이어 속성 팝업 사용여부 (기본값: true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">webLayerUpdatePopup</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>웹 레이어 수정 팝업 사용여부 (기본값: true) 이 기능을 사용할 경우, 웹레이어 권한 체크 함수 필요(함수 정의 안한 경우, true 일때, 모든 웹레이어 수정 가능)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">visibleRange</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>레이어 현재 지도에 표출되고 있으면 글씨가 검정색으로, 표출되고 있지 않으면 회색글씨로 표출되는 기능 사용여부 (기본값 : true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">closeGrid</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>속성테이블 on/off 사용 여부(기본값 false), 이 기능을 사용할 경우, 속성테이블 아이콘 클릭시 on/off 할 수 있음. false일 경우, on만 가능</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">auth</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>컨텐츠 버튼 권한 확인</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">webLayerUpdatePopup</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>웹 레이어 수정 버튼을 사용하기 위한 권한이 있는지 확인하는 함수</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">layerDetailTargetElemnet</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>레이어 상세 정보 표출 영역 정의</p>
<ul>
<li>미 정의시 toc 위젯 생성 영역에 생성</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">popup</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_popup_object">oui_popup_object</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>팝업 설정 옵션 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">style</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_style_object">oui_style_object</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>레이어 스타일 옵션 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerSearch</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_LayerSearch_object">oui_LayerSearch_object</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>TOC 헤더에 생성되는 레이어 검색을 위한 정보</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layerUpload</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_LayerUpload_object">oui_LayerUpload_object</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>TOC 헤더에 생성되는 레이어 업로드를 위한 정보</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">grid</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_GridWidgetOption">oui_GridWidgetOption</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>TOC의 속성테이블 기능 사용 옵션</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">legend</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>TOC의 범례 옵션 정의</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">singleSymbolText</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>singleSymbolText 단일 심볼시 표출될 명칭 (기본값: &quot;기본 스타일&quot;)</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">api</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>TOC 위젯 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">getGroupId</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_TOCApi_getGroupId">oui_TOCApi_getGroupId</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>그룹 ID 가져오기</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">getLayerBox</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_attributeApi_getLayerBbox">oui_attributeApi_getLayerBbox</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>레이어 extents 가져오기</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">let layerApi = oui.LayerApi(oui.HttpClient({
        baseURL: '[api 경로]',
    }), {
    userId: 'user',
    userMapId: 'UM0000000429'
});
let layerSearchSubNavCodeApi = oui.CommonCodeApi(oui.HttpClient({
    baseURL: '[api 경로]',
}), { paramList: [{ params: { groupCode: 'DMS004' }, type: 'detail' }]});
let uploadApi = oui.UploadApi(oui.HttpClient({
    baseURL: 'http://**********:10040',
}), {
    sendOpertNtcnInfo: sendOpertNtcnInfo,
    userId: "user"
});
let geocodingApi = oui.GeocodingApi(oui.HttpClient({
    baseURL: '[api 경로]',
}), {sendOpertNtcnInfo: sendOpertNtcnInfo});
let mapApi = oui.MapApi(oui.HttpClient({
    baseURL: '[api 경로]',
}));
let commonCodeApi = oui.CommonCodeApi(oui.HttpClient({
    baseURL: '[api 경로]',
}));
let layerDownloadApi = oui.LayerDownloadApi(oui.HttpClient({
    baseURL: '[api 경로]',
}));
let columnInfoApi = oui.ColumnInfoApi(oui.HttpClient({
    baseURL: '[api 경로]',
}), { lyrId: 'LR0000030049', userId: 'user' });
let tocApi = oui.TOCApi(oui.HttpClient({
    baseURL: '[api 경로]',
}));

var tocWidget = new oui.TOCWidget({
    odf: odf,
    target: document.getElementById('tocWidget'),
    options: {
         alertList: {
             customAlert: (message) => {
                 console.dir(message);
             }
         },
         toc: {
			    layerUpload: _checkOption,
				addGroup: _checkOption,
 //      setContentsElement : (contentInfo) =>{
	//          return contentInfo.lyrClSeCode == '03' ? '&lt;button>새로운버튼추가&lt;/button>' : null;
	//      },
         maxDepth: 2,
         groupHeight: 55,
         layerHeight: 77,
         layerDetailTargetElemnet: '#layerDetail',
         popup: {
             api: {
                 selectPopupInfo: layerApi.selectPopupInfo
                 columnInfoOptionChange: columnInfoApi.changeOption,
                 columnInfoFunction: columnInfoApi.columnInfoFunction
             }
         },
         style: {
             options: {
                 imageUrl: '[그룹 이미지가 존재하는 경로]',
                 alertList: {
                     customAlert: (message) => {
                         console.dir(message);
                     }
                 },
             },
             api: {
                 //사용자정의 이미지 조회 api
                 selectSymbol: layerApi.selectSymbol,
                 //사용자정의 이미지 추가 api
                 insertSymbol: layerApi.insertSymbol,
                 //사용자정의 이미지 삭제 api
                 deleteSymbol: layerApi.deleteSymbol,
             },
         },
         layerSearch: {
             options: {
                 holdDataSeCode: 0,
                 pageSize: 20,
                 pageIndex: 1,
                 getAddLayerInfo: getAddLayerInfo, //추가버튼 클릭한 레이어의 정보
                 removeLayerCallback: (layerInfo) => { //레이어가 삭제된후에 실행되는 콜백함수
                     let layerId = layerInfo.layerId;
                //toc갱신 및 지도 재작업이 필요한 경우 여기서 해주어야한다.
                 },
                 alertList: {
                     customAlert: (message) => {
                          console.dir(message);
                     },
                     //사용자 정의 알림 메세지 정의
                     customConfirm: (message, callback) => {
                         //확인창 띄우기
                         let test = confirm(message);
                         if (test) {
                             callback();
                         }
                     }
                 },
                 layerTypeList: [
                     {
                         typeNm: "전체",
                         lyrTySeCode: ""
                     },
                     {
                         typeNm: "점",
                          lyrTySeCode: "1"
                       },
                      {
                         typeNm: "선",
                          lyrTySeCode: "2"
                      },
                       {
                          typeNm: "면",
                          lyrTySeCode: "3"
                     },
                      {
                          typeNm: "GeoTiff",
                          lyrTySeCode: "4"
                      }
                   ]
             },
             api: {
                 getLayerList: layerApi.getLayerList,
                 removeLayer: layerApi.removeLayer,
                 getNavInfo: function(callback){
                     callback({navList : [
                         {
                                    title: '사용자데이터',
                                    options: { holdDataSeCode: '1' }
                                }, {
                                    title: '공유데이터',
                                    options: { holdDataSeCode: '2' },
                                    subNavList: [
                                    {
                                        title: '전체 공유',
                                        options: { subHoldDataSeCode: '21' }
                                    },
                                    {
                                        title: '기관 공유',
                                        options: { subHoldDataSeCode: '22' }
                                    },
                                    {
                                        title: '사용자 공유',
                                        options: { subHoldDataSeCode: '23' }
                                    }
                     ], initNavValue : '1'});
                 },
                 getTypeNavList: function(callback){
                     let typeList = [
                         {"typeNm":"전체","lyrTySeCode":""}
                         ,{"typeNm":"TIFF","lyrTySeCode":"0"}
                        ,{"typeNm":"점","lyrTySeCode":"1","description":"(POINT)"}
                        ,{"typeNm":"선","lyrTySeCode":"2","description":"(LINE)"}
                        ,{"typeNm":"면","lyrTySeCode":"3","description":"(POLYGON)"}
                        ,{"typeNm":"GeoTIFF","lyrTySeCode":"4"}
                        ,{"typeNm":"레이어그룹","lyrTySeCode":"5"}
                     ];
                     callback(typeList);
                 }
             }
         },
         layerUpload: {
             options: {
                 alertList: {
                      customAlert: (message) => {
                         alert(message);
                     }
                 },
                 geocodingOptions: {
                     targetSrid: 5186
                 }
             },
             api: {
                 selectNotice: noticeApi.selectNotice,
                 insertLayer: layerApi.insertLayer,
                 publishFileLayer: uploadApi.publishFileLayer,
                 geocodingLayer: geocodingApi.geocodingLayer,
                 publishWebLayer: (result) => {
                     console.dir(result);
                 }
             }
         },
         grid: {
             options: {
                 pagination: true,
                 pageSize: 100,
                 rowSelection: 'multiple',
                 sortable: true,
                 filter: true,
                 mode: 'layer',
                 gridHeight: '500px',
                 gridWidth: '1500px',
                 cellWidth: '',
                 createOption: {
                     chart: true,
                     geomSearch: true,
                     attributeEditor: true,
                     modify: true,
                     filter: true,
                     csv: true,
                     delete: true,
                     insert: true,
                     clear: true,
                     editMode: true
                 }
             },
             api: {//데이터 조회 (mode에 따라 layer(feature 정보), object(일반 json 정보))
                 //지오서버 데이터 조회
                 getData: mapApi.getData,
                 //지오서버 업로드
                 updateData: mapApi.updateData,
                 //공통코드조회
                 getCommonCode: commonCodeApi.commonCodeFunction,
                 //상세공통코드 조회 aixos.all
                 getAllDetailCode: commonCodeApi.getAllDetailCode,
                 //별칭 및 컬럼 정보 조회
                 columnInfoFunction: columnInfoApi.columnInfoFunction,
                 //컬럼정보조회 옵션값 변경
                 columnInfoOptionChange: columnInfoApi.changeOption,
                 //레이어다운로드
                 downloadLayer: layerDownloadApi.downloadLayer,
                 // //cql 정보 조회
                 //cqlInfoFunction: cqlInfoApi.cqlInfoFunction,
                 // //cql 옵션 변경
                 //cqlInfoOptionChange: cqlInfoApi.changeOption,
             },
             target: document.getElementById('root'),
         },
     },
     api: {
         getGroupId: tocApi.getGroupId,
     }
});</code></pre>
















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="addTo">addTo<span class="signature">(flag)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>TOC 위젯 열기 / 닫기</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//TOC 위젯 열기
tocWidget.addTo(true);

//TOC 위젯 닫기
tocWidget.addTo(false);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="changeChartOptions">changeChartOptions<span class="signature">(param)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>toc chartOption 변경함수</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">param</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변경할 파라미터</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.changeChartOptions({
                                  // chartCreateMode : 'show',
                                  // createObject : {
                                  // title : '차트타이틀',
                                  // chartType : 'piechart',
                                  // targetCol : '읍면동',
                                  // value : 'base_year',
                                  // targetElement : '',
                                  // width : '1000px',
                                  // height : '600px',
                                  // },
                                  label: false,
                                  legend: true,
                                  legendPosition: 'right',
                                  //									customColor : {
                                  //										colorList : [[255,219,219],[255,191,191],[255,159,159],[255,128,128],[255,96,96],[255,64,64],[255,32,32],[255,0,0]],
                                  //										colorLegend :  [3,24.714285714285715,46.42857142857143,68.14285714285714,89.85714285714286,111.57142857142858,133.2857142857143,155],
                                  //									},
                                  applyCallback: (object) => {
                                      console.log(object)
                                  })</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="changeGridOptions">changeGridOptions<span class="signature">(param)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>toc gridOption 변경함수</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">param</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변경할 파라미터</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.changeGridOptions({
                                gridCallback: function (e) {
                                    // smt 프로젝트 내에서 처리할 dom event들을 callback으로
                                    // 받아서 oui 에서 처리
                                    document.querySelector('#optionTable').style.display = 'block';
                                    app.widget.geocodingGridWidget &amp;&amp; app.widget.geocodingGridWidget.addTo(false);
                                    document.getElementsByClassName('titOpTable')[0].innerText = e.title;
                                    window.setTimeout(function () { map.updateSize() }, '100');
                                },
                                alertList: {
                                    customAlert: (message) => {
                                        callAlertMessage(message);
                                    },
                                    customErrorAlert: (type, message) => {
                                        callAlert(type, message);
                                    },
                                    startLoadingBar: (message) => {
                                        callTargetLoadingBar(document.querySelector('#gridWidget'), message, true);
                                    },
                                    // 사용자 정의 로딩바 종료 function
                                    endLoadingBar: (message) => {
                                        callTargetLoadingBar(document.querySelector('#gridWidget'), message, false);
                                    },
                                },
                                pagination: true,
                                pageSize: 100,
                                rowSelection: 'multiple',
                                sortable: true,
                                filter: true,
                                mode: 'layer',
                                gridHeight: '306px',
                                gridWidth: '',
                                cellWidth: '',
                                createOption: {
                                    chart: true, // 차트 위젯 생성여부
                                    geomSearch: true, // 공간검색 사용여부
                                    attributeEditor: app.webapp == undefined &amp;&amp; viewMode == 'mapGale' ? false : true, // 속성설정
                                    // 사용여부
                                    editMode: app.webapp == undefined &amp;&amp; viewMode == 'mapGale' ? false : true, // 편집모드
                                    // 사용여부
                                    modify: true, // 피쳐편집 사용여부
                                    filter: true, // 조건식편집기 사용여부
                                    export: true, // 추출 사용여부
                                    delete: true, // 피쳐삭제 사용여부
                                    insert: true, // 피쳐 추가 사용여부
                                    clear: true, // 초기화 버튼 사용여부
                                },
                                conditionFilterOption: {
                                    thema: 'table', // 조건식편집기 thema
                                },
                                attributeEditorOption: {

                                },
                                chartOption: {
                                    // chartCreateMode : 'show',
                                    // createObject : {
                                    // title : '차트타이틀',
                                    // chartType : 'piechart',
                                    // targetCol : '읍면동',
                                    // value : 'base_year',
                                    // targetElement : '',
                                    // width : '1000px',
                                    // height : '600px',
                                    // },
                                    label: false,
                                    legend: true,
                                    legendPosition: 'right',
                                    //									customColor : {
                                    //										colorList : [[255,219,219],[255,191,191],[255,159,159],[255,128,128],[255,96,96],[255,64,64],[255,32,32],[255,0,0]],
                                    //										colorLegend :  [3,24.714285714285715,46.42857142857143,68.14285714285714,89.85714285714286,111.57142857142858,133.2857142857143,155],
                                    //									},
                                    applyCallback: (object) => {
                                        console.log(object)
                                    },
                                    getData : (obj) => {
                                        console.log(obj)
                                    }
                                },
                            },)</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="createContentList">createContentList<span class="signature">(contentList)</span><span class="return-type-signature"> &rarr; {Promise.&lt;Array>}</span>
    </h4>





<div class="method-description">
    
    <p>TOC 목록 생성</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">contentList</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#oui_toc_content">oui_toc_content</a>></code>
    </span>
    

            

            

            

            <div class="param-description"><p>TOC에 추가할 콘텐츠(레이어 또는 그룹) 정보 목록</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Promise.&lt;Array></code>
            
            
                <p>생성된 TOC 목록을 Array로 갖는 Promise</p>
            
        </li>
    
    </ul>




    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">let layer = odf.LayerFactory.produce('geoserver', {
    method: 'get',
    server: `[api경로]/api/map/wfs`,
    layer: 'user:L100004587',
    service: 'wfs',
    bbox: false,
});
let contentList = [
    {
        lyrGroupSeCode: "01",
        contentId: 'LG0000000185',
        title: '테스트_그룹',
        lyrGroupId: 'LG0000000185',
        onOffAt: 'Y',
        upperGroupId: null,
        lyrGroupSn: 2,
        registerId: 'user'
    },
    {
        lyrGroupSeCode: "02",
        title: "테스트_레이어",
        registerId: 'user',
        contentId: layer.getODFId(),
        odfLayerId: layer.getODFId(),
        cntntsId: 'L100004587',
        upperGroupId: 'LG0000000185',
        linkedLayer: layer,
        layerId: 'LR0000031702',
        onOffAt: 'Y',
        style: {
            ...styleOption,
            "useMultiStyle": item.jobClCode
        }
        , filter: {
            //추후에 추가 예정
        }
        , attributes: []
        , jobClCode: "02",
        , popup: {
            originPopupObject: {
                layerPopupEstbs:
                    lyrGroupSn: 1
                    popupTyCode: "1"
                    popupSj: "시공시 주의 면적"
                    registerId: "user"
                }
                detailList: [
                    {columnNm: "이름", columnOrdr: 1, indictAt: "Y", columnNcm: "이름"},
                    {columnNm: "id", columnOrdr: 2, indictAt: "N", columnNcm: "id"},
                    {columnNm: "등급", columnOrdr: 3, indictAt: "N", columnNcm: "등급"},
                    {columnNm: "표시", columnOrdr: 4, indictAt: "N", columnNcm: "표시"},
                    {columnNm: "비고", columnOrdr: 5, indictAt: "N", columnNcm: "비고"},
                    {columnNm: "구분", columnOrdr: 6, indictAt: "N", columnNcm: "구분"}
                ];
            },
            nowPopupObject:{
                layerPopupEstbs:
                    lyrGroupSn: 1
                    popupTyCode: "1"
                    popupSj: "시공시 주의 면적"
                    registerId: "user"
                }
                detailList: [
                    {columnNm: "이름", columnOrdr: 1, indictAt: "Y", columnNcm: "이름"},
                    {columnNm: "id", columnOrdr: 2, indictAt: "N", columnNcm: "id"},
                    {columnNm: "등급", columnOrdr: 3, indictAt: "N", columnNcm: "등급"},
                    {columnNm: "표시", columnOrdr: 4, indictAt: "N", columnNcm: "표시"},
                    {columnNm: "비고", columnOrdr: 5, indictAt: "N", columnNcm: "비고"},
                    {columnNm: "구분", columnOrdr: 6, indictAt: "N", columnNcm: "구분"}
                ];
            }
        },
        lyrGroupSn: 1
    }
];
tocWidget.createContentList(contentList);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getAttributesInfo">getAttributesInfo<span class="signature">()</span><span class="return-type-signature"> &rarr; {Object}</span>
    </h4>





<div class="method-description">
    
    <p>toc grid의 속성설정정보 조회</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Object</code>
            
            
                <p>속성설정정보 object</p>
            
        </li>
    
    </ul>




    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.getAttributesInfo();</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getContentList">getContentList<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array}</span>
    </h4>





<div class="method-description">
    
    <p>toc 현재 목록 정보 가져오기</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array</code>
            
            
                <p>toc에 표출되고 있는 그룹 및 레이어 정보 배열 (위에서 아래 순으로 배열)</p>
            
        </li>
    
    </ul>




    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">tocWidget.getContentList();</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getDataOptions">getDataOptions<span class="signature">()</span><span class="return-type-signature"> &rarr; {Object}</span>
    </h4>





<div class="method-description">
    
    <p>toc 특정 레이어 조건식 초기값 변경 함수</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Object</code>
            
            
                <p>Grid 데이터 옵션값</p>
            
        </li>
    
    </ul>




    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.updateServerCql('id = 1');
          </code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getTarget">getTarget<span class="signature">()</span><span class="return-type-signature"> &rarr; {HTMLElement}</span>
    </h4>





<div class="method-description">
    
    <p>toc 타겟 element 가져오기</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>HTMLElement</code>
            
            
                <p>toc 타겟 element</p>
            
        </li>
    
    </ul>




    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.getTarget();
          </code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="remove">remove<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>toc 위젯 제거</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">tocWidget.remove();</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeContent">removeContent<span class="signature">(object)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>toc 해당 컨텐츠 제거하기</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">object</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>제거할 컨텐츠 정보</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">type</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>제거할 컨텐츠 구분값 ( contentId : toc 구분 ID , layerId : 레이어 DB ID , layerGroupId : 그룹 ID )</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">id</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>제거할 컨텐츠 ID</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.removeContent([{ type: 'layerId', id: 'LR0000031702' }]);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeGrid">removeGrid<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>toc grid 제거 함수</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.removeGrid();</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setContent">setContent<span class="signature">(content)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>toc 현재 목록 정보 가져오기</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">content</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_toc_content">oui_toc_content</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>TOC에 TOC에 추가할 콘텐츠 정보</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">let groupContent = {
    lyrGroupSeCode: "01",
    contentId: 'LG0000000185',
    title: '테스트_그룹',
    lyrGroupId: 'LG0000000185',
    onOffAt: 'Y',
    upperGroupId: null,
    lyrGroupSn: 2,
    registerId: 'user'
}
tocWidget.setContent(groupContent);
let layer = odf.LayerFactory.produce('geoserver', {
    method: 'get',
    server: `[api경로]/api/map/wfs`,
    layer: 'user:L100004587',
    service: 'wfs',
    bbox: false,
});

//레이어의 스타일 정보 조회
let styleObject = [{
     seperatorFunc: 'default',
     style: layer.getStyle().getObject()
 }];

//레이어 투명도 조회
let opacity = layer.getOpacity();

let layerContent = {
    lyrGroupSeCode: "02",
    title: "테스트_레이어",
    registerId: 'user',
    contentId: layer.getODFId(),
    odfLayerId: layer.getODFId(),
    cntntsId: 'L100004587',
    upperGroupId: 'LG0000000185',
    linkedLayer: layer,
    layerId: 'LR0000031702',
    onOffAt: 'Y',
    style: {
        labelFlag: false,//라벨 활성화여부
        targetLayer: layer,//스타일을 표현할 레이어 객체
        targetLayerService: 'vector',//레이어 유형 'vector', 'image', 'cluster', 'hotspot'
        originStyleObject: {
             styleObject,
             opacity,
        },//초기 스타일 값
        nowStyleObject: {
             styleObject,
             opacity,
        },//현재 적용된 스타일 값
        previewStyleObject: {
            option: {
                type: undefined,
                useManualEditing: true,
                styleOption: undefined,
            },
            styleObject: styleObject,//미리보기 스타일 값
            opacity,
        },//미리보기 스타일 정보
        geometryType: 'polygon'
    }
    , filter: {
        //추후에 추가 예정
    }
    , attributes: []
    , jobClCode: "02",
    , popup: {
        originPopupObject: {
            layerPopupEstbs:
                lyrGroupSn: 1
                popupTyCode: "1"
                popupSj: "시공시 주의 면적"
                registerId: "user"
            }
            detailList: [
                {columnNm: "이름", columnOrdr: 1, indictAt: "Y", columnNcm: "이름"},
                {columnNm: "id", columnOrdr: 2, indictAt: "N", columnNcm: "id"},
                {columnNm: "등급", columnOrdr: 3, indictAt: "N", columnNcm: "등급"},
                {columnNm: "표시", columnOrdr: 4, indictAt: "N", columnNcm: "표시"},
                {columnNm: "비고", columnOrdr: 5, indictAt: "N", columnNcm: "비고"},
                {columnNm: "구분", columnOrdr: 6, indictAt: "N", columnNcm: "구분"}
            ];
        },
        nowPopupObject:{
            layerPopupEstbs:
                lyrGroupSn: 1
                popupTyCode: "1"
                popupSj: "시공시 주의 면적"
                registerId: "user"
            }
            detailList: [
                {columnNm: "이름", columnOrdr: 1, indictAt: "Y", columnNcm: "이름"},
                {columnNm: "id", columnOrdr: 2, indictAt: "N", columnNcm: "id"},
                {columnNm: "등급", columnOrdr: 3, indictAt: "N", columnNcm: "등급"},
                {columnNm: "표시", columnOrdr: 4, indictAt: "N", columnNcm: "표시"},
                {columnNm: "비고", columnOrdr: 5, indictAt: "N", columnNcm: "비고"},
                {columnNm: "구분", columnOrdr: 6, indictAt: "N", columnNcm: "구분"}
            ];
        }
    },
    lyrGroupSn: 1
}
tocWidget.setContent(groupContent);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setLegendList">setLegendList<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>범례 목록 셋팅</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.setLegendList([ID1, ID2, ID3....]);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="updateServerCql">updateServerCql<span class="signature">(cqlText)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>toc 특정 레이어 조건식 초기값 변경 함수</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">cqlText</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조건식 필터값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">toc.updateServerCql('id = 1');</code></pre>
















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Mon Dec 09 2024 10:44:32 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>