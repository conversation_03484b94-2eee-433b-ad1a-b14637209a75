<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: OverViewMapControlWidget</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AddressSearchWidget.html">AddressSearchWidget</a></span><span class="nav-desc"><p>주소 검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AdministrativeDistrictSearchWidget.html">AdministrativeDistrictSearchWidget</a></span><span class="nav-desc"><p>행정구역 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AttributeEditorWidget.html">AttributeEditorWidget</a></span><span class="nav-desc"><p>속성 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapWidget.html">BasemapWidget</a></span><span class="nav-desc"><p>배경지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookMarkControlWidget.html">BookMarkControlWidget</a></span><span class="nav-desc"><p>북마크컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CCTVControlWidget.html">CCTVControlWidget</a></span><span class="nav-desc"><p>CCTV컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ChartWidget.html">ChartWidget</a></span><span class="nav-desc"><p>차트 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControlWidget.html">ClearControlWidget</a></span><span class="nav-desc"><p>초기화컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ConditionFilterWidget.html">ConditionFilterWidget</a></span><span class="nav-desc"><p>조건식 편집기 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CreateLayerWidget.html">CreateLayerWidget</a></span><span class="nav-desc"><p>레이어 생성 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapWidget.html">DivideMapWidget</a></span><span class="nav-desc"><p>분할지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControlWidget.html">DownloadControlWidget</a></span><span class="nav-desc"><p>다운로드컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControlWidget.html">DrawControlWidget</a></span><span class="nav-desc"><p>그리기컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureAttributeFormWidget.html">FeatureAttributeFormWidget</a></span><span class="nav-desc"><p>피쳐 속성 폼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControlWidget.html">FullScreenControlWidget</a></span><span class="nav-desc"><p>전체화면컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GeocodingGridWidget.html">GeocodingGridWidget</a></span><span class="nav-desc"><p>지오코딩 그리드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GridWidget.html">GridWidget</a></span><span class="nav-desc"><p>속성테이블 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControlWidget.html">HomeControlWidget</a></span><span class="nav-desc"><p>홈이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LabelStyleWidget.html">LabelStyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerSearchWidget.html">LayerSearchWidget</a></span><span class="nav-desc"><p>레이어검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerUploadWidget.html">LayerUploadWidget</a></span><span class="nav-desc"><p>레이어업로드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LegendWidget.html">LegendWidget</a></span><span class="nav-desc"><p>범례 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControlWidget.html">MeasureControlWidget</a></span><span class="nav-desc"><p>측정컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControlWidget.html">MousePositionControlWidget</a></span><span class="nav-desc"><p>현재위치컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControlWidget.html">MoveControlWidget</a></span><span class="nav-desc"><p>이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OneParcelWidget.html">OneParcelWidget</a></span><span class="nav-desc"><p>일필지 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverViewMapControlWidget.html">OverViewMapControlWidget</a></span><span class="nav-desc"><p>지도오버뷰컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupSettingsWidget.html">PopupSettingsWidget</a></span><span class="nav-desc"><p>팝업 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupWidget.html">PopupWidget</a></span><span class="nav-desc"><p>지도 위 클릭 시 피쳐 정보 표출 팝업 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControlWidget.html">PrintControlWidget</a></span><span class="nav-desc"><p>출력컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RoadViewWidget.html">RoadViewWidget</a></span><span class="nav-desc"><p>로드뷰(카카오) 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControlWidget.html">RotationControlWidget</a></span><span class="nav-desc"><p>회전컨트롤 위젯 (생성 후 지도 객체에서 Alt + Shift + 지도 객체 Drag로 사용)</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControlWidget.html">ScaleControlWidget</a></span><span class="nav-desc"><p>축척컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SpatialAnalysisWidget.html">SpatialAnalysisWidget</a></span><span class="nav-desc"><p>공간분석 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleWidget.html">StyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControlWidget.html">SwiperControlWidget</a></span><span class="nav-desc"><p>스와이퍼컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperWidget.html">SwiperWidget</a></span><span class="nav-desc"><p>스와이퍼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TimeSliderControlWidget.html">TimeSliderControlWidget</a></span><span class="nav-desc"><p>타임슬라이더컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TOCWidget.html">TOCWidget</a></span><span class="nav-desc"><p>TOC 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControlWidget.html">ZoomControlWidget</a></span><span class="nav-desc"><p>지도 확대/축소 조작 위젯</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">OverViewMapControlWidget</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>OverViewMapControlWidget</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>지도오버뷰컨트롤 위젯 생성자</p></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>지도오버뷰컨트롤 위젯</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="OverViewMapControlWidget">new OverViewMapControlWidget<span class="signature">(object)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">object</span>
            

            
                


    <span class="param-type">
        <code>object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>생성 관련 object</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">element</span>
            

            
                


    <span class="param-type">
        <code>Element</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도 오버뷰 맵객체를 생성할 Element</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">target</span>
            

            
                


    <span class="param-type">
        <code>Element</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도오버뷰컨트롤위젯을 생성할 Element</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">var overViewMapControlWidget = new oui.OverViewMapControlWidget({
    options: { element: document.getElementById('overview') },
    target: document.getElementById('test16'),
});</code></pre>
















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="addTo">addTo<span class="signature">(map)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도오버뷰컨트롤 위젯 추가</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code>map</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도오버뷰컨트롤 위젯을 생성할 맵객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">overViewMapControlWidget.addTo(map)</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="remove">remove<span class="signature">(map)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>지도오버뷰컨트롤 위젯 삭제</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code>map</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도오버뷰컨트롤 위젯을 생성할 맵객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">overViewMapControlWidget.remove()</code></pre>
















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Mon Dec 09 2024 10:44:32 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>