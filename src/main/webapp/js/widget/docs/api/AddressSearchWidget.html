<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: AddressSearchWidget</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AddressSearchWidget.html">AddressSearchWidget</a></span><span class="nav-desc"><p>주소 검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AdministrativeDistrictSearchWidget.html">AdministrativeDistrictSearchWidget</a></span><span class="nav-desc"><p>행정구역 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="AttributeEditorWidget.html">AttributeEditorWidget</a></span><span class="nav-desc"><p>속성 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapWidget.html">BasemapWidget</a></span><span class="nav-desc"><p>배경지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookMarkControlWidget.html">BookMarkControlWidget</a></span><span class="nav-desc"><p>북마크컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CCTVControlWidget.html">CCTVControlWidget</a></span><span class="nav-desc"><p>CCTV컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ChartWidget.html">ChartWidget</a></span><span class="nav-desc"><p>차트 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControlWidget.html">ClearControlWidget</a></span><span class="nav-desc"><p>초기화컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ConditionFilterWidget.html">ConditionFilterWidget</a></span><span class="nav-desc"><p>조건식 편집기 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="CreateLayerWidget.html">CreateLayerWidget</a></span><span class="nav-desc"><p>레이어 생성 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapWidget.html">DivideMapWidget</a></span><span class="nav-desc"><p>분할지도 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControlWidget.html">DownloadControlWidget</a></span><span class="nav-desc"><p>다운로드컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControlWidget.html">DrawControlWidget</a></span><span class="nav-desc"><p>그리기컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureAttributeFormWidget.html">FeatureAttributeFormWidget</a></span><span class="nav-desc"><p>피쳐 속성 폼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControlWidget.html">FullScreenControlWidget</a></span><span class="nav-desc"><p>전체화면컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GeocodingGridWidget.html">GeocodingGridWidget</a></span><span class="nav-desc"><p>지오코딩 그리드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="GridWidget.html">GridWidget</a></span><span class="nav-desc"><p>속성테이블 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControlWidget.html">HomeControlWidget</a></span><span class="nav-desc"><p>홈이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LabelStyleWidget.html">LabelStyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerSearchWidget.html">LayerSearchWidget</a></span><span class="nav-desc"><p>레이어검색 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerUploadWidget.html">LayerUploadWidget</a></span><span class="nav-desc"><p>레이어업로드 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LegendWidget.html">LegendWidget</a></span><span class="nav-desc"><p>범례 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControlWidget.html">MeasureControlWidget</a></span><span class="nav-desc"><p>측정컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControlWidget.html">MousePositionControlWidget</a></span><span class="nav-desc"><p>현재위치컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControlWidget.html">MoveControlWidget</a></span><span class="nav-desc"><p>이동컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OneParcelWidget.html">OneParcelWidget</a></span><span class="nav-desc"><p>일필지 조회 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverViewMapControlWidget.html">OverViewMapControlWidget</a></span><span class="nav-desc"><p>지도오버뷰컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupSettingsWidget.html">PopupSettingsWidget</a></span><span class="nav-desc"><p>팝업 설정 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PopupWidget.html">PopupWidget</a></span><span class="nav-desc"><p>지도 위 클릭 시 피쳐 정보 표출 팝업 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControlWidget.html">PrintControlWidget</a></span><span class="nav-desc"><p>출력컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RoadViewWidget.html">RoadViewWidget</a></span><span class="nav-desc"><p>로드뷰(카카오) 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControlWidget.html">RotationControlWidget</a></span><span class="nav-desc"><p>회전컨트롤 위젯 (생성 후 지도 객체에서 Alt + Shift + 지도 객체 Drag로 사용)</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControlWidget.html">ScaleControlWidget</a></span><span class="nav-desc"><p>축척컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SpatialAnalysisWidget.html">SpatialAnalysisWidget</a></span><span class="nav-desc"><p>공간분석 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleWidget.html">StyleWidget</a></span><span class="nav-desc"><p>레이어 스타일 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControlWidget.html">SwiperControlWidget</a></span><span class="nav-desc"><p>스와이퍼컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperWidget.html">SwiperWidget</a></span><span class="nav-desc"><p>스와이퍼 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TimeSliderControlWidget.html">TimeSliderControlWidget</a></span><span class="nav-desc"><p>타임슬라이더컨트롤 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="TOCWidget.html">TOCWidget</a></span><span class="nav-desc"><p>TOC 위젯</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControlWidget.html">ZoomControlWidget</a></span><span class="nav-desc"><p>지도 확대/축소 조작 위젯</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">AddressSearchWidget</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>AddressSearchWidget</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>주소 검색 위젯 생성자</p></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>주소 검색 위젯</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="AddressSearchWidget">new AddressSearchWidget<span class="signature">(object)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">object</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">odf</span>
            

            
                


    <span class="param-type">
        <code>odf</code>
    </span>
    

            

            

            

            <div class="param-description"><p>odf 모듈</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">target</span>
            

            
                


    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    

            

            

            

            <div class="param-description"><p>주소 검색 위젯을 생성할 영역</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>주소 검색 위젯 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">afterMove</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_afterMove">oui_AddressSearchWidget_afterMove</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>검색 결과를 더블클릭했을때, 해당 도형이 하이라이트 된 후, 실행되는 함수</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">afterMoveMapToResolution</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_afterMoveMapToResolution">oui_AddressSearchWidget_afterMoveMapToResolution</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>검색 결과를 더블클릭했을때, 해당 도형으로 이동 시킬때, 특정 해상도로 이동시키는 함수</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">pagineType</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_pagineType">oui_AddressSearchWidget_pagineType</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>페이징 유형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">styleObject</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>하이라이트 레이어 스타일(미정의시 내부에서 정의한 기본 스타일 적용)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">clearHilightLayerFlagMove</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>지도 이동시 하이라이트 레이어 초기화 활성화 기능 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useHilightZIndexUp</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>하이라이트 레이어의 Z-INDEX를 최상위로 조정할지 여부(기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">clearResultFlagMove</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>지도 이동시 검색결과 초기화 활성화 기능 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useSearchButton</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>주소 검색에서 검색 버튼 표출 여부(기본 false, 엔터를 사용해 검색)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">alertList</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_alertList">oui_alertList</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>주소 검색 위젯 내부에서 사용하는 알림 정보 표현 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">addressSearch</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>어떤 주소 검색을 사용할지 정의</p>
<ul>
<li>정의하지 않으면 모든 주소 검색 사용하는 것으로 간주</li>
</ul></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">basic</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>기초구역번호 검색 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">bld</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>건물명 검색 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">coord</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>경위도 검색 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">int</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>통합검색 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">jibun</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>지번 검색 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">pnu</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>PNU 검색 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">poi</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>POI 검색 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">road</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>도로명주소 검색 사용 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">roadApi</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>행안부 도로명 주소 검색(api 이용) 사용 여부(기본값 false)</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">useSearchResult</span>
            

            
                


    <span class="param-type">
        <code>Obejct</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>주소 검색결과 어떤 명칭만 표출 할 것인지 정의</p>
<ul>
<li>정의하지 않으면 모든 명칭 표출 하는 것으로 간주</li>
</ul></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">basic</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>기초구역번호 검색 결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">bld</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>건물명 검색 결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">coord</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>경위도 검색 결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">int</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>통합검색 검색 결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">jibun</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>지번 검색 결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">pnu</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>PNU 검색 결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">poi</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>POI 검색 결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">poiName</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>검색결과 POI명칭을 표출할 것인지 여부(기본값 true)</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">road</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>도로명주소 검색 결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">roadApi</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#oui_AddressSearchWidget_useSearchResult_name">oui_AddressSearchWidget_useSearchResult_name</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>행안부 도로명 주소 검색(api 이용)결과 어떤 명칭만 표출할 것인지 정의</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">featureDisplayPriority</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>피쳐 표출 우선순위</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">int</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>통합검색 피쳐 표출 우선순위 'build'(건물) 'lot'(필지) (기본값 : 'builder') 예) 'build'인 경우 건물 피쳐가 존재하면 건물피쳐로 표출, 없는 경우 필지 피쳐로 표출</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">useOneClickResultClose</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>검색결과 결과 클릭 시 결과창 닫힘 사용 유무 (기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">useAutoResolution</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>검색결과 클릭시 도형 위치로 지도가 이동되는데 이때 너무 확대되지 않도록 지도의 해상도를 자동으로 조절하는 기능 사용 여부 (기본값 true)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">customElement</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>사용자 커스텀 요소 추가</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">addrResult</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>주소검색 결과에 사용자 커스텀 요소 추가</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">button</span>
            

            
                


    <span class="param-type">
        <code>Array</code>
    </span>
    

            

            
                <span class="param-attributes">
                
                    &lt;optional&gt;<br>
                

                

                
                </span>
            

            

            <div class="param-description"><p>주소검색 결과에 사용자 정의 button들 추가  예시) [{id: '', className : 'addBtn', text: '+ 추가', isClearHighlight: true, //하이라이트 레이어 제거 여부(기본값 false) onButtonClick: (info)=&gt;{ this.addParcel({geom: info.geom, pnu : info.addrPnu})}}]</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">api</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#AddressApi">AddressApi</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>주소 검색 위젯에서 사용할 api 정보</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">basicSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>기초구역번호 검색</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">bldSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>건물명 검색</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">coordSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>경위도 좌표 검색</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">intSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>통합검색</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">jibunSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>지번 검색</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">pnuSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>PNU 검색</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">poiSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>POI 검색</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">roadSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>도로명주소 검색 (주소정제 이용)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">roadApiSearch</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#oui_addressApi_search">oui_addressApi_search</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>행안부 도로명 주소 검색(api 이용)</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//00. 주소검색 위젯 api 생성
   let addressApi = oui.AddressApi(oui.HttpClient({
    baseURL: '[api 경로]',
}), {
    projection: '5186',
});

   //01. 주소검색 위젯 생성
   let addressSearchWidget = new oui.AddressSearchWidget({
    odf,
    target: document.querySelector('#addressSearchArea'),
    options: {
        pagineType: 'countable',//countable(일반페이징), unbounded(스크롤페이징)
        styleObject: {
            image: {
            circle: {
                radius: 10,
                fill: { color: [255, 255, 255, 0.4] },
                stroke: { color: [241, 189, 29, 0.82], width: 2 },
            },
            },
            fill: { color: [255, 255, 255, 0.4] },
            stroke: { color: [241, 189, 29, 0.82], width: 2 },
        },
        //지도 이동시 하이라이트 레이어 초기화 활성화
        clearHilightLayerFlagMove: true,
        //지도 이동시 검색결과 날리기 활성화
        clearResultFlagMove: true,
        //알림옵션
        alertList: {
            //사용자 정의 알림 메세지 정의
            customAlert: (message) => {
            console.log(message);
            },
            //사용자 정의 로딩바 시작 function
            startLoadingBar: (message) => {
            console.log(message);
            },
            //사용자 정의 로딩바 종료 function
            endLoadingBar: (message) => {
            console.log(message);
            },
        }
        },
        api: {
        //01. type01
        //기초구역번호 검색 function
        basicSearch: addressApi.basicSearch,
        //건물명 검색
        bldSearch: addressApi.bldSearch,
        //경위도 좌표 검색 function
        coordSearch: addressApi.coordSearch,
        //통합검색 function
        intSearch: addressApi.intSearch,
        //지번 검색 function
        jibunSearch: addressApi.jibunSearch,
        //PNU 검색 function
        pnuSearch: addressApi.pnuSearch,
        //POI 검색 function
        poiSearch: addressApi.poiSearch,
        //도로명주소 검색 (주소정제 이용) function
        roadSearch: addressApi.roadSearch,
        //행안부 도로명 주소 검색 api 이용 function
        roadApiSearch: addressApi.roadApiSearch,
        }
    });</code></pre>
















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="addTo">addTo<span class="signature">(map)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>주소 검색 위젯을 지도 객체와 연결 후 렌더링</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code>odf.Map</code>
    </span>
    

            

            

            

            <div class="param-description"><p>연결할 지도 객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//01. 지도 객체 생성
     let map = new odf.Map(document.getElementById('map'),{
    ...
});

     //02. 주소검색 위젯 생성
     let addressSearchWidget = new oui.AddressSearchWidget({
    odf,
    target: document.querySelector('#addressSearchArea'),
    options: {...},
    api : {...},
};

     //03. 주소검색 위젯을 지도객체와 연결 및 렌더링
     addressSearchWidget.addTo(map);</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="remove">remove<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>주소 검색 위젯을 지도 객체와 연결 해제 및 렌더링 해제</p>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">//01. 지도 객체 생성
     let map = new odf.Map(document.getElementById('map'),{
    ...
});

     //02. 주소검색 위젯 생성
     let addressSearchWidget = new oui.AddressSearchWidget({
    odf,
    target: document.querySelector('#addressSearchArea'),
    options: {...},
    api : {...},
};

     //03. 주소검색 위젯을 지도객체와 연결 및 렌더링
     addressSearchWidget.addTo(map);

     //04. 주소 검색 위젯을 지도객체와 연결 해제 및 렌더링 해제
     addressSearchWidget.remove();</code></pre>
















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Mon Dec 09 2024 10:44:32 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>