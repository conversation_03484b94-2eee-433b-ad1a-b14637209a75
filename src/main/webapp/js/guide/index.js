/**
 * guide/index.js
 */

$(window).load(function() {
	menu.init("", makeMenuJson, loadTarget);
	site.eventTab();
});

var menuJson = {};

function makeMenuJson() {

	var grpId = 0;
	var lastTagName = "";

	$("#guide-doc h3,h4").each(function(index, elem) {

		var tagName = $(this).prop("tagName");
		var name = $(this).text();
		var id = $(this).attr("id");

		if(tagName == "H3" && lastTagName == "H4") {
			grpId++;
		}

		if(tagName == "H3") {
			menuJson[grpId] = {
				name: name,
				path: "",
				target: id,
				desc: ""
			};

		} else {

			if(menuJson[grpId]["submenu"] == undefined) {
				menuJson[grpId]["submenu"] = [];
			}

			menuJson[grpId]["submenu"].push({
				name: name,
				path: "",
				target: id,
				desc: "",
				grpId: grpId
			});
		}

		lastTagName = tagName;

	});
}

function loadTarget(menuName) {
	if(menuName != "") {
		var nowMenu = menu.getMenu(menuName);
		menu.scrollToId(nowMenu.target, false);
		menu.menuSelected(menuName);
	}
}

function downloadAPI(apiName){
	download(`/apiDownload?apiName=${apiName}`)
}

function downloadSettingFile(apiName){
	download(`/settingFileDownload`)
}

function download(url){
	// 새로운 <a> 엘리먼트 생성
	var link = document.createElement("a");
	// 다운로드할 파일의 URL 설정
	link.href = url;
	// 다운로드할 파일의 이름 설정
	link.download = url.substring(url.lastIndexOf("/") + 1);
	// body에 추가
	document.body.appendChild(link);
	// 클릭 이벤트 발생시킴
	link.click();
	// 사용이 끝나면 제거
	document.body.removeChild(link);
}
