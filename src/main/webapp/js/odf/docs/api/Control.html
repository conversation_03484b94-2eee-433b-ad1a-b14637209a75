<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: Control</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">Control</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>Control</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>사용자가 정의한 컨트롤을 생성한다.</p>
<pre class="prettyprint source lang-javascript"><code>//서브그룹 컨트롤
      class NewControl extends odf.Control {
        constructor() {
          super({
            setMap: () => {},
            removeMap: () => {},
            clear: () => {},
          });
        }
      }
      //서브그룹 컨트롤일 경우,
      NewControl.subGroup = [
        {
          id: 'new01',
          name: '뉴1',
          click: (evt) => {

            alert('서브그룹 컨트롤 클릭  뉴1');
          },
        },
        {
          id: 'new02',
          name: '뉴2',
          click: (evt) => {
            alert('서브그룹 컨트롤 클릭  뉴2');
          },
        },
      ];
      //controlId와 groupIdx는 서브그룹 컨트롤이든, 단일컨트롤이든 필수 정의
      NewControl.controlId = 'newControl';// (필수값)사용자정의 컨트롤 id
      NewControl.groupIdx = 3;// (필수값)사용자정의 컨트롤 그룹 순번

      //해당 컨트롤을 map 객체에 등록 분할지도 등에 사용하고 싶을 경우 (한번만 호출)
      NewControl.registControl(map, NewControl);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//단일 컨트롤
   class NewControl extends odf.Control {
     constructor() {
       super({
         setMap: () => {},
         removeMap: () => {},
         clear: () => {},
       });
     }
   }
   //단일 컨트롤이 아닐 경우
   NewControl.click = function () {
     alert('단일컨트롤 클릭');
   };
   //controlId와 groupIdx는 서브그룹 컨트롤이든, 단일컨트롤이든 필수 정의
   NewControl.controlId = 'newControl';// (필수값)사용자정의 컨트롤 id
   NewControl.groupIdx = 3;// (필수값)사용자정의 컨트롤 그룹 순번

   //해당 컨트롤을 map 객체에 등록 분할지도 등에 사용하고 싶을 경우 (한번만 호출)
   NewControl.registControl(map, NewControl);
</code></pre></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>사용자 정의 컨트롤 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="Control">new Control<span class="signature">(setMap, removeMap, clear)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">setMap</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용자 정의 컨트롤을 지도에 셋팅한 후 호출되는 callback function</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">removeMap</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용자 정의 컨트롤을 지도에서 삭제한 후 호출되는 callback function</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">clear</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>다른 컨트롤을 클릭하여 사용자정의 컨트롤이 닫힐 경우 호출되는 callback function</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".registControl">registControl<span class="signature">(map, _class)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>control 등록</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>DrawControl 객체생성 옵션</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">_class</span>
            

            
                


    <span class="param-type">
        <code>Class</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용자 정의 컨트롤을 상속받은 클래스</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>






    <h4 class="method-heading">Example</h4>
    
    <pre><code class="language-js">class NewControl extends odf.Control {
        ...
      }
      //controlId와 groupIdx는 서브그룹 컨트롤이든, 단일컨트롤이든 필수 정의
      NewControl.controlId = 'newControl';// (필수값)사용자정의 컨트롤 id
      NewControl.groupIdx = 3;// (필수값)사용자정의 컨트롤 그룹 순번

      //해당 컨트롤을 map 객체에 등록 분할지도 등에 사용하고 싶을 경우 (한번만 호출)
      NewControl.registControl(map, NewControl);
```</code></pre>
















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="clear">clear<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>생성자의 매개변수로 넘어온 clear 함수 호출</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>생성자의 매개변수로 넘어온 clear 함수 호출</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 newControl.clear(); //생성자의 매개변수로 넘어온  clear 함수 호출
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getElement">getElement<span class="signature">(contentId)</span><span class="return-type-signature"> &rarr; {HTMLElement}</span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>버튼 elment반환</p>
<pre class="prettyprint source lang-javascript"><code>//서브그룹컨트롤
 class NewControl extends odf.Control {
       ...</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>버튼 elment반환</p>
<pre class="prettyprint source lang-javascript"><code>//서브그룹컨트롤
 class NewControl extends odf.Control {
       ...
      }
      NewControl.subGroup = [
        {
          id: 'new01',
          name: '뉴1',
          click: (evt) => {

            alert('서브그룹 컨트롤 클릭  뉴1');
          },
        },
        {
          id: 'new02',
          name: '뉴2',
          click: (evt) => {
            alert('서브그룹 컨트롤 클릭  뉴2');
          },
        },
      ];
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 //setMap을 호출한 후에 생성되는 function
 newControl.getElement('new01'); //new01라는 id를 갖는 버튼 element 반환
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//단일 컨트롤
 class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 //setMap을 호출한 후에 생성되는 function
 newControl.getElement(); //버튼 element 반환
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">contentId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 버튼 id (단일 컨트롤의 경우 undefined)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>HTMLElement</code>
            
            
                <p>contentId에 해당하는 button Element</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getElements">getElements<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;HTMLElement>}</span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>서브그룹 컨트롤일 경우, 사용자정의 컨트롤 button Element 배열 반환</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>서브그룹 컨트롤일 경우, 사용자정의 컨트롤 button Element 배열 반환</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...
      }
      NewControl.subGroup = [
        {
          id: 'new01',
          name: '뉴1',
          click: (evt) => {

            alert('서브그룹 컨트롤 클릭  뉴1');
          },
        },
        {
          id: 'new02',
          name: '뉴2',
          click: (evt) => {
            alert('서브그룹 컨트롤 클릭  뉴2');
          },
        },
      ];
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 //setMap을 호출한 후에 생성되는 function
 newControl.getElements(); //사용자정의 컨트롤에 연결된 map 객체 반환
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;HTMLElement></code>
            
            
                <p>사용자정의 컨트롤 button Element</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMap">getMap<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="Map.html">Map</a>}</span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>사용자정의 컨트롤 연결된 map 반환</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>사용자정의 컨트롤 연결된 map 반환</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Map</code>
            
            
                <p>사용자정의 컨트롤에 연결된 map 객체</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeMap">removeMap<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>map과 연결 해제 한 후 생성자의 매개변수로 넘어온 removeMap 호출</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>map과 연결 해제 한 후 생성자의 매개변수로 넘어온 removeMap 호출</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 //setMap을 호출한 후에 생성되는 function
 newControl.removeMap(); //생성된 컨트롤 객체를 삭제
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMap">setMap<span class="signature">(map)</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>사용자정의 컨트롤에 map 연결 한 후 생성자의 매개변수로 넘어온 setMap 호출</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>사용자정의 컨트롤에 map 연결 한 후 생성자의 매개변수로 넘어온 setMap 호출</p>
<pre class="prettyprint source lang-javascript"><code> class NewControl extends odf.Control {
       ...
      }
      ...
 let newControl = new NewControl({}); //사용자정의 컨트롤 생성
 newControl.setMap(map); //사용자정의 컨트롤에 map 객체 연결
 newControl.getMap(map); //사용자정의 컨트롤에 연결된 map 객체 반환
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code><a href="Map.html">Map</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>사용자정의 컨트롤에 연결할 Map 객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:51 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>