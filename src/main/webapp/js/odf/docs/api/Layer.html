<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: Layer</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">Layer</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>Layer</h2> -->

        

        

        
            
            <div class="class-description"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="Layer">new Layer<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>

















<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="addFeature">addFeature<span class="signature">(feature)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 벡터레이어에 feature 추가</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.addFeature(feature);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">feature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>추가할 feature</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="addFeatures">addFeatures<span class="signature">(features)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 벡터레이어에 feature들 추가</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.addFeatures([feature,feature ...]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">features</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="Feature.html">Feature</a>></code>
    </span>
    

            

            

            

            <div class="param-description"><p>추가할 feature 배열</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="buffer">buffer<span class="signature">(meter)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="Feature.html">Feature</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 벡터레이어의 모든 피쳐에 버퍼 적용</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
let style = odf.StyleFacotry.produce(...);
sample.buffer(50);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">meter</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>적용할 버퍼의 크기</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Feature></code>
            
            
                <p>해당 벡터레이어의 모든 피처의 버퍼 도형들의 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="clear">clear<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>feature 리로드. server에서 끌어오는 데이터는 지우고 다시불러오고, 로컬데이터를 불러온 경우 모든 피쳐 삭제됨</p>
<pre class="prettyprint source lang-javascript"><code>let wfsLayer = odf.LayerFactory.produce(...);
wfsLayer.clear();//모든 feature 제거
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="clearFeatures">clearFeatures<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>모든 feature 제거</p>
<pre class="prettyprint source lang-javascript"><code>let wfsLayer = odf.LayerFactory.produce(...);
wfsLayer.clearFeatures();//모든 feature 제거
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="cropByFeature">cropByFeature<span class="signature">(feature)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer를 feature 모양만큼만 보이게 처리</p>
<pre class="prettyprint source lang-javascript"><code>//원형 feature 생성
let circleFeature = odf.FeatureFactory.produce({
           geometryType: 'circle',
           coordinates: [949597.4353529032, 1933961.3294866967],
           circleSize: 3732
       });
layer.cropByFeature(circleFeature);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//다각형 feature 생성
let polygonFeature = odf.FeatureFactory.produce({
       geometryType: 'polygon',
       coordinates: [
         [
           [949301.5734418407, 1935602.8858964627],
           [950628.1800753145, 1936891.316799477],
           [951353.5189540483, 1935240.216457096],
           [953911.2928948466, 1934782.1076915797],
           [951782.9959217197, 1933675.0115082492],
           [951391.694684508, 1932291.141279086],
           [950532.7407491653, 1933340.973866727],
           [947125.5568056392, 1933207.3588101182],
           [948824.3768110948, 1934304.9110608338],
           [947163.7325360989, 1935784.2206161462],
           [949301.5734418407, 1935602.8858964627],
         ],
       ],
     });
layer.cropByFeature(polygonFeature);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">feature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>geometry 타입이 circle 또는 polygon 또는 multiPolygon인 feature</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="defineQuery">defineQuery<span class="signature">(fParam)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>layer 필터링</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
//newSource로 sample 레이어의 Source 변경
sample.defineQuery({
condition : '컬럼명=속성값'
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">fParam</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>레이어 필터링 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">condition</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>어떤 조건으로 필터링할 것인가에 대한 정의. CQL_FILTER 형식의 문자열</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="fit">fit<span class="signature">(duration)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer가 한눈에 보이는 보여주는 extent로 화면 위치 이동 및 줌 레벨 변경</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.fit();
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">duration</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>부드럽게 이동. 기본값 0</p>
<ul>
<li>값의 크기가 클수록 천천히 이동</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="fromKML">fromKML<span class="signature">(kml, dataProjectionCode, featureProjectionCode)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>KML 형식 데이터 불러오기</p>
<pre class="prettyprint source lang-javascript"><code>let wfsLayer = odf.LayerFactory.produce(...);
var newKmlText = '&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&lt;kml xmlns=&quot;http://www.opengis.net/kml/2.2&quot;  xmlns:gx=&quot;http://www.google.com/kml/ext/2.2&quot; xmlns:kml=&quot;http://www.opengis.net/kml/2.2&quot;        xmlns:atom=&quot;http://www.w3.org/2005/Atom&quot;>&lt;Document>&lt;name>&lt;![CDATA[odf-emptyLayer-vector16054860583025tj71on5y7q]]>&lt;/name>&lt;description>&lt;![CDATA[odf-emptyLayer-vector16054860583025tj71on5y7q]]>&lt;/description>&lt;visibility>1&lt;/visibility>&lt;open>1&lt;/open>&lt;Style id=&quot;line&quot;>&lt;LineStyle>&lt;color>ffff55ff&lt;/color>&lt;width>3&lt;/width>&lt;/LineStyle>&lt;/Style>&lt;Folder>&lt;description>LineString&lt;/description>&lt;visibility>1&lt;/visibility>&lt;open>0&lt;/open>&lt;name>LineString&lt;/name>&lt;Placemark>&lt;styleUrl>#line&lt;/styleUrl>&lt;MultiGeometry>&lt;LineString>&lt;coordinates>1097802.7789468267,1717572.789176268 1097621.4442271432,1717505.9816479636 1097535.5488336089,1717315.1029956653 1097549.8647325314,1717071.7327139848 1097821.8668120564,1716947.6615899908 1098093.8688915817,1717033.5569835252 1098198.8521503457,1717367.5946250472 1098031.8333295847,1717477.3498501189 1097745.5153511371,1717472.5778838114 1097630.988159758,1717291.243164128 1097645.3040586805,1717133.7682759818 1097812.3228794415,1717086.048612907 1097974.5697338951,1717210.119736901 1097955.4818686654,1717353.278726125 1097817.094845749,1717310.3310293579 1097840.9546772863,1717233.9795684384 &lt;/coordinates>&lt;/LineString>&lt;LineString>&lt;coordinates>1098590.1533875575,1717768.4397948738 1098337.2391732621,1717749.3519296441 1098284.74754388,1717568.0172099606 1098408.818667874,1717200.5758042862 1098833.523669238,1717114.6804107518 1099105.5257487632,1717319.874961973 1099134.157546608,1717658.6845698026 1098924.1910290797,1717725.492098107 1098509.0299603308,1717653.9126034952 1098370.6429374146,1717458.2619848894 1098518.5738929457,1717281.6992315133 1098809.6638377008,1717286.4711978207 1099014.8583889215,1717525.0695131938 1098876.4713660053,1717630.0527719578 1098628.3291180173,1717534.6134458086 1098614.0132190948,1717381.91052397 1098709.452545244,1717381.91052397 1098823.9797366231,1717510.7536142713 &lt;/coordinates>&lt;/LineString>&lt;/MultiGeometry>&lt;/Placemark>&lt;/Folder>&lt;/Document>&lt;/kml>';
wfsLayer.fromKML(newKmlText, 'EPSG:5179', 'EPSG:5179');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">kml</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>kml 형식의 문자열</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">dataProjectionCode</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>원본 kml data 프로젝션 코드
(ex) 'EPSG:5179' , 'EPSG:4326' ...</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">featureProjectionCode</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도 프로젝션 코드
(ex) 'EPSG:5179' , 'EPSG:4326' ...</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getAttributes">getAttributes<span class="signature">(typeFilter, filterFlag)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#odf_attribute">odf_attribute</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 WFS/WMS 레이어의 속성 정보 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getAttributes();//모든 데이터 타입의 속성 항목 조회
sample.getAttributes(['string']);//string 타입의 속성 항목만 조회
sample.getAttributes(['string','geometry']);//string, geometry 타입 속성 항목만 조회
sample.getAttributes(['string'],false);//string 타입이 아닌 속성 항목만 조회
sample.getAttributes(['string','geometry'],false);//string, geometry 타입이 아닌 속성 항목만 조회
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">typeFilter</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_attribute_dataType">odf_attribute_dataType</a>></code>
    </span>
    

            

            

            

            <div class="param-description"><p>생성할 색의 계열</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">filterFlag</span>
            

            
                


    <span class="param-type">
        <code>boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>typeFilter를 제외한 데이터 타입을 조회(true)할지,
typeFilter에 해당하는 데이터 타입만 조회(false)할지 여부. true가 기본값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;odf_attribute></code>
            
            
                <p>레이어의 속성정보 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getAttributesFeaturesValueRange">getAttributesFeaturesValueRange<span class="signature">(attributeName)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_attribute_range">odf_attribute_range</a>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 WFS 레이어의 특성 속성 값의 범위 조회(실제 레이어가 갖는 feature 기준)</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
//name 속성의 값 정보
sample.getAttributesFeaturesValueRange('name');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">attributeName</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 속성명</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_attribute_range</code>
            
            
                <p>조회된 속성 값의 범위</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getAttributesRange">getAttributesRange<span class="signature">(attributeName, limit)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_attribute_range">odf_attribute_range</a>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 WFS/WMS 레이어의 특성 속성 값의 범위 조회(지오서버로 조회한 feature 데이터 기준)</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
//name 속성의 값 정보
sample.getAttributesRange('name');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">attributeName</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 속성명</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">limit</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회 피쳐 개수 제한 최소 0, 최대 100000</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_attribute_range</code>
            
            
                <p>조회된 속성 값의 범위</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getCenter">getCenter<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;Number>}</span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 중심점 조회 기능</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
       imageLayer.setMap(map);
       imageLayer.getCenter();//[이미지중심x좌표, 이미지 중심 y좌표]
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Number></code>
            
            
                <p>이미지 레이어 중심점</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getClosestFeatureToCoordinate">getClosestFeatureToCoordinate<span class="signature">(coordinate)</span><span class="return-type-signature"> &rarr; {ODF.Feature}</span>
    </h4>





<div class="method-description">
    
    <p>레이어의 도형중 입력 좌표와 가장 가까운 도형 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getClosestFeatureToCoordinate([x좌표,y좌표]);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">coordinate</span>
            

            
                


    <span class="param-type">
        <code>coordinate</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 좌표</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>ODF.Feature</code>
            
            
                <p>레이어의 도형중 입력 좌표와 가장 가까운 도형</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getCQLFilter">getCQLFilter<span class="signature">()</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>레이어에 적용된 cql 필터 값을 반환</p>
<pre class="prettyprint source lang-javascript"><code>layer.getCQLFilter();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>레이어에 적용된 cql 필터 값</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getCrop">getCrop<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;Number>}</span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 잘린 영역 조회</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
       imageLayer.setMap(map);

       imageLayer.setCrop([minX, minY, maxX, maxY]);//자를 이미지 영역 셋팅
       imageLayer.getCrop();//이미지 레이어 잘린 영역 조회
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Number></code>
            
            
                <p>잘린 이미지 영역</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getDefaultSLD">getDefaultSLD<span class="signature">()</span><span class="return-type-signature"> &rarr; {Promise}</span>
    </h4>





<div class="method-description">
    
    <p>해당  wms 레이어와 연결된 사용자정의 스타일 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
let sld = odf.StyleFactory.produceSLD({
name: &quot;My Style&quot;,
rules: [
  {
    name: &quot;My Rule&quot;,
    symbolizers: [
      {
        kind: &quot;Mark&quot;,
        wellKnownName: &quot;Circle&quot;,
        color: &quot;#FF0000&quot;,
        radius: 6
      }
    ]
  }
]
});
sample.setStyle(sld);
sample.getDefaultSLD().then(sld=>{
//control sld
//sld.download();
});
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Promise</code>
            
            
                <p>해당 wms 레이어에 적용된 사용자정의 스타일을 PromiseValue로 갖는 promise객체</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getEditMenuTarget">getEditMenuTarget<span class="signature">()</span><span class="return-type-signature"> &rarr; {HTMLElement}</span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 편집 오버레이 target html 조회</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
       imageLayer.setMap(map);
       let element = imageLayer.getEditMenuTarget();//이미지 레이어 편집 오버레이 target html element조회
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>HTMLElement</code>
            
            
                <p>이미지 레이어 편집 오버레이 target html</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getEditOverlay">getEditOverlay<span class="signature">()</span><span class="return-type-signature"> &rarr; {ExtOverlay}</span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 편집 오버레이 조회</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
       imageLayer.setMap(map);
       let overlay = imageLayer.getEditOverlay();//이미지 레이어 편집 오버레이 조회
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>ExtOverlay</code>
            
            
                <p>편집 오버레이 객체</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getExtent">getExtent<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;Number>}</span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 영역 조회</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
      imageLayer.setMap(map);

      imageLayer.setExtent([minX, minY, maxX, maxY]);//이미지 표현 extent 설정
      imageLayer.getExtent();//설정된 extent 조회
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Number></code>
            
            
                <p>이미지 영역</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getFeatureById">getFeatureById<span class="signature">(featureId)</span><span class="return-type-signature"> &rarr; {<a href="Feature.html">Feature</a>}</span>
    </h4>





<div class="method-description">
    
    <p>feature id로 feature 객체 리턴</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getFeatureById('id01');
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">featureId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회할 Featuer Id</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Feature</code>
            
            
                <p>입력한 featureId 값을 id로 갖는 Feature</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getFeatureInfoUrl">getFeatureInfoUrl<span class="signature">(coordinate, resolution, projection, params)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>GetFeatureInfo URL을 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getFeatureInfoUrl();
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">coordinate</span>
            

            
                


    <span class="param-type">
        <code>Array</code>
    </span>
    

            

            

            

            <div class="param-description"><p>좌표</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">resolution</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>축척</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">projection</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>좌표계</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">params</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>GetFeatureInfo URL 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">INFO_FORMAT</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>출력물 포멧</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">QUERY_LAYERS</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>쿼리할 레이어</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">LAYERS</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>표시할 레이어 (QUERY_LAYERS 와 동일)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">VERSION</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>서비스의 버전</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getFeatures">getFeatures<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="Feature.html">Feature</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 벡터레이어가 소유한 feature 배열 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getFeatures();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Feature></code>
            
            
                <p>해당 벡터레이어가 소유한 feature 배열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getFitExtent">getFitExtent<span class="signature">(calculateFlag)</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_extent">odf_extent</a>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer가 한눈에 보이는 보여주는 extent 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getFitExtent();
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">calculateFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>extent값 계산 여부 .기본값 false</p>
<ul>
<li>true : (wfs 레이어일 경우) 로드된 feature 기준으로 extent 계산 값 이용 / (geoImage 레이어인 경우) 로드된 이미지 기준으로 extent 계산</li>
<li>false : geoserver에 등록된 extent 값 이용</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_extent</code>
            
            
                <p>해당 layer가 한눈에 보이는 보여주는 extent형식의 문자열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getGeometryName">getGeometryName<span class="signature">()</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 geometry 칼럼명 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getGeometryName();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>해당 레이어의 geometry 컬럼명</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getGeometryType">getGeometryType<span class="signature">()</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 geometry 타입 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getGeometryName();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>해당 레이어의 geometry 타입</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getInitialOption">getInitialOption<span class="signature">()</span><span class="return-type-signature"> &rarr; {Object}</span>
    </h4>





<div class="method-description">
    
    <p>LayerFactory를 통해 Layer 생성한 경우 , 생성 옵션 값을 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(type, {
 server: 'GeoServer URL 입력',
 layer: 'Store 명칭:Layer 명칭 입력',
 service: '서비스 종류',
}); // sample은 Layer 클래스로 만들어진 객체
sample.getInitialOption();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Object</code>
            
            
                <p>레이어 생성 옵션</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMap">getMap<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="Map.html">Map</a>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer와 연결된 map 객체 반환</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
sample.getMap();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Map</code>
            
            
                <p>해당 layer와 연결된 map 객체</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMaxResolution">getMaxResolution<span class="signature">()</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer 뷰잉 최대 해상도 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getMaxResolution();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <p>최대 해상도 설정값</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getMinResolution">getMinResolution<span class="signature">()</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer 뷰잉 최소 해상도 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getMinResolution();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <p>최소 해상도 설정값</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getODFId">getODFId<span class="signature">()</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 고유 id 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getODFId();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>해당 layer의 고유 id</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getOpacity">getOpacity<span class="signature">()</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 투명도 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getOpacity();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <p>해당 레이어의 투명도 값 조회</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getOriginCRS">getOriginCRS<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer 지오서버 발행 좌표계 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getOriginCRS();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array</code>
            
            
                <p>해당 layer의 서버 발행 원본 좌표계</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getProperties">getProperties<span class="signature">()</span><span class="return-type-signature"> &rarr; {Object}</span>
    </h4>





<div class="method-description">
    
    <p>해당 객체가 갖고있는 모든 속성명과 속성값 정보 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getProperties();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Object</code>
            
            
                <p>해당 객체가 갖고있는 모든 속성명과 속성값 정보</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getRotation">getRotation<span class="signature">()</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 회전각 조회 기능</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
       imageLayer.setMap(map);
       imageLayer.getRotaion();//0
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <p>이미지 레이어 회전각</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getScale">getScale<span class="signature">()</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 크기 조회 기능</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
      imageLayer.getScale();//이미지 크기 값 조회
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <p>이미지 크기</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getSLD">getSLD<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="SLD.html">SLD</a>}</span>
    </h4>





<div class="method-description">
    
    <p>해당  wms 레이어와 연결된 사용자정의 스타일 조회</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
let sld = odf.StyleFactory.produceSLD({
name: &quot;My Style&quot;,
rules: [
  {
    name: &quot;My Rule&quot;,
    symbolizers: [
      {
        kind: &quot;Mark&quot;,
        wellKnownName: &quot;Circle&quot;,
        color: &quot;#FF0000&quot;,
        radius: 6
      }
    ]
  }
]
});
sample.setStyle(sld);
sample.getSLD();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>SLD</code>
            
            
                <p>해당 wms 레이어에 적용된 사용자정의 스타일</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getStyle">getStyle<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 레이어와 연결된 스타일 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.getStyle();//따로 스타일 설정 안 했으니, 기본 스타일 반환
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getVisible">getVisible<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 가시성 정보 조회</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
sample.getVisible();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getZIndex">getZIndex<span class="signature">()</span><span class="return-type-signature"> &rarr; {Number}</span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 z-index 반환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
sample.getZIndex();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Number</code>
            
            
                <p>해당 layer의 z-index.</p>
<ul>
<li>setMap을 한 순서 대로 drawing 한다. ( 1 -&gt; 2 -&gt; 3 )</li>
<li>보이는 순서는 역순 이다. ( 3 -&gt; 2 -&gt; 1)</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="layerFilter">layerFilter<span class="signature">(filterFunction)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 벡터레이어를 Style을 이용해 필터링
필터링된 데이터는 화면상에서 보이지 않을 뿐 feature 목록에 여전히 존재</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.layerODFFilter((feature)=>{
  return feature.getProperties().ADM_CD===&quot;4117310200&quot;
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">filterFunction</span>
            

            
                


    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>필터링 function</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="on">on<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer에 이벤트 연결</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.on('render',function(){
//do something
});
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="refresh">refresh<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer refresh</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.refresh();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeFeature">removeFeature<span class="signature">(feature)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>특정 feature 제거</p>
<pre class="prettyprint source lang-javascript"><code>let wfsLayer = odf.LayerFactory.produce(...);
let feature = wfsLayer.getFeatures()[0];
wfsLayer.removeFeature(feature);//특정 feature 제거
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">feature</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>제거할 feature</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeFeatureById">removeFeatureById<span class="signature">(featureId)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>특정 featureId를 갖는 feature 제거</p>
<pre class="prettyprint source lang-javascript"><code>let wfsLayer = odf.LayerFactory.produce(...);
wfsLayer.removeFeatureById(wfsLayer.getFeatures()[0].getId());//특정 feature 제거
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">featureId</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>제거할 feature의 id</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="revertLayer">revertLayer<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>feature 모양만큼만 보이게 처리한 레이어를 원래 상태로 원복</p>
<pre class="prettyprint source lang-javascript"><code>//원형 feature 생성
let circleFeature = odf.FeatureFactory.produce({
           geometryType: 'circle',
           coordinates: [949597.4353529032, 1933961.3294866967],
           circleSize: 3732
       });
layer.cropByFeature(circleFeature);
...</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>feature 모양만큼만 보이게 처리한 레이어를 원래 상태로 원복</p>
<pre class="prettyprint source lang-javascript"><code>//원형 feature 생성
let circleFeature = odf.FeatureFactory.produce({
           geometryType: 'circle',
           coordinates: [949597.4353529032, 1933961.3294866967],
           circleSize: 3732
       });
layer.cropByFeature(circleFeature);
...
layer.revertLayer();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setCenter">setCenter<span class="signature">(center)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 중심점 수정 기능</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
       imageLayer.setMap(map);
       imageLayer.setCenter([수정_이미지중심x좌표, 수정_이미지 중심 y좌표]);//중심점 수정
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">center</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

            

            

            

            <div class="param-description"><p>이미지 레이어 중심점</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setCrop">setCrop<span class="signature">(crop)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 잘린 영역 조회</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
       imageLayer.setMap(map);

       imageLayer.setCrop([minX, minY, maxX, maxY]);//자를 이미지 영역 셋팅
       imageLayer.getCrop();//이미지 레이어 잘린 영역 조회
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">crop</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

            

            

            

            <div class="param-description"><p>잘린 이미지 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setExtent">setExtent<span class="signature">(extent)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 영역 설정</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
      imageLayer.setMap(map);

      imageLayer.setExtent([minX, minY, maxX, maxY]);//이미지 표현 extent 설정
      imageLayer.getExtent();//설정된 extent 조회
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">extent</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

            

            

            

            <div class="param-description"><p>이미지 영역</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMap">setMap<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer를 map 객체와 연결</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMaxResolution">setMaxResolution<span class="signature">(최대)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer 뷰잉 최대 해상도 설정</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.setMaxResolution(9.718);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">최대</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>해상도 설정값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMaxZoom">setMaxZoom<span class="signature">(최대)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer 뷰잉 최대 줌레벨 설정</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.setMaxZoom(10);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">최대</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>줌레벨 설정값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMinResolution">setMinResolution<span class="signature">(최소)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer 뷰잉 최소 해상도 설정</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.setMinResolution(9.718);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">최소</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>해상도 설정값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMinZoom">setMinZoom<span class="signature">(최소)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer 뷰잉 최소 줌레벨 설정</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.setMinResolution(10);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">최소</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>줌레벨 설정값</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setOpacity">setOpacity<span class="signature">(val)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 투명도 설정</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.setOpacity(1);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">val</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>해당 레이어의 투명도 값 설정 (0~1)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setRotation">setRotation<span class="signature">(rotation)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 회전각 수정 기능</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
       imageLayer.setMap(map);
       imageLayer.setRotation(180);//회전각 수정
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">rotation</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이미지 레이어 회전각</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setScale">setScale<span class="signature">(scale)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>이미지 레이어 크기 수정 기능</p>
<pre class="prettyprint source lang-javascript"><code>var imageLayer = odf.LayerFactory.produce('geoImage', {...});
      imageLayer.setMap(map);
      imageLayer.setScale(2);//이미지 크기를 2배로 키움
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">scale</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>이미지 크기</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setSLD">setSLD<span class="signature">(sld)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 wms 레이어와 연결된 스타일 변경</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
let sld = odf.StyleFactory.produceSLD({
         name: &quot;My Style&quot;,
         rules: [
           {
             name: &quot;My Rule&quot;,
             symbolizers: [
               {
                 kind: &quot;Mark&quot;,
                 wellKnownName: &quot;Circle&quot;,
                 color: &quot;#FF0000&quot;,
                 radius: 6
               }
             ]
           }
         ]
       })
sample.setStyle(sld);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">sld</span>
            

            
                


    <span class="param-type">
        <code><a href="SLD.html">SLD</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>sld 객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setSource">setSource<span class="signature">(source)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>layer와 연결된 source 변경</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
//newSource로 sample 레이어의 Source 변경
sample.setSource(newSource);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">source</span>
            

            
                


    <span class="param-type">
        <code>Source</code>
    </span>
    

            

            

            

            <div class="param-description"><p>layer와 연결할 source</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setStyle">setStyle<span class="signature">(_style)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 레이어와 연결된 스타일 변경</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
let style = odf.StyleFacotry.produce(...);
sample.setStyle(style);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">_style</span>
            

            
                


    <span class="param-type">
        <code><a href="Style.html">Style</a></code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;<a href="Style.html">Style</a>></code>
    </span>
    |

    <span class="param-type">
        <code><a href="StyleFunction.html">StyleFunction</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>변경할 스타일 객체</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setVisible">setVisible<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 가시성 정보 수정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
let sample = odf.LayerFactory.produce(...);
sample.setMap(map);

//레이어가 화면상에 보이지 않게 처리
sample.setVisible(false);

//레이어가 화면상에 보이게 처리
sample.setVisible(true);
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setZIndex">setZIndex<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 z-index 수정</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.setMap(map);
sample.setZIndex(10);
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="toGeoJson">toGeoJson<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_layer_geoJson_object">odf_layer_geoJson_object</a>}</span>
    </h4>





<div class="method-description">
    
    <p>해당 벡터레이어를 geojson 타입의 Object로 변환</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.toGeoJson();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_layer_geoJson_object</code>
            
            
                <ul>
<li>변환된 geojson 타입의 Object</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="toKML">toKML<span class="signature">(downloadFile)</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>KML 형식으로 변환</p>
<pre class="prettyprint source lang-javascript"><code>let wfsLayer = odf.LayerFactory.produce(...);
var kmlText = wfsLayer.toKML();
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">downloadFile</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>다운로드 여부(기본값 false)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>kml 형식 문자열</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="un">un<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>해당 layer의 이벤트 연결 해제</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(...);
sample.un('render',function(){
//do something
});
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>