<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: Popup</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">Popup</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>Popup</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>팝업 클래스 생성자</p>
<pre class="prettyprint source lang-javascript"><code>  // String(text) 형태로 HTML을 구성하여 Parameter로 사용할 수 있다.
  let content = '&lt;div>&lt;table>&lt;thead>&lt;/thead>'
    + '&lt;tbody>&lt;tr>&lt;td>&lt;button value=move>이동&lt;/button>&lt;/td>&lt;/tr>&lt;/tbody>'
    + '&lt;/table>&lt;/div>';
  // document.createElement('div')등의 형태로 html을 생성하여
  // Popup Class의 Parameter로 사용할 수 있다.
  
  let options = {
    autoPan: true,
    autoPanAnimation: {
      duration: 250
    }
  };
    let popup = new odf.Popup(Content, options);
popup.openOn(map);
let coord = new odf.Coordinate([126 , 37]]);
popup.setPosition(coord);
</code></pre></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>팝업 생성 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="Popup">new Popup<span class="signature">(content, options<span class="signature-attributes">nullable</span>)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">content</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>HTML</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>Popup으로 생성할 HTML 내용</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>팝업 옵션 정보</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">autoPan</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>true : 지도 화면에서 팝업이 잘려나오게 될 경우, 지도를 이동합니다. (기본값 : false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">autoPanAnimation</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>autoPan 이 ture 여서 지도를 이동하는데 사용되는 애니메이션 입니다. (기본값 : 250, autoPan이 ture일때만 사용가능)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">autoPanMargin</span>
            

            
                


    <span class="param-type">
        <code>number</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>autoPan 이 ture 면서 , 팝업과 지도 사이의 여백(픽셀) 지정 (기본값 : 20, autoPan이 ture일때만 사용가능)</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="close">close<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>열려있는 Popup을 닫는다</p>
<pre class="prettyprint source lang-javascript"><code>let popup = new odf.Popup(...);
popup.isOpen();
popup.close();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getPosition">getPosition<span class="signature">(coord)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>Popup의 위치 가져오기</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);
  console.dir(popup.getPosition());   //openOn() 현재 팝업이 표시된 위치 가져오기
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">coord</span>
            

            
                


    <span class="param-type">
        <code><a href="Coordinate.html">Coordinate</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>위치 좌표 odf.Coordinate 객체로 생성</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="isOpen">isOpen<span class="signature">()</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>Popup Open 되어 있는지 확인</p>
<pre class="prettyprint source lang-javascript"><code>let popup = new odf.Popup(...);
popup.isOpen();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <p>Popup이 열려있는지 결과에 따라 true/false 값 리턴</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="openOn">openOn<span class="signature">(map)</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>Popup를 map 객체와 연결</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map); //openOn() 실행 후 좌표를 지정하지 않았기 때문에 setPosition 함수를 통해 위치를 지정해주어야합니다.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>Popup를 map 객체와 연결</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map); //openOn() 실행 후 좌표를 지정하지 않았기 때문에 setPosition 함수를 통해 위치를 지정해주어야합니다.
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">map</span>
            

            
                


    <span class="param-type">
        <code><a href="Map.html">Map</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>Popup 객체와 map 객체 연결</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setOffset">setOffset<span class="signature">(position)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>popup 배치할 때 사용되는 오프셋(픽셀)입니다. 배열의 첫 번째 요소는 수평 오프셋(양수 값은 오버레이를 오른쪽으로 이동) 배열의 두 번째 요소는 수직 오프셋(양수 값은 오버레이를 아래로 이동합니다.)</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);   //openOn() 실행 후 좌표를 지정해주어야 지도에 표시 됩니다.
  popup.setOffset([0, -10]);
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">position</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>오버레이가 지도의 해당 지점을 기준으로 배치되는 방식 (가능한 값 : 'bottom-left', 'bottom-center', 'bottom-right', 'center-left', 'center-center', 'center-right', 'top-left', 'top-center', 'top-right'</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setPosition">setPosition<span class="signature">(coord)</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>Popup의 위치 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);   //openOn() 실행 후 좌표를 지정해주어야 지도에 표시 됩니다.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>Popup의 위치 설정</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);   //openOn() 실행 후 좌표를 지정해주어야 지도에 표시 됩니다.
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">coord</span>
            

            
                


    <span class="param-type">
        <code><a href="Coordinate.html">Coordinate</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>위치 좌표 odf.Coordinate 객체로 생성</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setPositioning">setPositioning<span class="signature">(position)</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>Popup의 오버레이가 속성과 관련하여 실제로 배치되는 방식을 정의</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);   //openOn() 실행 후 좌표를 지정해주어야 지도에 표시 됩니다.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>Popup의 오버레이가 속성과 관련하여 실제로 배치되는 방식을 정의</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(...);
odf.event.addListener(map, 'click', (evt) => {
  popup.openOn(map);
  let coord = new odf.Coordinate(evt.coordinate);
  popup.setPosition(coord);   //openOn() 실행 후 좌표를 지정해주어야 지도에 표시 됩니다.
  popup.setPositioning('bottom-left');
});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">position</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>오버레이가 지도의 해당 지점을 기준으로 배치되는 방식 (가능한 값 : 'bottom-left', 'bottom-center', 'bottom-right', 'center-left', 'center-center', 'center-right', 'top-left', 'top-center', 'top-right'</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>