<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: LayerFactory</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">LayerFactory</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>LayerFactory</h2> -->

        

        

        
            
            <div class="class-description"><p>레이어 생성 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="LayerFactory">new LayerFactory<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>

















<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getWmtsLayerOpt">getWmtsLayerOpt<span class="signature">(opt)</span><span class="return-type-signature"> &rarr; {Json}</span>
    </h4>





<div class="method-description">
    
    <p>wmts 옵션값 생성(기준, OpenLayers)</p>
<pre class="prettyprint source lang-javascript"><code>let opt = {
 level : 13,
 epsg : 'epsg:5179'
}

let option = odf.LayerFactory.getWmtsLayerOpt(opt);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">opt</span>
            

            
                


    <span class="param-type">
        <code>Json</code>
    </span>
    

            

            

            

            <div class="param-description"><p>옵션을 생성하기 위한 값</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">level</span>
            

            
                


    <span class="param-type">
        <code>option</code>
    </span>
    

            

            

            

            <div class="param-description"><p>WMTS Zoom Level Step</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">epsg</span>
            

            
                


    <span class="param-type">
        <code>option</code>
    </span>
    

            

            

            

            <div class="param-description"><p>WMTS 옵션 생성에 기준이 될 좌표</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Json</code>
            
            
                <p>옵션 객체</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".intersect">intersect<span class="signature">(al, tla)</span><span class="return-type-signature"> &rarr; {<a href="Feature.html">Feature</a>}</span>
    </h4>





<div class="method-description">
    
    <p>레이어 교체값 추출</p>
<pre class="prettyprint source lang-javascript"><code>let list = map.getODFLayers();
let option = odf.LayerFactory.intersect(Target, list);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">al</span>
            

            
                


    <span class="param-type">
        <code><a href="Layer.html">Layer</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="Extent.html">Extent</a></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>조회하고자 하는 영역(Layer or Extent)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">tla</span>
            

            
                


    <span class="param-type">
        <code>Array</code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        null
                    </span>
                
            

            <div class="param-description"><p>비교하고자 하는 LayerList</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Feature</code>
            
            
                <p>교차된 대상</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produce">produce<span class="signature">(type, params)</span><span class="return-type-signature"> &rarr; {<a href="Layer.html">Layer</a>}</span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>레이어 생성 함수</p>
<pre class="prettyprint source lang-javascript"><code>let sample = odf.LayerFactory.produce(type, {
  server: 'GeoServer URL 입력',  //wfs, wms, wcs, wmts
  layer: 'Store 명칭:Layer 명칭 입력',
  service: '서비스 종류',
  //matrixSet: 'GeoServer에 발행된 타일매트릭스 중 선택', // wmts 일 경우에 사용. 생략하면 지도 좌표계 이용
});
sample.setMap(map);
</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>레이어 객체 생성</p>
<pre class="prettyprint source lang-javascript"><code>let option = {
  server: 'GeoServer URL 입력',
  layer: 'Store 명칭:Layer 명칭 입력',
  service: '서비스 종류', //wfs, wms, wcs, wmts
  //matrixSet: 'GeoServer에 발행된 타일매트릭스 중 선택', // wmts 일 경우에 사용. 생략하면 지도 좌표계 이용
}

let sample = odf.LayerFactory.produce(type, option);
sample.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//그룹레이어
let groupLayer = odf.LayerFactory.produce('geoserver', { // 레이어 호출 방법 (ex. geoserver, geojson)
 server: 'GeoServer URL 입력',  //wfs, wms, wcs
  layer: 그룹레이어 명 ('저장소:레이어명')
  service: 'group',
});
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//kml 텍스트
var kmlText = '&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&lt;kml xmlns=&quot;http://www.opengis.net/kml/2.2&quot;  xmlns:gx=&quot;http://www.google.com/kml/ext/2.2&quot; xmlns:kml=&quot;http://www.opengis.net/kml/2.2&quot;        xmlns:atom=&quot;http://www.w3.org/2005/Atom&quot;>&lt;Document>&lt;name>&lt;![CDATA[odf-emptyLayer-vector1605257609045f9x2gq94bc]]>&lt;/name>&lt;description>&lt;![CDATA[odf-emptyLayer-vector1605257609045f9x2gq94bc]]>&lt;/description>&lt;visibility>1&lt;/visibility>&lt;open>1&lt;/open>&lt;Style id=&quot;area&quot;>&lt;LineStyle>&lt;color>ff0000ff&lt;/color>&lt;width>3&lt;/width>&lt;/LineStyle>&lt;PolyStyle>&lt;color>55ffff55&lt;/color>&lt;/PolyStyle>&lt;/Style>&lt;Folder>&lt;description>Polygon&lt;/description>&lt;visibility>1&lt;/visibility>&lt;open>0&lt;/open>&lt;name>Polygon&lt;/name>&lt;Placemark>&lt;styleUrl>#area&lt;/styleUrl>&lt;MultiGeometry>&lt;Polygon>&lt;outerBoundaryIs>&lt;LinearRing>&lt;coordinates>1102899.2389631933,1717024.0130509103 1102999.45025565,1717133.768275982 1102975.5904241127,1716985.8373204507 1103142.6092448737,1716947.661589991 1102985.1343567276,1716899.9419269164 1103018.5381208798,1716732.9231061554 1102927.870761038,1716847.4502975345 1102846.7473338114,1716728.151139848 1102832.4314348889,1716866.5381627642 1102698.81637828,1716966.749455221 1102899.2389631933,1717024.0130509103 &lt;/coordinates>&lt;/LinearRing>&lt;/outerBoundaryIs>&lt;/Polygon>&lt;Polygon>&lt;outerBoundaryIs>&lt;LinearRing>&lt;coordinates>1103548.2263810078,1717004.9251856806 1103572.0862125452,1717181.4879390565 1103681.8414376169,1717033.5569835252 1103901.35188776,1717071.7327139848 1103786.8246963809,1716938.117657376 1103872.7200899152,1716842.6783312266 1103724.789134384,1716880.8540616864 1103610.2619430048,1716670.8875441581 1103605.4899766974,1716866.538162764 1103385.9795265542,1716871.3101290714 1103548.2263810078,1717004.9251856806 &lt;/coordinates>&lt;/LinearRing>&lt;/outerBoundaryIs>&lt;/Polygon>&lt;Polygon>&lt;outerBoundaryIs>&lt;LinearRing>&lt;coordinates>1103223.7326721007,1716346.393835251 1103223.7326721007,1716494.324790782 1103319.1719982498,1716351.1658015584 1103424.1552570139,1716379.7975994032 1103385.9795265542,1716260.4984417167 1103467.1029537811,1716198.4628797197 1103323.9439645573,1716212.778778642 1103228.504638408,1716088.707654648 1103199.8728405633,1716212.778778642 1103047.1699187246,1716260.4984417167 1103223.7326721007,1716346.393835251 &lt;/coordinates>&lt;/LinearRing>&lt;/outerBoundaryIs>&lt;/Polygon>&lt;/MultiGeometry>&lt;/Placemark>&lt;Placemark>&lt;styleUrl>#area&lt;/styleUrl>&lt;Polygon>&lt;outerBoundaryIs>&lt;LinearRing>&lt;coordinates>1099959.7077177984,1716260.498441717 1100098.0947407146,1716608.8519821614 1100675.5026639171,1716794.9586681523 1101190.875025123,1716561.132319087 1101114.5235642034,1716308.2181047916 1100661.1867649949,1715859.6532718902 1099773.6010318075,1715530.3875966757 1099353.667996751,1716160.2871492603 1099277.3165358317,1716584.992150624 1099329.8081652136,1717028.785017218 1099730.6533350402,1717119.4523770595 1100083.7788417924,1717024.0130509103 1100145.8144037894,1716713.8352409257 1099959.7077177984,1716260.498441717 &lt;/coordinates>&lt;/LinearRing>&lt;/outerBoundaryIs>&lt;/Polygon>&lt;/Placemark>&lt;/Folder>&lt;/Document>&lt;/kml>';
//kml 레이어 생성
var kmlLayer = odf.LayerFactory.produce('kml', {
     	data: kmlText,//kml 형식 텍스트
      dataProjectionCode: 'EPSG:5179', //원본 좌표계
      featureProjectionCode: 'EPSG:5179',// 변환할 좌표계(지도좌표계)
});
kmlLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//csv 텍스트
var csvText = '[[csv 텍스트]]';
//csv 레이어 생성
var csvLayer = odf.LayerFactory.produce('csv', {
	     data: csvText,//csv 형식 텍스트
      dataProjectionCode: 'EPSG:5179', //원본 좌표계
      featureProjectionCode: 'EPSG:5179',// 변환할 좌표계(지도좌표계)
      geometryColumnName : 'the_geom',//geometry column
      delimiter : ','//구분자(기본값 콤마(,))
});
csvLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// api 레이어 생성(국가공간정보 포탈 WMS API)
   var apiLayer = odf.LayerFactory.produce('api', {
   server: 'http://openapi.nsdi.go.kr/nsdi/map/LandInfoBaseMapITRF2000BlueService', // API 주소
   layers: '0', //레이어명. 여러개일경우, 쉼표(,)로 구분
   service: 'wms', // wms/ wfs/xyz/wmts
   crs : 'EPSG:5179', // 요청 좌표계
   //bgcolor : '0xRRGGBB', //배경색
   transparent : 'true',//반환 이미지 배경의 투명 여부-'true'/'false'
   //exceptions:''//예외 발생 시 처리 방법 - 'blank'/'xml'/'inimage'
   //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
   //SERVICE : true,//(기본값 true)
   //REQUEST : true, //(기본값 true)
   //WIDTH : true,//(기본값 true)
   //HEIGHT : true,//(기본값 true)
   //BBOX : true,//(기본값 true)
   //FORMAT : true,//(기본값 false)
   //TRANSPARENT : true,//(기본값 false)
   //STYLES : true,//(기본값 false)
   //CRS : false,//(기본값 false)
   //VERSION : false,//(기본값 false)
   // },
   authkey : '[발급받은 api key]',//발급받은 api key
   });
   apiLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>   //애니메이션 클러스터 레이어 생성
   var pointLayer = odf.LayerFactory.produce('geoserver', {
   method : 'get',
   server : 'https://geoserver.gonp.duckdns.org/geoserver', // 분석결과 레이어가 발행된 주소
   layer : 'geonpass:L100000256', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
   crtfckey : '인증키 입력',
   service : 'animatedCluster',
   //distance : 50, default 50,
   //animationDuration :700  애니메이션 표출 시간 단위 ms
   });
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// api 레이어 생성(VWORLD WMS API)

   var apiLayer = odf.LayerFactory.produce('api', {
   server:{url:'http://api.vworld.kr/req/wms'} , // API 주소
   service: 'wms', // wms/wfs/xyz/wmts

   layers: 'lt_c_adsigg_info,lt_c_ademd_info', //하나 또는 쉼표(,)로 분리된 지도레이어 목록, 최대 4개
   styles: 'lt_c_adsigg,lt_c_ademd_3d', //LAYERS와 1대1 관계의 하나 또는 쉼표(,)로 분리된 스타일 목록
   // version : '1.3.0',//요청 서비스 버전
   crs : 'EPSG:5179',//응답결과 좌표계와 bbox 파라미터의 좌표계
   //transparent : 'true',//지도 배경의 투명도 여부
   //bgcolor:'0xFFFFFF',//배경색
   //exceptions:'text/xml',

   //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
   //SERVICE : true,//(기본값 true)
   //REQUEST : true, //(기본값 true)
   //WIDTH : true,//(기본값 true)
   //HEIGHT : true,//(기본값 true)
   //BBOX : true,//(기본값 true)

   //★BBOX★
   //odf에서 기본 제공하는 bbox 배열은 minx,miny,maxx,maxy 순인 반면에
   //vworld에서는 EPSG:4326/EPSG:5186/EPSG:5187일 경우 bbox 배열을 miny,minx, maxy,maxx 순으로 입력받음
   //해당 경우에는 BBOX 값을 '{{miny}},{{minx}},{{maxy}},{{maxx}}' 와같이 입력하면 x와 y의 순서가 바뀌어 적용됨.
   //이때 bbox는 지도 좌표계를 따르는데, bbox의 좌표계를 설정하기 위해서는 LAYER_PROJECTION값에 'EPSG:4326'과 같이 변환할 좌표계를 입력해야한다.

   //BBOX : '{{miny}},{{minx}},{{maxy}},{{maxx}}',
   //LAYER_PROJECTION : 'EPSG:4326',

   //FORMAT : true,//(기본값 false)
   //TRANSPARENT : true,//(기본값 false)
   //STYLES : true,//(기본값 false)
   //CRS : false,//(기본값 false)
   //VERSION : false,//(기본값 false)
   //},

   domain:'[API KEY를 발급받을때 입력했던 URL]',//API KEY를 발급받을때 입력했던 URL
   KEY : '[발급받은 api key]',//발급받은 api key

   });
   apiLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// api 레이어 생성(VWORLD WFS API)
   var apiLayer = odf.LayerFactory.produce('api', {
   server:{url:'http://api.vworld.kr/req/wfs'} , // API 주소
   service: 'wfs', // wms/wfs/xyz/wmts
   typename: &quot;lt_c_adsigg_info&quot;, //레이어명
   srsname : 'EPSG:5179', //사용 좌표계
   //VERSION : '1.1.0', //버젼
   //EXCEPTIONS:'text/xml',//에러 받는 타입
   output: 'application/json',//응답결과 포맷
   //maxfeatures:50,//출력되는 피처의 최대 개수
   //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
   //  REQUEST : true, //(기본값 : true)
   //  BBOX : true, //(기본값 : true)
   //  SERVICE : true, //(기본값 : true)
   //},
   domain:'[API KEY를 발급받을때 입력했던 URL]' //API KEY를 발급받을때 입력했던 URL
   KEY : '[발급받은 api key]', //발급받은 api key
   });
   apiLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// api 레이어 생성(카카오 지적편집도)
   var apiLayer = odf.LayerFactory.produce('api', {
   server: 'https://map0.daumcdn.net/map_usedistrict/2009alo/L{{15-z}}/{{-y-1}}/{{x}}.png', // API 주소
   service: 'xyz', // wms/wfs/xyz/wmts
   projection : 'EPSG:5181', // 요청 좌표계
   extent : [-30000, -60000, 494288, 988576],
   tileGrid:{
   origin: [-30000, -60000],
   resolutions: [4096, 2048, 1024, 512, 256, 128, 64, 32, 16, 8, 4, 2, 1, 0.5, 0.25, 0.125],
   tileSize : 256,
   },
   });
   apiLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// tms 레이어
let tmsLayer = odf.LayerFactory.produce('api', {
service : 'tms',
projection : 'EPSG:5186',
server: {
url : '서버 경로/{{z}}/{{-y-1}}/{{x}}.png',
//proxyURL: 'proxyUrl.jsp',
//proxyParam: 'url',
},
tileGrid: {
origin :[...],
resolutions : [...],
},
});
tmsLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// wmts api 레이어 생성(바로e맵)
   var apiLayer = odf.LayerFactory.produce('api', {
   server: 'http://mapapi.ngii.go.kr:8013/openapi/Gettile.do', // API 주소
   service: 'wmts', // wms/wfs/xyz/wmts
   projection : 'EPSG:5179', // 요청 좌표계
   layer : 'korean_map',
   style : 'korean',
   //version: '1.0.0',
   //format: 'image/jpg',
   tileGrid: {
   origin: [-200000.0, 4000000.0],
   resolutions: [2088.96, 1044.48, 522.24, 261.12, 130.56, 65.28, 32.64, 16.32, 8.16, 4.08, 2.04, 1.02, 0.51, 0.255, 0.1275, 0.06375,],
   matrixIds: ['L05', 'L06', 'L07', 'L08', 'L09', 'L10', 'L11', 'L12', 'L13', 'L14', 'L15', 'L16', 'L17', 'L18', 'L19', 'L20',],
   },
   //★바로e맵의경우 행망 서비스중 '영상'지도를 제외한 레이어는 COL과 ROW 반전 적용시키고 있음
   //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
   //  TILEROW_TILECOL_INVERTED_STATE : false (기본값 false)
   //}
   });
   apiLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// wmts api 레이어 생성(브이월드)
   var apiLayer = odf.LayerFactory.produce('api', {
   server: 'http://api.vworld.kr/req/wmts/1.0.0/{{key}}/{{layer}}/{{tileMatrix}}/{{tileRow}}/{{tileCol}}.png', // API 주소
   service: 'wmts', // wms/wfs/xyz/wmts
   projection : 'EPSG:3857', // 요청 좌표계
   layer : 'Base',
   //version: '1.0.0',
   //format: 'image/jpg',
   key : '[발급받은 키]',
   tileGrid: {
   origin: [-20037508.342789244, 20037508.342789244],
   resolutions: [2445.98490512564, 1222.99245256282, 611.49622628141, 305.748113140705, 152.8740565703525, 76.43702828517625, 38.21851414258813, 19.109257071294063, 9.554628535647032, 4.777314267823516, 2.388657133911758, 1.194328566955879, 0.5971642834779395, 0.29858214173896974,],
   matrixIds: [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19],
   },
   //originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
   //  TILEROW_TILECOL_INVERTED_STATE : false (기본값 false)
   //}
   });
   apiLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// 이미지 레이어 생성
   var imageLayer = odf.LayerFactory.produce('geoImage', {
   url : '이미지 경로', //이미지 정보
   projection : 'EPSG:5186',// 좌표계 정보
   imageCenter : [이미지중심x좌표, 이미지 중심 y좌표],//이미지 중심점 좌표
   editMenu : ['translate'], // 우클릭시 이미지 위치 이동 및 수정 이벤트 생성
   imageRotate : 0 , //이미지 회전 값
   });
   imageLayer.setMap(map);

   // 이미지레이어 수정 활성화시 트리거
   odf.event.addListener(map, 'editstart', (layerObject) => {});
   // 이미지레이어 수정 종료시 트리거
   odf.event.addListener(map, 'editend', (layerObject) => {
   //수정된 좌표/회전각 정보를 저장
   });
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//vectortile 레이어 생성
var vectortileLayer = odf.LayerFactory.produce('api', {
service: 'vectortile',
server: {
url: '/data/{z}/{x}/{y}.pbf'
},
maxResolution: 610.8116873548161,
minResolution: 0.32,
projection: 'EPSG:5179',
tileGrid: {
extent: [],
tileSize: 256,
minZoom: 8,
maxZoom: 18,
}
})
vectortileLayer.setMap(map);
//벡터타일 레이어에 스타일 설정
let styleFunction = odf.StyleFactory.produceFunction([
{
seperatorFunc  : (feature, resolution)=>{
return feature.get(&quot;layer&quot;) == &quot;레이어명1&quot;;
},
style : {...}
},
...
{
seperatorFunc  : &quot;default&quot;,
style : {...}
}
]);
vectortileLayer.setStyle(styleFunction);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// webGL vector(wfs) 레이어 생성
let webGLVectorLayer = odf.LayerFactory.produce('geoserver', {
method: 'get',
server: '서버 경로',
layer: '레이어 typeName',
crtfckey: '',
service: 'wfs',
webGLRender :true,
//bbox:true,
renderOptions: {
style: {
'stroke-color': 'red',
'stroke-width': 3,
}
}
});
webGLVectorLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// webGL vectortile 레이어 생성
var webGLvectortileLayer = odf.LayerFactory.produce('api', {
service: 'vectortile',
server: {
url: '/data/{z}/{x}/{y}.pbf'
},
maxResolution: 610.8116873548161,
minResolution: 0.32,
webGLRender :true,
projection: 'EPSG:5179',
tileGrid: {
extent: [],
tileSize: 256,
minZoom: 8,
maxZoom: 18,
},
renderOptions: {
style : {
builder : {
'fill-color': ['get', 'fillColor'],
'stroke-color': ['get', 'strokeColor'],
'stroke-width': ['get', 'strokeWidth'],
'circle-radius': 4,
'circle-fill-color': '#777',
},
attributes : {
fillColor: {
size: 2,
callback: (feature) => {
const style = this.getStyle()(feature, 1)[0];
const color = asArray(style?.getFill()?.getColor() || '#eee');
return odf.ColorFactory.packColor(color);
},
},
strokeColor: {
size: 2,
callback: (feature) => {
const style = this.getStyle()(feature, 1)[0];
const color = asArray(style?.getStroke()?.getColor() || '#eee');
return odf.ColorFactory.packColor(color);
},
},
strokeWidth: {
size: 1,
callback: (feature) => {
const style = this.getStyle()(feature, 1)[0];
return style?.getStroke()?.getWidth() * 2 || 0;
},
},
}
}
}
}
})
webGLvectortileLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// webGL tile(wms) 레이어 생성
let webGLTileLayer = odf.LayerFactory.produce('geoserver', {
method: 'get',
server: '서버 경로',
layer: '레이어 typeName',
crtfckey: '',
service: 'wms',
webGLRender :true,
//bbox:true,
});
webGLTileLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// tileDebug 레이어
let tileDebugLayer = odf.LayerFactory.produce('tileDebug', {
projection: 'EPSG:5186',
tileGrid: {
origin: [...],
resolutions: [...],
}
});
tileDebugLayer.setMap(map);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>// geotiff 레이어
let geotiffLayer = odf.LayerFactory.produce('geotiff', {
sources: [
{url: 'data/geotiff/Wgeonedu12-L100002134.tif'}
]
});
geotiffLayer.setMap(map);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">type</span>
            

            
                


    <span class="param-type">
        <code>type</code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        empty
                    </span>
                
            

            <div class="param-description"><p>레이어 생성 방법 종류</p>
<ul>
<li>'geoserver' : GeoServer에 발행되어 있는 데이터를 호출하여 레이어 생성</li>
<li>'geojson' : GeoJson 형태의 데이터를 이용하여 레이어 생성</li>
<li>'kml' : KML 형태의 데이터를 이용하여 레이어 생성</li>
<li>'csv' : CSV 형태의 데이터를 이용하여 레이어 생성</li>
<li>'empty' : 빈 벡터 레이어 생성</li>
<li>'api' : 빈 벡터 레이어 생성</li>
<li>'geoImage' : 이미지 레이어 생성</li>
<li>'geotiff' : geotiff 레이어 생성</li>
<li>'svg' :  SVG 이미지 레이어</li>
</ul></div>
            
        </li>

    

        <li>
            
                <span class="param-name">params</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#geoserverOption">geoserverOption</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#geojsonOption">geojsonOption</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#kmlOption">kmlOption</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#csvOption">csvOption</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#apiOption">apiOption</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#imageOption">imageOption</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#geotiffOption">geotiffOption</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#svgOption">svgOption</a></code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>레이어 생성 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Layer</code>
            
            
                <p>레이어 객체</p>
            
        </li>
    
    </ul>


















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>