<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: DrawControl</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">DrawControl</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>DrawControl</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>지도에 그릴수 있는 다양한 그리기 도구를 제공</p>
<pre class="prettyprint source lang-javascript"><code>let drawControl = new odf.DrawControl({
        //style : {} //그리기 피쳐 odf 스타일 옵션,
        //bufferStyle : {} //버퍼 도형 odf 스타일 옵션,
        //createNewLayer : true //drawControl 생성 시 새로운 레이어 생성 여부
        //message : { //그리기 도구 시작시 툴팁 메세지
          //DRAWSATRT_POINT : '점 그리기 측정입니다.',
          //DRAWSTART_LINESTRING : '',
          //DRAWSTART_POLYGON : '',
          //DRAWSTART_CURVE : '' ,
          //DRAWSTART_TEXT : '',
          //DRAWSTART_BUFFER : '',
          //DRAWSTART_CIRCLE : '',
          //DRAWSTART_BOX : '',
          //DRAWEND_DRAG : '',
          //DRAWEND_DBCLICK : '',
        //},
        //measure : false, 측정 옵션 활성화 여부, (선, 원형만 측정 가능)
        //continuity : false, 연속 측정 여부  (기본값 false),
        //rightClickDelete : false, 우클릭 삭제 기능 활성화 여부,
        //tools : ['text', 'polygon','lineString','point','circle','curve','box','square','buffer'],
});
</code></pre></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>그리기 도구 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="DrawControl">new DrawControl<span class="signature">(options<span class="signature-attributes">nullable</span>)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>DrawControl 객체생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">style</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>그리기 도형 style 설정 옵션</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">bufferStyle</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>버퍼 그리기 도형 style 설정 옵션</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">createNewLayer</span>
            

            
                


    <span class="param-type">
        <code>boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>drawControl 생성 시 마다 새 레이어 생성 여부</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">message</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>툴팁 메세지</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">DRAWSTART_POINT</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>점 그리기 시작 안내 메세지
(기본값) '점을 그리기 위해 지도를 클릭해주세요'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_LINESTRING</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>선 그리기 시작 안내 메세지
(기본값) '선을 그리기 위해 지도를 클릭해주세요'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_POLYGON</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>면 그리기 시작 안내 메세지
(기본값) '면을 그리기 위해 지도를 클릭해주세요'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_CURVE</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>곡선 그리기 시작 안내 메세지
(기본값) '곡선을 그리기 위해 지도를 드래그해주세요'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_TEXT</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>텍스트 그리기 시작 안내 메세지
(기본값) '텍스트를 입력하기 위해 지도를 클릭해주세요.'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_BUFFER</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>버퍼 그리기 시작 안내 메세지
(기본값) '버퍼를 생성하기 위해 레이어를 선택해주세요.'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_CIRCLE</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>원 그리기 시작 안내 메세지
(기본값) '원을 그리기 위해 지도를 클릭해주세요.'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWSTART_BOX</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>사각형 그리기 시작 안내 메세지
(기본값) '사각형을 그리기 위해 지도를 클릭해주세요.'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWEND_DRAG</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>그리기 종료 안내 메세지(드래그)
(기본값) '드래그를 멈추면 그리기가 종료됩니다.'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">DRAWEND_DBCLICK</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>그리기 종료 안내 메세지(더블클릭)
(기본값) '더블클릭시 그리기가 종료됩니다.'</p></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">measure</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>측정 옵션 활성화 여부(기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">continuity</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>연속 그리기 여부 (기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">rightClickDelete</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>우클릭 삭제 기능 활성화 여부 (기본값 false)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">tools</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>생성할 툴 배열</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">text</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>텍스트 그리기 툴</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">polygon</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>다각형 그리기 툴</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">lineString</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>선 그리기 툴</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">box</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>사각형 그리기 툴</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">square</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>정사각형 그리기 툴</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">point</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>점 그리기 툴</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">circle</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>원 그리기 툴</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">curve</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>곡선 그리기 툴</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">buffer</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>버퍼 그리기 툴</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="clear">clear<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>그리기 인터렉션 삭제 및 그리기 오버레이 삭제, 그리던 도형 삭제</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.clear();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="draw">draw<span class="signature">(type)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.draw('text');</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">type</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>그리기 유형</p>
<ul>
<li>'text' : 텍스트</li>
<li>'polygon' : 다각형</li>
<li>'lineString' : 선</li>
<li>'box' : 사각형</li>
<li>'square' : 정사각형</li>
<li>'point' : 점</li>
<li>'circle' : 원</li>
<li>'curve' : 곡선</li>
<li>'buffer' : 버퍼</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="drawBox">drawBox<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 사각형 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.drawBox();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="drawCircle">drawCircle<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 원 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.drawCircle();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="drawCurve">drawCurve<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 곡선 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.drawCurve();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="drawLineString">drawLineString<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 라인 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.drawLineString();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="drawPoint">drawPoint<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 점 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.drawPoint();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="drawPolygon">drawPolygon<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 폴리곤 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.drawPolygon();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="drawSquare">drawSquare<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 정사각형 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.drawSquare();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="drawText">drawText<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 텍스트 그리기</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.drawText();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="findDrawVectorLayer">findDrawVectorLayer<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="Layer.html">Layer</a>}</span>
    </h4>





<div class="method-description">
    
    <p>그리기 도구로 그린 피쳐가 있는 벡터레이어가 있는지 확인,없을 경우 벡터레이어 생성</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.findDrawVectorLayer();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Layer</code>
            
            
                <p>그리기 레이어 반환</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getActiveType">getActiveType<span class="signature">()</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>어떤 그리기가 활성화 상태인지 조회</p>
<pre class="prettyprint source lang-javascript"><code> let drawControl = new odf.DrawControl();
 drawControl.getActiveType();//어떤 그리기가 활성화 상태인지
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>어떤 그리기가 활성화 상태인지</p>
<ul>
<li>'none' : 그리기 활성화상태 x</li>
<li>'text' : 텍스트 그리기 활성화 상태</li>
<li>'polygon' : 다각형 그리기 활성화 상태</li>
<li>'lineString' : 선 그리기 활성화 상태</li>
<li>'point' : 점 그리기 활성화 상태</li>
<li>'circle' : 원 그리기 활성화 상태</li>
<li>'box' : 사각형 그리기 활성화 상태</li>
<li>'square' : 정사각형 그리기 활성화 상태</li>
<li>'curve' : 곡선 그리기 활성화 상태</li>
<li>'buffer' : 버퍼 그리기 활성화 상태</li>
</ul>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getConstructorOptions">getConstructorOptions<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;Object>}</span>
    </h4>





<div class="method-description">
    
    <p>컨트롤 생성 옵션 반환</p>
<pre class="prettyprint source lang-javascript"><code> let drawControl = new odf.DrawControl();
 drawControl.getConstructorOptions();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Object></code>
            
            
                <p>컨트롤 생성 옵션 반환</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeMap">removeMap<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>그리기 도구에 연결된 map 객체 제거</p>
<pre class="prettyprint source lang-javascript"><code>let map = new odf.Map(mapContainer, mapOption);
let drawControl = new odf.drawControl();
drawControl.setMap(map);
drawControl.removeMap();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeToolTip">removeToolTip<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl 툴팁 삭제 함수</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.removeHelpTooltip();</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMap">setMap<span class="signature">(_map, createElementFlag<span class="signature-attributes">nullable</span>)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DrawControl map 객체 연결</p>
<pre class="prettyprint source lang-javascript"><code> let drawControl = new odf.DrawControl({
      });
 drawControl.setMap(map,createElementFlag); //DrawControl 객체에 map 객체 연결
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">_map</span>
            

            
                


    <span class="param-type">
        <code><a href="Map.html">Map</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>DrawControl 객체와 연결할 Map 객체</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">createElementFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>DrawControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setStyle">setStyle<span class="signature">(styleObject)</span><span class="return-type-signature"></span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>DrawControl 스타일 변경 함수</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.setStyle({
             fill : {
          color : [1,10,100,0.4]
      },
      stroke : {
          color : [1,10,100,0.4],
          width : 3,
      },
      point : { // 점 스타일
         icon : {
              width : 20, // scale 옵션과 함께 사용할 수 없습니다.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>DrawControl 스타일 변경 함수</p>
<pre class="prettyprint source lang-javascript"><code>drawControl.setStyle({
             fill : {
          color : [1,10,100,0.4]
      },
      stroke : {
          color : [1,10,100,0.4],
          width : 3,
      },
      point : { // 점 스타일
         icon : {
              width : 20, // scale 옵션과 함께 사용할 수 없습니다.
              height: 20, // scale 옵션과 함께 사용할 수 없습니다.
              scale: 1,
              color: 'red',
              opacity: 1,
              img: HTMLImageElement // src 옵션과 함께 사용할 수 없습니다.
              src: 'images/icon.png' // img 옵션과 함께 사용할 수 없습니다.
         }
      },
      text : {
          text: '',
          fill: { color: [1, 10, 100, 1] },
          stroke: { color: [1, 10, 100, 1] },
          font: '20px sans-serif',
      },
     bufferStyle : {
          stroke: {
              color: [67, 116, 217, 1],
              width: 2,
            },
            fill: { color: [178, 204, 255, 0.3] },
      },

})</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">styleObject</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>스타일을 생성할 Object</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>