<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: StyleFactory</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">StyleFactory</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>StyleFactory</h2> -->

        

        

        
            
            <div class="class-description"><p>스타일 생성 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="StyleFactory">new StyleFactory<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>

















<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".addPattern">addPattern<span class="signature">(title, options)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>사용자 정의 패턴 추가</p>
<pre class="prettyprint source lang-javascript"><code>      //사용자 정의 패턴 추가
      odf.StyleFactory.addPattern (&quot;copy (char pattern)&quot;, { char:&quot;©&quot; });
      odf.StyleFactory.addPattern (&quot;bug (fontawesome)&quot;, { char:'\uf188', size:12, font:&quot;10px FontAwesome&quot; });
      odf.StyleFactory.addPattern (&quot;smiley (width angle)&quot;, { char:'\uf118', size:20, angle:true, font:&quot;15px FontAwesome&quot; });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">title</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>추가할 패턴의 이름</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>추가할 패턴 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">char</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>문자열 패턴 (ex) &quot;©&quot; , '\uf188', '\uf118'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">size</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>패턴의 크기</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">font</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>폰트 크기와 폰트 (ex) &quot;10px FontAwesome&quot;</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">angle</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>회전각</p></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".cvtSLD2Object">cvtSLD2Object<span class="signature">(sld)</span><span class="return-type-signature"> &rarr; {Promise}</span>
    </h4>





<div class="method-description">
    
    <p>SLD 형식 문자열 -&gt; SLD 객체 생성 옵션</p>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">sld</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>SLD 형식 문자열</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Promise</code>
            
            
                <p>변환된 SLD 생성 object를 Promise Value로 갖는 Promise</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".cvtStyle2Json">cvtStyle2Json<span class="signature">(style)</span><span class="return-type-signature"> &rarr; {String}</span>
    </h4>





<div class="method-description">
    
    <p>EXTStyle 또는 Style을 JSON 형태로 변환</p>
<pre class="prettyprint source lang-javascript"><code>  //생성된 스타일 function을 JSON으로 변경
   let style = odf.StyleFactory.produce({
        fill:{color:[0,0,0,0.5]},//채우기
        stroke: {color:'red',width:3,},//윤곽선
    });
    odf.StyleFactory.cvtStyle2Json(style);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">style</span>
            

            
                


    <span class="param-type">
        <code>odf.Style</code>
    </span>
    |

    <span class="param-type">
        <code>odf.ExtStyle</code>
    </span>
    |

    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변환 대상 style 또는 styleFunction</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>String</code>
            
            
                <p>JSON형태로 변환된 스타일 생성옵션</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".cvtStyle2Object">cvtStyle2Object<span class="signature">(style)</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#odf_styleOption">odf_styleOption</a>>|<a href="global.html#odf_styleOption">odf_styleOption</a>|Array.&lt;<a href="global.html#odf_StyleFunctionOption">odf_StyleFunctionOption</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>EXTStyle 또는 Style을 Object 형태로 변환</p>
<pre class="prettyprint source lang-javascript"><code>  //생성된 스타일 function을 JSON으로 변경
   let style = odf.StyleFactory.produce({
        fill:{color:[0,0,0,0.5]},//채우기
        stroke: {color:'red',width:3,},//윤곽선
    });
    odf.StyleFactory.cvtStyle2Object(style);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">style</span>
            

            
                


    <span class="param-type">
        <code>odf.Style</code>
    </span>
    |

    <span class="param-type">
        <code>odf.ExtStyle</code>
    </span>
    |

    <span class="param-type">
        <code>function</code>
    </span>
    

            

            

            

            <div class="param-description"><p>변환 대상 style 또는 FunctionStyle</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;odf_styleOption></code>
            
                <code>odf_styleOption</code>
            
                <code>Array.&lt;odf_StyleFunctionOption></code>
            
            
                <p>Object 형태로 변환된 스타일 생성옵션</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".getValidFillPatternList">getValidFillPatternList<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;<a href="global.html#odf_default_pattern_define">odf_default_pattern_define</a>>}</span>
    </h4>





<div class="method-description">
    
    <p>기본으로 제공하는 패턴명과 이미지 조회</p>
<pre class="prettyprint source lang-javascript"><code>      //기본으로 제공하는 패턴명과 이미지 조회
      odf.StyleFactory.getValidFillPatternList();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;odf_default_pattern_define></code>
            
            
                <p>Object 형태로 변환된 스타일 생성옵션</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produce">produce<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.Style}</span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>스타일 객체 생성</p>
<pre class="prettyprint source lang-javascript"><code>//점 스타일 생성 예제
let pointCircleStyle = odf.StyleFactory.produce({
  image : {
    circle : {
      radius:50,//크기
      fill:{
        color:'gray'//채우기 색
       },//채우기
       stroke: {//윤곽선
         color:'red',//테두리 색
         width:10,//굵기
         lineDash:[4, 8]//점선 설정
       },
       snapToPixel : false //true : sharp, false : blur
     }
   },
   text : {
     text : '텍스트 내용', //텍스트 내용
     font : 'bold 20px Courier New' //폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
   }
 });
 //생성된 스타일을 JSON형태로 변환
 pointCircleStyle.getJSON();
</code></pre>
<pre class="prettyprint source lang-javascript"><code>    //심볼 스타일 생성 예제
    let pointIconStyle = odf.StyleFactory.produce({
      image : {
        icon : {
          anchor:[70, 0.7], //아이콘 위치 조정 값 [x조정값, y조정값] ※size 옵션과 함께 사용 (기본값)[0.5,0.5]
          anchorOrigin:'bottom-right', //아이콘 위치 조정 기준점※(기본값)'bottom-right'
          anchorXUnits:'pixels', //아이콘 x 위치 설정 단위  ※anchor 값의 첫번째 요소의 단위('pixels' 또는 'fraction')
                                 //※ 'pixels' : 픽셀단위로 위치 조정.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>스타일 객체 생성</p>
<pre class="prettyprint source lang-javascript"><code>//점 스타일 생성 예제
let pointCircleStyle = odf.StyleFactory.produce({
  image : {
    circle : {
      radius:50,//크기
      fill:{
        color:'gray'//채우기 색
       },//채우기
       stroke: {//윤곽선
         color:'red',//테두리 색
         width:10,//굵기
         lineDash:[4, 8]//점선 설정
       },
       snapToPixel : false //true : sharp, false : blur
     }
   },
   text : {
     text : '텍스트 내용', //텍스트 내용
     font : 'bold 20px Courier New' //폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
   }
 });
 //생성된 스타일을 JSON형태로 변환
 pointCircleStyle.getJSON();
</code></pre>
<pre class="prettyprint source lang-javascript"><code>    //심볼 스타일 생성 예제
    let pointIconStyle = odf.StyleFactory.produce({
      image : {
        icon : {
          anchor:[70, 0.7], //아이콘 위치 조정 값 [x조정값, y조정값] ※size 옵션과 함께 사용 (기본값)[0.5,0.5]
          anchorOrigin:'bottom-right', //아이콘 위치 조정 기준점※(기본값)'bottom-right'
          anchorXUnits:'pixels', //아이콘 x 위치 설정 단위  ※anchor 값의 첫번째 요소의 단위('pixels' 또는 'fraction')
                                 //※ 'pixels' : 픽셀단위로 위치 조정. size의 절반 값이 중심 위치
                                 //※ 'fraction'(기본값) :  백분율로 위치조정. 0.5가 중심 위치
          anchorYUnits:'fraction', //아이콘 y 위치 설정 단위  ※anchor 값의 두번째 요소의 단위('pixels' 또는 'fraction'(기본값))
          color:'blue', //이미지 색상
          offset:[20,20], //offsetOrigin으로 부터 x축, y축 좌표위치 이동
          offsetOrigin:'top-left', //offset의 기준점
          opacity:0.5, //투명도
          scale:0.5, //크기를 정해진 값의 n배로 셋팅
          snapToPixel:false,  //true : sharp, false : blur
          rotateWithView:false, //지도가 회전할때 텍스트도 적절하게 회전할지 여부
          rotation:30*Math.PI/180, //시계방향으로 회전 (단위:라디안)
          size:[100, 100],  //이미지가 그려지는 도형 크기 ※ 그림을 그린 도화지의 크기
          src:'images/sample.png' //이미지 경로
        }
      }
    });
    //생성된 스타일을 JSON형태로 변환
    pointIconStyle.getJSON();
</code></pre>
<pre class="prettyprint source lang-javascript"><code>    //선 스타일 생성 예제
    let lineStyle = odf.StyleFactory.produce({
      stroke : {
        color:'red',
        lineCap : 'butt',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
        lineJoin : 'miter',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
        //lineDash : [10],//점선의 간격 크기
        width:20
      },
      text :{
        text:'텍스트 내용',//텍스트 내용
        //offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
        //offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
        //rotation : (Math.PI*270/180), //회전
        //textAlign : 'left',//텍스트 수평정렬
        //textBaseline : 'middle',//텍스트 수직정렬
        font : 'bold 20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
        fill : {color:'red'},
        stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
          color:'blue',
        },
        padding : [10,5,5,5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
        backgroundStroke : {color:'black'},//placement :'line' 일경우 미적용
        backgroundFill : {color:'white'},//placement :'line' 일경우 미적용
        placement :'line',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지
        maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
        overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
        scale : 2, //텍스트 크기를 정해진 값의 n배로 셋팅
        rotateWithView: true//지도가 회전할때 텍스트도 적절하게 회전할지 여부
      }
    });
    //생성된 스타일을 JSON형태로 변환
    lineStyle.getJSON();
</code></pre>
<pre class="prettyprint source lang-javascript"><code>   //면 스타일 생성 예제
   let polygonStyle = odf.StyleFactory.produce({
     fill : {
       color:'gray',
     },
     stroke : {
       color:'red',
       lineCap : 'butt',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
       lineJoin : 'miter',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
       //lineDash : [10],//점선의 간격 크기
       width:20
     },
     text :{
       text:'텍스트 내용',//텍스트 내용
       //offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
       //offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
       //rotation : (Math.PI*270/180), //회전
       //textAlign : 'left',//텍스트 수평정렬
       //textBaseline : 'middle',//텍스트 수직정렬
       font : 'bold 20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
       fill : {color:'red'},
       stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
         color:'blue',
       },
       padding : [10,5,5,5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
       backgroundStroke : {color:'black'},//placement :'line' 일 경우 미적용
       backgroundFill : {color:'white'},//placement :'line' 일 경우 미적용
       placement :'point',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지
       maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
       overflow : false,//placement :'line' 일 경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
       scale : 2, //텍스트 크기를 정해진 값의 n배로 셋팅
       rotateWithView: true//지도가 회전할 때 텍스트도 적절하게 회전할지 여부
     }
   });
   //생성된 스타일을 JSON형태로 변환
   polygonStyle.getJSON();
</code></pre>
<pre class="prettyprint source lang-javascript"><code>   //면(패턴채우기) 스타일 생성 예제
   let polygonStyle = odf.StyleFactory.produce({
     
     fillPattern : {
       //1. 기본제공 패턴 이용시 (pattern 또는 image  둘중 하나만 정의)
       // ★ 기본제공 패턴 종류 ★
       // 'hatch', 'cross', 'dot', 'circle', 'square', 'tile',
       // 'woven', 'crosses', 'caps', 'nylon', 'hexagon', 'cemetry',
       // 'sand', 'conglomerate', 'gravel', 'brick', 'dolomite', 'coal',
       // 'breccia', 'clay', 'flooded', 'chaos', 'grass', 'swamp', 'wave',
       // 'vine', 'forest', 'scrub', 'tree', 'pine', 'pines', 'rock', 'rocks'
       pattern : 'hatch',//기본제공 패턴 명 (odf.StyleFactory.getValidFillPatternList() 로 조회 가능)
       patternColor : 'blue', //패턴의 색상
       fill : { //패턴 배경색상
         color : 'yellow'
       },

       //2. 사용자 정의 이미지 이용시  (pattern 또는 image  둘중 하나만 정의)
       // image : {
       //   icon : {
       //     src : &quot;data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2ZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo1RjI5MTdFNjgzMTlFQjExQTI4NUU2MEIzOEZDQzE2MyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDo5MzNFMUU4MzE5ODcxMUVCQjRDOEQyNTdBRjg5QTZDOSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDo5MzNFMUU4MjE5ODcxMUVCQjRDOEQyNTdBRjg5QTZDOSIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M2IChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjY1MjkxN0U2ODMxOUVCMTFBMjg1RTYwQjM4RkNDMTYzIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjVGMjkxN0U2ODMxOUVCMTFBMjg1RTYwQjM4RkNDMTYzIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+1Tz04QAAAJtJREFUeNpi/P//PwMMTFi6mgEJHE4+sdSWgQzAO3kDCp+JgPr/JOLV2AxhYqAeeAPE2bS2JB+IX9HSks1AvAyXJDUseQ/EGfgUUMOSv0D8h9aWiADxdFpbAgJBQBxBa0tAYDIQi9HaElCwTcUmwUJAIyM1bMdryVyLaDi7IDqUbEuoGVyjloxaMmrJqCV4LAkEYjloPUExAAgwADiJIuQ99s0ZAAAAAElFTkSuQmCC&quot;
       //   }
       // },
       
       //3. 기본제공 패턴 또는 사용자 정의 이미지 이용시 정의
       offset :16,//패턴 위치 이동 (오른쪽 아래 방향)
       scale :1.5,//패턴 크기
       size :10, //패턴 도형의 크기
       spacing :18,//패턴 도형간의 간격
       angle :0,//회전각
     },
     stroke : {
       color:'red',
       lineCap : 'butt',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
       lineJoin : 'miter',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
       //lineDash : [10],//점선의 간격 크기
       width:20
     },
   });
   //생성된 스타일을 JSON형태로 변환
   polygonStyle.getJSON();
</code></pre>
<pre class="prettyprint source lang-javascript"><code>   //스타일 복합 속성 설정 배열 생성 예제
   let styleArray = odf.StyleFactory.produce([
     //스타일 1 : 다각형의 외곽선 스타일
     {
       stroke: {
         color: [132,229,252,0.95],
         lineCap: 'round', //선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
         lineJoin: 'round', //('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
         width: 5,
       },
     },
     //스타일 2 : 다각형의 무계중심점에 대한 포인트 스타일
     {
       geometry: function(feature) {
         let geometry = feature.getGeometry();
         return geometry.getInteriorPoints();
       },
       image : {
         circle : {
           fill : {
             color : '#CC70B4'
           },
           radius : 10,
           stroke:{
             color : '#CC70B4',
             width: 1
           },
         }
       }
     },
   ]);
   //생성된 스타일 배열을 JSON 형태로 변환
   styleArray.getJSON();
</code></pre>
<pre class="prettyprint source lang-javascript"><code> //파이차트 스타일 생성 예제
 var pointChartStyle = odf.StyleFactory.produce({
   image : {
     chart : {
       type : 'pie', //'pie','pie3D','donut','bar'
       data : [1,5,2,3],//고정값
       stroke : {
         color : '#ffffff', //테두리 색상
         width:2, //테두리 두께
       },
       radius : 50,//파이의 크기
       //파이차트에서 사용할 색상
       colors : 'pale' , // 'classic', 'dark', 'pale', 'pastel', 'neon'
       //아래와같이 직접 정의하여 사용할 수도 있음
       //colors :['#FF4B4B','#FF7272','#FF9999','#FFC0C0','#FFE7E7'],
       //rotation : Math.PI*90/180//기울기
     }
   },
 } );
 //생성된 스타일을 JSON형태로 변환
 pointChartStyle.getJSON();
</code></pre>
<pre class="prettyprint source lang-javascript"><code> //별모양 regularShape 스타일 생성 예제
 var starRegularShapeStyle = odf.StyleFactory.produce({
   image : {
     regularShape : {
       fill: {
           color: [0, 0, 0, 0.2]
           //채우기 색
       },//채우기
       stroke: {//윤곽선
           color: [132, 229, 252, 0.95],//테두리 색
           width: 2,//굵기
           //lineDash:[4, 1]//점선 설정
       },
       points: 5,
       radius: 10,
       radius2: 4,
       angle: 0
     },
 } );
 //생성된 스타일을 JSON형태로 변환
 starRegularShapeStyle.getJSON();
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_styleOption">odf_styleOption</a>></code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>스타일 생성을 위한 option</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Style</code>
            
            
                <p>파라미터로 넘어온 options의 값으로 스타일 객체 생성</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceChart">produceChart<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.Chart}</span>
    </h4>





<div class="method-description">
    
    <p>Chart Style 생성</p>
<pre class="prettyprint source lang-javascript"><code>      let chartStyle = odf.StyleFactory.produceChart( {
            type : 'pie', // 2차원 파이 : 'pie', 3차원 파이 : 'pie3D', 도넛형태 : 'donut'
            datas : ['X', 'Y', 'id'],
            stroke : {
              color : '#ffffff', //테두리 색상
              width:2, //테두리 두께
            },
            radius : 50,//파이의 크기
            //파이차트에서 사용할 색상
            //colors : 'pale' , // 'classic', 'dark', 'pale', 'pastel', 'neon'
            //아래와같이 직접 정의하여 사용할 수도 있음
            colors :['#FF4B4B','#FF7272','#FF9999','#FFC0C0','#FFE7E7'],
                   //rotation : Math.PI*90/180//기울기
          });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_chart">odf_styleOption_chart</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>chart Style 생성을 위한 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Chart</code>
            
            
                <p>chart Style</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceCircle">produceCircle<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.Circle}</span>
    </h4>





<div class="method-description">
    
    <p>Circle Style 생성</p>
<pre class="prettyprint source lang-javascript"><code>      let circleStyle = odf.StyleFactory.produceCircle({
         radius:50,//크기
         fill:{
           color:'gray'//채우기 색
          },//채우기
          stroke: {//윤곽선
            color:'red',//테두리 색
            width:10,//굵기
            lineDash:[4, 8]//점선 설정
          },
          snapToPixel : false //true : sharp, false : blur
      });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_circle">odf_styleOption_circle</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>Circle Style 생성을 위한 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Circle</code>
            
            
                <p>Circle Style</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceElement">produceElement<span class="signature">(options, size, labelFlag)</span><span class="return-type-signature"> &rarr; {HTMLElement}</span>
    </h4>





<div class="method-description">
    
    <p>스타일 옵션으로 범례용 element 생성</p>
<pre class="prettyprint source lang-javascript"><code>odf.StyleFactory.produceElement({
    image : {
      circle : {
                radius:25,//크기
                fill:{
                  color:[0,0,0,0.2]//채우기 색
                },//채우기
                stroke: {//윤곽선
                  color:[132,229,252,0.95],//테두리 색
                  width:10,//굵기
                  //lineDash:[4, 1]//점선 설정
                },
               // snapToPixel : true //true : sharp, false : blur
            }
    },
         text :{
            text:'욥',//텍스트 내용
            //offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
            //offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
            //rotation : (Math.PI*270/180), //회전
            //textAlign : 'left',//텍스트 수평정렬
            //textBaseline : 'middle',//텍스트 수직정렬
            font : 'bold 14px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
            fill : {color:[0,0,0,0.95]},
            stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
                color:[255,255,255,0.8],
            },
            padding : [0.5,0.5,0.5,0.5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
            backgroundStroke : {color:'black'},//placement :'line' 일경우 미적용
            backgroundFill : {color:'white'},//placement :'line' 일경우 미적용
            //maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
            //overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
            scale : 1, //텍스트 크기를 정해진 값의 n배로 셋팅
            rotateWithView: true//지도가 회전할때 텍스트도 적절하게 회전할지 여부
        }

  },20,true);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>스타일 생성 옵션(Object/JSON String)</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">size</span>
            

            
                


    <span class="param-type">
        <code>Number</code>
    </span>
    

            

            

            

            <div class="param-description"><p>element 크기(width,height등에 사용). default 10 최소 0 최대 2000</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">labelFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>라벨 스타일 적용 여부. true=&gt; 적용, false=&gt;미적용. default false</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>HTMLElement</code>
            
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceFill">produceFill<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.Fill}</span>
    </h4>





<div class="method-description">
    
    <p>fill Style 생성</p>
<pre class="prettyprint source lang-javascript"><code>      let fillStyle = odf.StyleFactory.produceFill({color:'red'});
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>odf_styleOption_fill</code>
    </span>
    

            

            

            

            <div class="param-description"><p>Fill Style 생성을 위한 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Fill</code>
            
            
                <p>Fill Style</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceFillPattern">produceFillPattern<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.FillPattern}</span>
    </h4>





<div class="method-description">
    
    <p>fillPattern Style 생성</p>
<pre class="prettyprint source lang-javascript"><code>      let fillPatternStyle = odf.StyleFactory.produceFillPattern({
        pattern : 'hash',//패턴 유형
        patternColor : '#55dd20',//패턴색상(전경색)
        fill : '#ffffff',//패턴 배경 색상 
      });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>odf_styleOption_fillPattern</code>
    </span>
    

            

            

            

            <div class="param-description"><p>FillPattern Style 생성을 위한 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.FillPattern</code>
            
            
                <p>Fill Style</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceFunction">produceFunction<span class="signature">(options, mode)</span><span class="return-type-signature"> &rarr; {function}</span>
    </h4>





<div class="method-description">
    
    <p>스타일 Function 생성</p>
<pre class="prettyprint source lang-javascript"><code>   //resolution 값을 기준으로 윤곽선 색 변경
   odf.StyleFactory.produceFunction([
     //기본스타일
     {
       seperatorFunc :&quot;default&quot;,
       style :{
         fill:{color:[0,0,0,0.5]},//채우기
         stroke: {color:'red',width:3,},//윤곽선
        },
      },
      //resolution 값이 2보다 클때 적용되는 스타일
      {
        seperatorFunc :function(feature,resolution){
          return resolution>2;
        },
        style :{
          fill:{color:[0,0,0,0.5]},//채우기
          stroke: {color:'gray',width:3,},//윤곽선
        },
        priority : 2,
      },
      //resolution 값이 4보다 클때 적용되는 스타일
      {
        seperatorFunc :function(feature,resolution){
          return resolution>4;},
          style :{
            fill:{color:[0,0,0,0.5]},//채우기
            stroke: {color:'blue',width:3,},//윤곽선
          },
          priority : 1,
        }]);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>  //피쳐별로 텍스트 값 다르게 적용
      odf.StyleFactory.produceFunction([
        {
          seperatorFunc :&quot;default&quot;,
          style :{
            fill:{color:[0,0,0,0.5]},//채우기
            stroke: {color:'red',width:3,},//윤곽선
            text : {font : 'bold 14px Courier New',fill : {color:'red'},},//텍스트
          },
          callbackFunc : function(style,feature,resolution){
            style.getText().setText(feature.getProperties().속성명);
          },
        }
      ])
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_StyleFunctionOption">odf_StyleFunctionOption</a>></code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            
                
            

            <div class="param-description"><p>스타일 Function 생성을 위한 옵션 배열</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">mode</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            
                
                    <span class="param-default">
                        normal
                    </span>
                
            

            <div class="param-description"><p>'fast'모드 스타일 사용여부</p>
<ul>
<li>'fast' : 스타일 적용 속도 빠름</li>
<li>'normal' : 스타일 적용 속도 느림(default)</li>
</ul></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>function</code>
            
            
                <p>파라미터로 넘어온 options의 값으로 스타일 함수 생성</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceIcon">produceIcon<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.Icon}</span>
    </h4>





<div class="method-description">
    
    <p>Icon Style 생성</p>
<pre class="prettyprint source lang-javascript"><code>      let iconStyle = odf.StyleFactory.produceIcon({
          anchor:[70, 0.7], //아이콘 위치 조정 값
          anchorOrigin:'bottom-right', //아이콘 위치 조정 기준점
          anchorXUnits:'pixels', //아이콘 위치 조정 단위 설정 x축
          anchorYUnits:'fraction', //아이콘 위치 조정 단위 설정 y축
          color:'blue', //이미지 색상
          offset:[20,20], //offsetOrigin으로 부터 x축, y축 좌표위치 이동
          offsetOrigin:'top-left', //offset의 기준점
          opacity:0.5, //투명도
          scale:0.5, //크기를 정해진 값의 n배로 셋팅
          snapToPixel:false,  //true : sharp, false : blur
          rotateWithView:false, //지도가 회전할때 텍스트도 적절하게 회전할지 여부
          rotation:30*Math.PI/180, //시계방향으로 회전
          size:[100, 100],  //이미지가 그려지는 도형 크기 ※ 그림을 그린 도화지의 크기
          src:'images/sample.png' //이미지 경로
        });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_icon">odf_styleOption_icon</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>Icon Style 생성을 위한 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Icon</code>
            
            
                <p>Icon Style</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceRegularShape">produceRegularShape<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.RegularShape}</span>
    </h4>





<div class="method-description">
    
    <p>RegularShape Style 생성</p>
<pre class="prettyprint source lang-javascript"><code>  let regularShapeStyle = odf.StyleFactory.produceRegularShape( {
      image : {
        regularShape : {
          fill: {
              color: [0, 0, 0, 0.2]
              //채우기 색
          },//채우기
          stroke: {//윤곽선
              color: [132, 229, 252, 0.95],//테두리 색
              width: 2,//굵기
              //lineDash:[4, 1]//점선 설정
          },
          points: 5,
          radius: 10,
          radius2: 4,
          angle: 0
        },
  });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_regularShape">odf_styleOption_regularShape</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>chart Style 생성을 위한 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.RegularShape</code>
            
            
                <p>regularShape Style</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceSLD">produceSLD<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {<a href="SLD.html">SLD</a>}</span>
    </h4>



    <h4 class="method-heading">Summary</h4>
    <p>스타일 객체 생성</p>
<pre class="prettyprint source lang-javascript"><code>//점 스타일 생성
let sld = odf.StyleFactory.produceSLD({
   rules: [
     {
       name: 'My Rule', //룰 이름
       //해당 룰 표현 범위
       scaleDenominator: {
         //min: 100001,
         max: 100001,
         },
         //해당 룰 적용 대상 한정
         //  ★ 기본 비교
         // - filter[0] : 비교연산자 ('==' , '!=' ,  '>' , '&lt;' , '>=', '&lt;=')
         // - filter[1] : 칼럼명
         // - filter[2] : 기준 값
         // ★ like 비교
         // - filter[0] : '*='
         // - filter[1] : 칼럼명
         // - filter[2] : 비교 문자열 (wildCard=&quot;*&quot; singleChar=&quot;.&quot; escape=&quot;!&quot;)
         //          (ex 1) *_2  => [somthing] + '_2'
         //          (ex 2) *_.</code></pre>



<div class="method-description">
    <h4 class="method-heading">Description</h4>
    <p>스타일 객체 생성</p>
<pre class="prettyprint source lang-javascript"><code>//점 스타일 생성
let sld = odf.StyleFactory.produceSLD({
   rules: [
     {
       name: 'My Rule', //룰 이름
       //해당 룰 표현 범위
       scaleDenominator: {
         //min: 100001,
         max: 100001,
         },
         //해당 룰 적용 대상 한정
         //  ★ 기본 비교
         // - filter[0] : 비교연산자 ('==' , '!=' ,  '>' , '&lt;' , '>=', '&lt;=')
         // - filter[1] : 칼럼명
         // - filter[2] : 기준 값
         // ★ like 비교
         // - filter[0] : '*='
         // - filter[1] : 칼럼명
         // - filter[2] : 비교 문자열 (wildCard=&quot;*&quot; singleChar=&quot;.&quot; escape=&quot;!&quot;)
         //          (ex 1) *_2  => [somthing] + '_2'
         //          (ex 2) *_.   => [somthing] + '_' +[어떤 문자이든 한개의 문자]
         //    ★ null 비교
         //   - filter[0] : 비교연산자 ('==' , '!=')
         //   - filter[1] : 칼럼명
         //   - filter[2] : null
         //    ★ 두개 이상의 조건
         //   - filter[0] : 논리연산자('&&','||')
         //   - filter[1] : 조건1
         //   - filter[2] : 조건2
         //   (ex) filter:['&&',['>=','id','3'],['!=',id,null]]
         filter: ['>', 'id', '20'], //[기본 비교][ , 칼럼명, 기준값]
         //filter: ['*=', 'type', '*_2'], //[like비교] wildCard=&quot;*&quot; singleChar=&quot;.&quot; escape=&quot;!&quot;
         //filter: ['!=', 'id', null], //[isnull비교]
         //filter:['&&',['>=','id','3'],['!=',id,null], ...]//[두개 이상의 조건]
       symbolizers: [
         {
           kind: 'Mark',
           // 포인트에 표현될 도형 종류
           // - 'circle' : 원
           // - 'square' : 사각형
           // - 'triangle' : 세모
           // - 'star' : 별 모양
           // - 'cross' :  + 모양
           // - 'x' : x 모양
           wellKnownName: 'Square',

           //포인트에 표현될 도형의 반지름
           radius: 6,
           //포인트에 표현될 도형의 채우기색
           color: '#FF0000',
           //포인트에 표현될 도형의 채우기색 투명도 0~1
           fillOpacity: 0.5,
           //포인트에 표현될 도형의 윤곽선 색
           strokeColor: '#0000FF',
           //포인트에 표현될 도형의 윤곽선 투명도 0~1
           strokeOpacity: 0.7,
           //포인트에 표현될 도형의 윤곽선 두께
           strokeWidth: 3,
           //offset 적용[x이동량,y이동량]
           offset: [0, 20], //pixel 단위로 이동
           //offset 적용 시킬 geometry 타입 칼럼명, 기본값=> 'the_geom'
           offsetGeometry: 'the_geom',
         },
       ],
       },
     ]
});
pointLayer.setSLD(sld);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//심볼 스타일 생성
let sld = odf.StyleFactory.produceSLD({
       rules: [
         {
           //룰 이름
           name: 'My Rule',
           symbolizers: [
             {
               kind: 'Icon',
               //심볼 이미지 경로 ★geoserver가 접근할 수 있는 경로★
               image: '이미지경로',
               //투명도
               opacity: '0.8',
               //회전각(도)
               rotate: 0,
               //크기
               size: 30,
               //offset 적용[x이동량,y이동량]
               offset: [0, -2000], //pixel 단위로 이동
               //offset 적용 시킬 geometry 타입 칼럼명, 기본값=> 'the_geom'
               offsetGeometry: 'the_geom',
             },
           ],
         },
       ],
});
pointLayer.setSLD(sld);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//선 스타일 생성
let sld = odf.StyleFactory.produceSLD({
       rules: [
         {
           //룰 이름
           name: 'My Rule',
           symbolizers: [
             {
               kind: 'Line',
               //라인 색상
               color: '#338866',
               // 라인의 끝 표현 방식
               //   - 'butt' : (Default) sharp square edge 끝부분을 수직으로 절단
               //   - 'round' : rounded edge 끝부분이 둥근 모양
               //   - 'square' :  slightly elongated square edge 끝부분에 사각형 추가
               cap: 'round',
               // 라인이 꺽이는 부분 표현 방식
               // - 'miter' : (Default) sharp corner 코너가 뾰족    /＼
               // - 'round' : rounded corner 코너가 동글동글
               // - 'bevel' :  diagonal corner 코너의 끝이 잘림 /￣＼
               join: 'round',
               //투명도 0~1
               opacity: 0.7,
               //두께
               width: 3,
               //대시 간격 조절
               dasharray: [16, 10],
               //선의 시작점에서 얼마나 떨어진 곳에서부터 점선을 표시할지
               dashOffset: 16,
             },
           ],
         },
       ],
});
lineLayer.setSLD(sld);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//면 스타일 생성
let sld = odf.StyleFactory.produceSLD({
       rules: [
         { //룰 이름
           name: 'My Rule',
           symbolizers: [
             {
                 kind: 'Fill',
                 //채우기색
                 color: '#AAAAAA',
                 //채우기 투명도 0~1
                 fillOpacity: 0.5,
                 //윤곽선색
                 outlineColor: '#338866',
                 //윤곽선 두께
                 outlineWidth: 3,
                 //윤곽선 투명도 0~1
                 outlineOpacity: 1,
                 //윤곽선 대쉬 간격
                 outlineDasharray: [16, 10],
             },
           ],
         },
       ],
});
polygonLayer.setSLD(sld);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//면(패턴채우기) 스타일 생성
let sld = odf.StyleFactory.produceSLD({
       rules: [
         { //룰 이름
           name: 'My Rule',
           symbolizers: [
             {
                 kind: 'Fill',
                 //패턴 채우기 이미지
                graphicFill:{
                  //이미지
                   image: 'http://**************:13002/geoserver/web/wicket/resource/org.geoserver.web.AboutGeoServerPage/img/icons/silk/help-ver-C3812C74BC524179F4CCF5D2DB7B3CBF.png',
                   kind: &quot;Icon&quot;,
                   //패턴 크기
                   size: 20,
                   //회전 각도
                   rotate : 90,
                 },
                 //채우기 투명도 0~1
                 fillOpacity: 0.5,
                 //윤곽선색
                 outlineColor: '#338866',
                 //윤곽선 두께
                 outlineWidth: 3,
                 //윤곽선 투명도 0~1
                 outlineOpacity: 1,
                 //윤곽선 대쉬 간격
                 outlineDasharray: [16, 10],
             },
           ],
         },
       ],
});
polygonLayer.setSLD(sld);
</code></pre>
<pre class="prettyprint source lang-javascript"><code>//라벨 스타일 생성
let sld = odf.StyleFactory.produceSLD({
       rules: [
         {
           //룰 이름
           name: 'My Rule',
           symbolizers: [
             {
                 kind: 'Text',
                 //사용할 폰트
                 font: ['Times'],
                 // 라벨 모양
                 // 	-'normal' : 기본
                 // 	-'italic' : 이탤릭체 - italic체로 디자인된 폰트를 사용
                 // 	-'oblique' : 기본 글씨체를 비스듬하게 보여줌
                 fontStyle: 'normal',
                 // 라벨 두께
                 // -'normal' : 기본
                 // -'bold' : 굵게
                 fontWeight: 'normal',
                 //라벨 텍스트
                 //	{{칼럼명}} => 해당 칼럼 값
                 label: '{{id}} {{저녁인}}',
                 //라벨 크기
                 size: 15,
                 //후광 색상
                 haloColor: '#ffffff',
                 //후광 두께
                 haloWidth: 5,

                 //★★라벨표현방식이 'LinePlacement'일경우 적용 속성 start★★
                   // 레이블이 선의 곡선을 따르도록 할지 여부
                   //   - true : 레이블이 선의 곡선을 따르도록
                   //   - false : 레이블이 선의 곡선을 따르지 않게
                   //followLine: true,
                   //레이블 반복 간격 조절
                   //   - 0 : 라벨 반복 x
                   //	 - 양수 값 :  라인에 따라 라벨을 표시하는 빈도 조정. 값이 클수록 띄엄띄엄 나타남
                   //repeat: 20,
                   //선을 따라 레이어의 변위를 제어. repeat 속성과 함께 사용할 경우, repeat속성보다 작은 값을 설정
                   // maxDisplacement: 15,
                 //★★라벨표현방식이 'LinePlacement'일경우 적용 속성 end★★

              //라벨 표현 방식
               LabelPlacement: [
                 {
                   //라벨표현방식- 기준점에 표현
                    PointPlacement: [
                      {
                        //기준점을 기준으로 라벨이 배치되는 위치 결정
                        AnchorPoint: [{
                           // 라벨이 x 기준점의 어느부분에 표시되는지(0~1)
                           //    - 0(default) :left
                           //    - 0.5 :center
                           //    - 1 : right
                          AnchorPointX: ['0.5']
                           //  라벨이 y 기준점의 어느부분에 표시되는지(0~1)
                           //   - 0(default) :bottom
                           //   - 0.5 :center
                           //   - 1 : top
                           , AnchorPointY: ['0.5']
                        }],
                        //라벨 기준점 좌표 이동(단위:pixel)
                        Displacement: [{
                          //라벨 x 기준점 좌표 이동량 (+ : 왼쪽,- : 오른쪽)
                          DisplacementX: ['0.5']
                          //라벨 y 기준점 좌표 이동량 (+ : 위,- : 아래)
                           , DisplacementY: ['0.5']
                        }],
                        //라벨 회전 각도
                        Rotation: ['0'],
                      },
                   ],
                  // // 라벨표현방식- 선을 따라서 표현
                  // LinePlacement: [
                  //  {
                  //     //라벨이 라인의 위에 위치할지(+), 아래에 위치할지(-)
                  //     PerpendicularOffset: ['10'],
                  //   },
                  //],
                 },
               ],
             },
           ],
         },
       ],
});
pointLayer.setSLD(sld);
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_sld_option">odf_sld_option</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>스타일 생성을 위한 option</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>SLD</code>
            
            
                <p>파라미터로 넘어온 options의 값으로 스타일 객체 생성</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceStroke">produceStroke<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.Stroke}</span>
    </h4>





<div class="method-description">
    
    <p>Stroke Style 생성</p>
<pre class="prettyprint source lang-javascript"><code>      let strokeStyle = odf.StyleFactory.produceStroke({
        color:'red',
        lineCap : 'butt',//선의 끝부분 모양('butt'(네모지게-선이 원래 길이보다 조금 일찍 끝남) / 'round' (둥글게) / 'square'(네모지게))
        lineJoin : 'miter',//('bevel' (꺾이는 부분을 지붕모양으로 )/ 'round' (둥글게)/ 'miter'(뾰족하게))
        //lineDash : [10],//점선의 간격 크기
        width:20
      });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_stroke">odf_styleOption_stroke</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>Stroke Style 생성을 위한 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Stroke</code>
            
            
                <p>Stroke Style</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".produceText">produceText<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {odf.Text}</span>
    </h4>





<div class="method-description">
    
    <p>Text Style 생성</p>
<pre class="prettyprint source lang-javascript"><code>      let textStyle = odf.StyleFactory.produceText({
        text:'텍스트 내용',//텍스트 내용
        //offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
        //offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
        //rotation : (Math.PI*270/180), //회전
        //textAlign : 'left',//텍스트 수평정렬
        //textBaseline : 'middle',//텍스트 수직정렬
        font : 'bold 20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
        fill : {color:'red'},
        stroke : {//text 안의 stroke는 width/lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
          color:'blue',
        },
        padding : [10,5,5,5],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
        backgroundStroke : {color:'black'},//placement :'line' 일경우 미적용
        backgroundFill : {color:'white'},//placement :'line' 일경우 미적용
        placement :'line',//텍스트를 나열하는 위치를 line을 따라 나타나게 할지, 특정 point에 나타나게 할지
        maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
        overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
        scale : 2, //텍스트 크기를 정해진 값의 n배로 셋팅
        rotateWithView: true//지도가 회전할때 텍스트도 적절하게 회전할지 여부
      });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>odf_styleOption_text</code>
    </span>
    

            

            

            

            <div class="param-description"><p>Text Style 생성을 위한 옵션</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf.Text</code>
            
            
                <p>Text Style</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    <span class="method-type-signature is-static">static</span>
    </div>

    <h4 class="method-name" id=".validationCheck">validationCheck<span class="signature">(options)</span><span class="return-type-signature"> &rarr; {Boolean}</span>
    </h4>





<div class="method-description">
    
    <p>스타일 생성 Option의 유효성검사</p>
<pre class="prettyprint source lang-javascript"><code>   //스타일 유효성 검사
   odf.StyleFactory.validationCheck({
     image : {
       circle : {
         radius:50,//크기
         fill:{
           color:'gray'//채우기 색
          },//채우기
          stroke: {//윤곽선
            color:'red',//테두리 색
            width:10,//굵기
            lineDash:[4, 8]//점선 설정
          },
        }
      },
      text : {
        text : '텍스트 내용', //텍스트 내용
        font : 'bold 20px Courier New' //폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
      }
    });
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>스타일 생성을 위한 option</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Boolean</code>
            
            
                <p>파라미터로 넘어온 options의 값이 스타일을 생성하기에 유효하다면 true 값 반환</p>
            
        </li>
    
    </ul>


















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:52 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>