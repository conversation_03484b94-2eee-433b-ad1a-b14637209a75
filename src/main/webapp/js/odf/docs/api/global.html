<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: Global</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind"></p>
    <h1 class="page-title">Global</h1>
    




<section>


<header class="not-class">


    
        
        <!-- <h2></h2> -->

        

        

        
    
</header>

<article>
    <div class="container-overview">



    
        

        


<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>


        
    
    </div>

    

    

     

    

    


    

    

    
        <h3 class="subsection-title">Type Definitions</h3>

        
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="apiOption">apiOption</h4>




<div class="description">
    <p>api 레이어 생성을 위한 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">server</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(필수값)API 주소</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">server</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 호출 서버 옵션, API 서버 주소</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">url</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(필수값) API 주소</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">proxyURL</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>프록시 url</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">proxyParam</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>프록시 매개변수 명</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">service</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(필수값) 적용 서비스 'wms' 또는 'wfs'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">bbox</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wfs 레이어의 경우 이용됨. 기본값 false</p>
<ul>
<li>true : 사용자가 보고있는 지도 영역에 걸치는 도형만 조회</li>
<li>false : 최초 1회 모든 도형을 조회</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">tiled</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wms 레이어 타일링 옵션. 기본값 false</p>
<ul>
<li>true : 사용자가 보고있는 지도 영역을 이미지 여러 장으로 조회</li>
<li>false : 사용자가 보고있는 지도 영역을 이미지 1장으로 조회</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">tileGrid</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wmts tileGrid(service값이 wmts일 경우 필수값)</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">origin</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wmts origin</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">resolutions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wmts resolutions</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">matrixIds</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wmts matrixIds</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">originalOption</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>odf 기본 적용 파라미터</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">REQUEST</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms/wfs)odf 기본 적용 REQUEST 파라미터 사용 여부 (기본값 true)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">SERVICE</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms/wfs)odf 기본 적용 SERVICE 파라미터 사용 여부 (기본값 true)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">BBOX</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms/wfs)odf 기본 적용 BBOX 파라미터 사용 여부 (기본값 true)
odf에서 기본 제공하는 bbox 배열은 minx,miny,maxx,maxy 순, 하지만 api에 따라 x와 y가 반전되어 bbox 배열이 miny,minx, maxy,maxx 순인 경우가 있다.
해당 경우에는 BBOX 값을 '{{miny}},{{minx}},{{maxy}},{{maxx}}' 와같이 입력하면 x와 y의 순서가 바뀌어 적용됨.
bbox는 기본적으로 지도 좌표계의 값을 따라가는데, 지도 좌표계와 api 레이어의 좌표계가 상이한 경우, 좌표변환을 위해서 LAYER_PROJECTION 값 또한 정의해야함</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">LAYER_PROJECTION</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지도 좌표계와 api 레이어의 좌표계가 상이할때 API의 레이어 좌표계 정의, 지도좌표계와 API 레이어의 좌표계가 같은 경우 정의 안해도 됨</p>
<ul>
<li>(ex) 'EPSG:5186'  (입력포멧 'EPSG:[숫자]')</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">TILEROW_TILECOL_INVERTED_STATE</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>WMTS 레이어일 경우, tileRow 값 tileCol 값 반전 여부 (기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">WIDTH</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms)odf 기본 적용 WIDTH 파라미터 사용 여부 (기본값 true)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">HEIGHT</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms)odf 기본 적용 HEIGHT 파라미터 사용 여부 (기본값 true)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">VERSION</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms)odf 기본 적용 VERSION 파라미터 사용 여부 (기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">TRANSPARENT</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms)odf 기본 적용 TRANSPARENT 파라미터 사용 여부 (기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">STYLES</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms)odf 기본 적용 STYLES 파라미터 사용 여부 (기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">CRS</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(wms)odf 기본 적용 CRS 파라미터 사용 여부 (기본값 false)</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">className</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 element의 클래스 명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">webGLRender</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>webGL을 이용하여 렌더링(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">renderOptions</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>렌더링 옵션(webGLRender가 true일때 사용됨)</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfFlatStyle">odfFlatStyle</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleRule">odfStyleRule</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfWebGLVectorTileStyle">odfWebGLVectorTileStyle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>flat style 형식</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>귀속 정의</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">parameterFilter</span>
        

        
            


    <span class="param-type">
        <code>function</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>서버에 요청시 사용하는 파라미터 편집(추가/삭제/수정)을 위한 필터(getMap, getFeature, GetTile, GetCapabilities, GetStyles, DescribeFeatureType)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="csvOption">csvOption</h4>




<div class="description">
    <p>CSV 레이어 생성을 위한 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">data</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>CSV 형식의 텍스트 데이터</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">dataProjectionCode</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 좌표계 코드 ex)'EPSG:5179'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">featureProjectionCode</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>변환활 좌표계 코드 ex)'EPSG:5179'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">geometryColumnName</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>csv 파일의 geometry column 이름  ex)'the_geom'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">delimiter</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>csv 파일의 컴럼을 구분할 구분자 (기본값 콤마(,))</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">className</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 element의 클래스 명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">webGLRender</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>webGL을 이용하여 렌더링(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">renderOptions</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>렌더링 옵션(webGLRender가 true일때 사용됨)</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfFlatStyle">odfFlatStyle</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleRule">odfStyleRule</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfWebGLVectorTileStyle">odfWebGLVectorTileStyle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>flat style 형식</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>귀속 정의</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="event_excludeEvent_item">event_excludeEvent_item</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">eventId</span>
        

        
            


    <span class="param-type">
        <code>string</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>대상 이벤트의 id</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">state</span>
        

        
            


    <span class="param-type">
        <code>event_state</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이벤트 상태</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="event_expiration">event_expiration</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">true</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>한번만 호출하고 해제</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">false</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지도객체가 없어지거나 disconncet 할 때 해제</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">customize</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>사용자가 원하는 시점에서 1번 호출하고 해제</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">odfDeleted</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(기본값) 지도객체가 없어지면 해제</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="event_type">event_type</h4>




<div class="description">
    <p>이벤트 target에 따라 적용 가능한 event 종류</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'change'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 일반적인 변경 이벤트. revision counter가 증가 하면 발생(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:layerGroup'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] layerGroup 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:size'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] size 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:target'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] target 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:view'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] view 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'click'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] drag 없는 클릭. 더블 클릭을 하면 두 번 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'dblclick'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] drag 없는 더블 클릭</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'moveend'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 지도가 이동 된 후 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'movestart'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 지도가 이동을 시작할 때 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'pointerdrag'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 마우스 drag 할 때 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'pointermove'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 마우스 포인터가 이동할 때 마다 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'precompose'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] layer를 그리기 전에 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'postcompose'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] layer를 모두 그린 후 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'postrender'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 지도 프레임이 렌더링 된 후에 발생
(precompose → postcompose → postrender 순서)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'propertychange'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 속성이 변경 되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'singleclick'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] drag 와 더블 클릭이 없는 단일 클릭. 더블클릭과 구분하기 위해</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'contextmenu'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 우클릭시 트리거</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'modifyend'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 레이어 수정 완료 시 (피쳐 이동 종료) 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'editend'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 레이어 편집 종료 시(종료 버튼 클릭) 발생 (현재 우클릭으로 편집 가능한 레이어는 geoImageLayer뿐)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'editstart'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[map] 레이어 편집 시작 시(이동/회전/크기조절 버튼 클릭) 발생 (현재 우클릭으로 편집 가능한 레이어는 geoImageLayer뿐)</p>
<p>250ms 지연발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[view] 일반적인 변경 이벤트. revision counter가 증가 하면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:center'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[view] center 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:resolution'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[view] resolution 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:rotation'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[view] rotation 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'propertychange'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[view] 속성이 변경 되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] 일반적인 변경 이벤트. revision counter가 증가 하면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:extent'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] extent 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:maxResolution'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] maxResolution 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:minResolution'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] minResolution 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:opacity'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] opacity 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:visible'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] visible 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:zIndex'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] zIndex 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'propertychange'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] 속성이 변경되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'precompose'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[WebGL 레이어]  레이어가 구성되기 전에 트리거</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'prerender'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] layer 렌더링 전 트리거</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'postcompose'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[WebGL 레이어]  레이어가 구성된 후에 트리거</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'postrender'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] layer 렌더링 후 트리거</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'rendercomplete'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[layer 공통] 렌더링 완료시 트리거
(precompose → prerender → postcompose → postrender → rendercomplete 순서)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:blur'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Heatmap 레이어] blur 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:gradient'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Heatmap 레이어] gradient 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:radius'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Heatmap 레이어] radius 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:source'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Heatmap|Image|Tile|Vector|VectorTile 레이어] source 속성
변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:preload'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Tile|VectorTile 레이어] preload 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:useInterimTilesOnError</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Tile|VectorTile  레이어]
useInterimTilesOnError 속성 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[source 공통] 일반적인 변경 이벤트. revision counter가 증가 하면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'propertychange'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[source 공통] 속성이 변경되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'imageloadend'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[ImageArcGISRest|ImageMapGuide|ImageWMS 소스] 이미지로드가 완료되면
발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'imageloaderror'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[ImageArcGISRest|ImageMapGuide|ImageWMS 소스] 이미지로드로 인해 오류가
발생하면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'imageloadstart'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[ImageArcGISRest|ImageMapGuide|ImageWMS 소스] 이미지로드가 시작될 때
발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'afteroperations'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Raster 소스] 작업이 실행 된 후에 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'beforeoperations'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Raster 소스] 작업이 실행 되기 전에 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'tileloadstart'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[UrlTile|TileImage|BingMaps|TileArcGISRest|TileJSON|TileWMS|WMTS|XYZ|VectorTile 소스] 타일 load가
시잘할때 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'tileloadend'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[UrlTile|TileImage|BingMaps|TileArcGISRest|TileJSON|TileWMS|WMTS|XYZ|VectorTile 소스] 데이터로드시
또는 타일이 더 이상 필요 없기 때문에로드가 중단 된 경우 타일로드가 완료되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'tileloaderror'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[UrlTile|TileImage|BingMaps|TileArcGISRest|TileJSON|TileWMS|WMTS|XYZ|VectorTile 소스] 타일로드로 인해
오류가 발생하면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'addfeature'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Vector|Cluster 소스] Feature가 source에 추가 되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'featureloadend'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Vector|Cluster 소스] Feature가 source에 모두 로드되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'changefeature'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Vector|Cluster 소스] Feature가 업데이트 되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'clear'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Vector|Cluster 소스] source에서 clear 메소드가 호츨 되면 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'removefeature'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Vector|Cluster 소스] feature가 제거되면 발생. source.clear 제외</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Marker]  일반 변경 이벤트.position,draggable,map 값 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:position'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Marker]  position 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:draggable'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Marker]  draggable 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'change:map'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Marker]  map 변경시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'click'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Marker]  marker 클릭시 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'markerdragstart'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Marker]  draggable 마커 drag를 시작했을때 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'markerdrag'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Marker]  draggable 마커를 중 마우스 이동 발생했을때 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'markerdragend'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[Marker]  draggable 마커 drag가 종료됬을때 발생</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'drawstart'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[DrawControl]  DrawControl을 이용하여 그리기를 시작할때 발생
(buffer 도형 그리기에서는 트리거 되지 않음)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'drawend'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[DrawControl] DrawControl을 이용하여 그리기를 종료할때 발생</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="forEachFeatureAtPixel_callback">forEachFeatureAtPixel_callback<span class="signature">(feature, layer)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">feature</span>
            

            
                


    <span class="param-type">
        <code>ODF.Feature</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회된 도형</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">layer</span>
            

            
                


    <span class="param-type">
        <code>ODF.Layer</code>
    </span>
    

            

            

            

            <div class="param-description"><p>조회된 도형이 속한 레이어(layerFlag가 true일때 반환됨)</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="geojsonOption">geojsonOption</h4>




<div class="description">
    <p>geojson 레이어 생성을 위한 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">data</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>geojson 형식의 object 데이터</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">dataProjectionCode</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 좌표계 코드 ex)'EPSG:5179'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">featureProjectionCode</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>변환활 좌표계 코드 ex)'EPSG:5179'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">className</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 element의 클래스 명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">webGLRender</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>webGL을 이용하여 렌더링(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">renderOptions</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>렌더링 옵션(webGLRender가 true일때 사용됨)</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfFlatStyle">odfFlatStyle</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleRule">odfStyleRule</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfWebGLVectorTileStyle">odfWebGLVectorTileStyle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>flat style 형식</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>귀속 정의</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="geoserverOption">geoserverOption</h4>




<div class="description">
    <p>레이어 생성을 위한 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">server</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>레이어 호출 서버 주소</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">server</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>레이어 호출 서버 옵션</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">url</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 호출 서버 주소</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">version</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 호출 서버 버전</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">proxyURL</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>프록시 url</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">proxyParam</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>프록시 매개변수 명</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">layer</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>레이어 저장소명과 레이어 명칭 (ex) nsid_dev:L100000254</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">service</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#service">service</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>레이어 호출 종류</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">limit</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>레이어 호출 제한 수</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">crtfckey</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        

        <div class="param-description">
            <p>api 사용시 인증 키</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">bbox</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        

        <div class="param-description">
            <p>bbox 사용 여부(wfs 레이어일 때만 의미 있음)</p>
<ul>
<li>true :  feature 정보를 사용자가 보는 영역만큼 매번 요청하여 가져옴</li>
<li>false : feature정보를 한번에 다 가져옴 (기본값)</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">method</span>
        

        
            


    <span class="param-type">
        <code>'get'</code>
    </span>
    |

    <span class="param-type">
        <code>'post'</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        

        <div class="param-description">
            <p>정보를 조회할때 get 요청을 할지, post 요청을할지</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">tiled</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        

        <div class="param-description">
            <p>(wms 레이어일 때만 의미 있음) 이미지를 타일링하여 요청할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">className</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>레이어 element의 클래스 명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">webGLRender</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>webGL을 이용하여 렌더링(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">renderOptions</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>렌더링 옵션(webGLRender가 true일때 사용됨)</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfFlatStyle">odfFlatStyle</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleRule">odfStyleRule</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfWebGLVectorTileStyle">odfWebGLVectorTileStyle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>flat style 형식</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>귀속 정의</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">gmlStorePrefix</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>gml 생성시 저장소명에 붙일 프리픽스 문자열 정의</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">parameterFilter</span>
        

        
            


    <span class="param-type">
        <code>function</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>서버에 요청시 사용하는 파라미터 편집(추가/삭제/수정)을 위한 필터(getMap, getFeature, GetTile, GetCapabilities, GetStyles, DescribeFeatureType)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="geotiffOption">geotiffOption</h4>




<div class="description">
    <p>geotiff 레이어 생성 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">tileGrid</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>타일링 정보</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">extent</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>타일 ​​그리드의 범위. 이 범위를 벗어나는 타일은 소스에서 요청되지 않습니다.</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">resolutions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>제공 레졸루션 목록 정의</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">tileSizes</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Array.&lt;Number>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>타일 ​​크기</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">normalize</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기본적으로 소스 데이터는 래스터 통계 또는 각 소스의 min 또는 max 속성을 기반으로 한 배율 인수를 사용하여 0과 1 사이의 값으로 정규화됩니다.(default true)
대신 스타일 표현식의 원시 값으로 작업하려면, 이 값을 false로 설정하세요.
이 옵션을 false로 설정하면 소스의 모든 속성 min과 max속성이 무시됩니다.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">wrapX</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>타일 ​그리드 범위를 넘어 타일을 렌더링할지 여부. (defualt false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">sources</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#geotiffSourceOption">geotiffSourceOption</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>GeoTIFF 소스에 대한 정보 목록, 배율을 적용한 후 해상도 세트가 동일하다면 여러 소스를 결합할 수 있음</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">opaque</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어가 불투명한지여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">transition</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>불투명도. 불투명도 전환을 비활성화하려면 0 전달</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">renderOptions</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>렌더링 옵션</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfFlatStyle">odfFlatStyle</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleRule">odfStyleRule</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfWebGLVectorTileStyle">odfWebGLVectorTileStyle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>flat style 형식</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>귀속 정의</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="geotiffSourceOption">geotiffSourceOption</h4>




<div class="description">
    <p>geotiff 레이어 생성 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">url</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>geoTiff 파일 경로.
tileGrid가 정의되어있다면 '~/~//{{z}}/{{y}}/{{x}}.tif' 이와 같이 사용 가능</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">overviews</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 GeoTIFF 파일의 저해상도 버전 파일. url이 정의됬을때 사용됨</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">proxyURL</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>프록시 url</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">proxyParam</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>프록시 매개변수 명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">blob</span>
        

        
            


    <span class="param-type">
        <code>Blob</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>goeTiff를 blob 타입으로 받아 생성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">bands</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>읽을 밴드 번호. 제공되지 않으면 모든 밴드를 읽습니다.
예를 들어 GeoTIFF에 파란색(1), 녹색(2), 빨간색(3) 및 근적외선(4) 대역이 있고 근적외선 대역만 필요한 경우 'band : [4]'로 설정</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">min</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>최소 소스 데이터 값. 렌더링된 값은 구성된 최소 및 최대 값을 기준으로 0에서 1까지 조정 (기본값 0)
이 값을 설정하지 않으면 래스터 통계를 사용합니다. 이 동작을 비활성화하려면 normalize 옵션을 false로 설정하세요</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">max</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>최대 소스 데이터 값. 렌더링된 값은 구성된 최소 및 최대 값을 기준으로 0에서 1까지 조정.
이 값을 설정하지 않으면 래스터 통계를 사용합니다. 이 동작을 비활성화하려면 normalize 옵션을 false로 설정하세요</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">nodata</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>삭제할 값. geoTiff의 메타데이터의 nodata 값을 덮어씌움</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="imageOption">imageOption</h4>




<div class="description">
    <p>이미지 레이어 생성을 위한 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">url</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이미지 경로</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">projection</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 좌표계 정보 (ex)'EPSG:5186'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">imageCenter</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 중심점</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">extent</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이미지가 뿌려질 extent(extent 정의시 imageCenter값, scale값 무시)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">editMenu</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이미지 위치/회전 편집 기능 사용 정의</p>
<ul>
<li>['translate'] : 편집 기능 사용</li>
<li>[] | undefined : 편집기능 사용 안함</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">imageRotate</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이미지 회전 각도(도)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">className</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 element의 클래스 명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>귀속 정의</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="kmlOption">kmlOption</h4>




<div class="description">
    <p>KML 레이어 생성을 위한 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">data</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>KML 형식의 텍스트 데이터</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">dataProjectionCode</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 좌표계 코드 ex)'EPSG:5179'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">featureProjectionCode</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>변환활 좌표계 코드 ex)'EPSG:5179'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">className</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 element의 클래스 명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">webGLRender</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>webGL을 이용하여 렌더링(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">renderOptions</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>렌더링 옵션(webGLRender가 true일때 사용됨)</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfFlatStyle">odfFlatStyle</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleRule">odfStyleRule</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfWebGLVectorTileStyle">odfWebGLVectorTileStyle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>flat style 형식</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>귀속 정의</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="mapInfo">mapInfo</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">center</span>
        

        
            


    <span class="param-type">
        <code>odf.Coordinate</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>현재 지도 중심 좌표</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">zoom</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>현재 줌레벨</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">maxZoom</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>최대 줌레벨</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">minZoom</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>최소 줌레벨</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">resolution</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>해상도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotation</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>회전</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">projection</span>
        

        
            


    <span class="param-type">
        <code>string</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>좌표계</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="markerOption">markerOption</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">position</span>
        

        
            


    <span class="param-type">
        <code><a href="Coordinate.html">Coordinate</a></code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>마커의 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">draggable</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>드래그 가능 여부(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">positioning</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_marker_positioning">odf_marker_positioning</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>마커의 상대적 위치 (기본값 'bottom-center')</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offset</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;number></code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>기준점으로 부터 정의한 값만큼 마커를 이동(픽셀) (기본값 [0,0])</p>
<ul>
<li>첫번째 요소 : 수평 오프셋 값. 양수는 마커를 오른쪽으로 이동시킴</li>
<li>두번째 요소 : 수직 오프셋 값. 양수는 마커를 아래로 이동시킴</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stopEvent</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>마커 이벤트 전파 중지 여부. true =&gt; 이벤트 전파 중지, false=&gt; 이벤트 전파 (기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>마커의 스타일 정의</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">width</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>마커의 너비 정의</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">height</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>마커의 높이 정의</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">src</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>마커의 배경 이미지 url 정의</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">element</span>
        

        
            


    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>사용자 정의 마커 element</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">autoPan</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
                &lt;nullable&gt;<br>
            
            </span>
        

        

        <div class="param-description">
            <p>지도 화면에서 팝업이 잘려나오게 될 경우, 지도를 이동할지 여부</p>
<ul>
<li>true : 지도 화면에서 팝업이 잘려나오게 될 경우, 지도를 이동합니다. (기본값 : false)</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">autoPanAnimation</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
                &lt;nullable&gt;<br>
            
            </span>
        

        

        <div class="param-description">
            <p>autoPan이 ture일때, 지도를 이동하는데 사용되는 애니메이션 입니다. (기본값 : 250) 최소 :0 최대 : 100000</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">autoPanMargin</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
                &lt;nullable&gt;<br>
            
            </span>
        

        

        <div class="param-description">
            <p>autoPan이 ture일때, 팝업과 지도 사이의 여백(픽셀) 지정 (기본값 : 20)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="modifiedCallback">modifiedCallback<span class="signature">(callbackObject, original, modify)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">callbackObject</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>수정 도형 정보</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">original</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>수정 전 도형 정보</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">modify</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>수정 후 도형 정보</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="modifyObject">modifyObject</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">update</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 피쳐, 수정된 피쳐 배열</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">original</span>
        

        
            


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 피쳐</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">modify</span>
        

        
            


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>수정된 피쳐</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">insert</span>
        

        
            


    <span class="param-type">
        <code>Array</code>
    </span>
    |

    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>추가된 피쳐 배열</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">delete</span>
        

        
            


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>삭제할 피쳐</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">moveFeature</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 피쳐, 수정된 피쳐 배열</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">original</span>
        

        
            


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 피쳐</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">modify</span>
        

        
            


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>수정된 피쳐</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">transform</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 피쳐, 수정된 피쳐 배열</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">original</span>
        

        
            


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 피쳐</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">modify</span>
        

        
            


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>수정된 피쳐</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_attribute">odf_attribute</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">name</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>속성명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">type</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>데이터 타입  (ex) string, int, geometry</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_attribute_dataType">odf_attribute_dataType</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'geometry'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지오메트리(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'string'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>문자열</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'int'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>숫자</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_attribute_range">odf_attribute_range</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">values</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>속성값을 키로, 해당 속성 값을 갖는 요소의 갯수를 count 속성 값으로 갖는 Object
(ex) {
'aaa' : {count : 1},
'bbb' : {count : 5},
'ccc' : {count : 3},
}</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">min</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>해당 속성 값들의 최소값</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">max</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>해당 속성 값들의 최대값</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_basemap_baroEMapKey">odf_basemap_baroEMapKey</h4>




<div class="description">
    <p>baseMap 생성 키</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'eMapBasic'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 기본 지도(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'eMapColor'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 색각 지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'eMapLowV'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 큰글씨 지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'eMapWhite'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 백지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'eMapEnglish'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 영어 지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'eMapChinese'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 중어 지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'eMapJapanese'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 일어 지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'eMapWhiteEdu'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 교육용 백지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'eMapAIR'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵  영상지도</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_basemap_kakaoMapKey">odf_basemap_kakaoMapKey</h4>




<div class="description">
    <p>kakao 생성 키</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'kakaoBase'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>kakao 기본 지도(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'kakaoSkyview'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>kakao 영상 지도</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_basemap_option">odf_basemap_option</h4>




<div class="description">
    <p>사용자가 어떤 그룹의 어떤 베이스맵을 사용할지 정의</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">baroEMap</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_basemap_baroEMapKey">odf_basemap_baroEMapKey</a>></code>
    </span>
    |

    <span class="param-type">
        <code>boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 그룹 내 어떤 배경 지도를 사용할지 정의
(ex) ['eMapBasic','eMapColor','eMapLowV','eMapWhite','eMapEnglish','eMapChinese','eMapKorean','eMapJapanese','eMapWhiteEdu','eMapAIR']  :  바로e맵 그룹의 기본지도와 영상 지도 사용
(ex) true  :  바로e맵 그룹의 모든 배경지도 사용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">vWorld</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_basemap_vWorldMapKey">odf_basemap_vWorldMapKey</a>></code>
    </span>
    |

    <span class="param-type">
        <code>boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 그룹 내 어떤 배경 지도를 사용할지 정의
(ex) ['vWorldBase', 'vWorldWhite' ,'vWorldMidnight','vWorldHybrid','vWorldSatellite']  :  vWorld 그룹의 기본지도와 회색 지도 사용
(ex) true  :  vWorld 그룹의 모든 배경지도 사용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">kakao</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_basemap_kakaoMapKey">odf_basemap_kakaoMapKey</a>></code>
    </span>
    |

    <span class="param-type">
        <code>boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 그룹 내 어떤 배경 지도를 사용할지 정의
(ex) ['kakaoBase','kakaoSkyview' ] :  카카오 그룹의 기본지도와 영상 지도 사용
(ex) true  :  카카오 그룹의 모든 배경지도 사용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">OSM</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_basemap_OSMMapKey">odf_basemap_OSMMapKey</a>></code>
    </span>
    |

    <span class="param-type">
        <code>boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 그룹 내 어떤 배경 지도를 사용할지 정의
(ex) ['OSM']  :  OSM 지도 사용
(ex) true  :  OSM 그룹의 모든 배경지도 사용</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_basemap_OSMMapKey">odf_basemap_OSMMapKey</h4>




<div class="description">
    <p>kakao 생성 키</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'OSM'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>OSM 기본 지도(table)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_basemap_vWorldMapKey">odf_basemap_vWorldMapKey</h4>




<div class="description">
    <p>vWorld 생성 키</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'vWorldBase'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>vWorld 기본 지도(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'vWorldWhite'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>vWorld 회색 지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'vWorldMidnight'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>vWorld 야간 지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'vWorldHybrid'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>vWorld 하이브리드 지도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'vWorldSatellite'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>vWorld 영상 지도</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_color_type">odf_color_type</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'red'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>빨간색 계열(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'blue'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>파란색 계열</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'yellow'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>노란색 계열</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'green'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>초록색 계열</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'purple'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>보라색 계열</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'brown'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>갈색 계열</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'black'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>검정색 계열</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'red2blue'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>빨간색→파란색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'red2green'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>빨간색→초록색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'red2purple'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>빨간색→보라색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'red2yellow'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>빨간색→노란색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'yellow2blue'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>노란색→파란색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'yellow2purple'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>노란색→보라색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'yellow2green'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>노란색→초록색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'yellow2brown'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>노란색→갈색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'yellow2black'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>노란색→검정색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'blue2green'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>파란색→초록색,</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'random'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>랜덤 색상</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_coordinate">odf_coordinate</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">odf_coordinate[x</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>x 좌표</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">odf_coordinate[y</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>y 좌표</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_default_pattern_define">odf_default_pattern_define</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">name</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">image</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴 이미지(base64)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_divideMap_info">odf_divideMap_info</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">map</span>
        

        
            


    <span class="param-type">
        <code><a href="Map.html">Map</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>분할지도 지도 객체</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">position</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>분할지도 위치 (1~4)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">mainMapFlag</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>메인 지도 여부 ※true이면 메인지도, false이면 분할지도 객체</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_divideMapOption">odf_divideMapOption</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">position</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>분할지도가 위치할 번호(지정하지 않을 경우 기본값 지정)
2분할 지도(수직분할)의 경우 1은 좌측, 2는 우측
2분할 지도(수평분할)의 경우 1은 상단, 2는 하단
4분할 지도의 경우 1은 좌상단, 2는 우상단, 3은 좌하단, 4는 우하단</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">mapOption</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지도 생성에 사용할 옵션</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">center</span>
        

        
            


    <span class="param-type">
        <code>Odf.Coordinate</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지도 중심점 좌표</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">zoom</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>현재 확대 레벨</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">maxZoom</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>최대 확대레벨</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">minZoom</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>최소 확대레벨</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">projection</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>좌표계 SRS ID</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">proxyURL</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>프록시 URL</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">proxyParam</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>프록시 파라미터</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">apiGateWayKey</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>api GateWay Key</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">basemap</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_basemap_option">odf_basemap_option</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>사용할 베이스맵 선택 옵션</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">baroEMapURL</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 경로</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">baroEMapKey</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>바로e맵 API KEY</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">controlOption</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>분할지도 내에서 사용할 컨트롤 지정</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">basemap</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>basemap 컨트롤을 사용할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">clear</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>clear 컨트롤을 사용할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">download</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>download 컨트롤을 사용할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">print</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>print 컨트롤을 사용할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">overviewmap</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>overviewmap 컨트롤을 사용할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">draw</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>draw 컨트롤을 사용할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">measure</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>measure 컨트롤을 사용할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">move</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>move 컨트롤을 사용할지 여부</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_divideMaps">odf_divideMaps</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">dualMap</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_divideMap_info">odf_divideMap_info</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>2분할지도 객체</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">quadMap</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_divideMap_info">odf_divideMap_info</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>4분할지도 객체</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_esrijson_format_object">odf_esrijson_format_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">format</span>
        

        
            


    <span class="param-type">
        <code>'esrijson'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>kml 형식</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">geometryName</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지오메트리 칼럼명</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_extent">odf_extent</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Array</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">odf_extent[0];</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>좌상단 x좌표</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">odf_extent[1];</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>좌상단 y좌표</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">odf_extent[2];</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>우하단 x좌표</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">odf_extent[3];</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>우하단 y좌표</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_feature_geojson_object">odf_feature_geojson_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">geometry</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지오메트리 정보</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_feature_option">odf_feature_option</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">geometryType</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_geometryType">odf_geometryType</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>지오메트리 타입</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">coordinates</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_coordinate">odf_coordinate</a></code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_coordinate">odf_coordinate</a>></code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Array.&lt;<a href="global.html#odf_coordinate">odf_coordinate</a>>></code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Array.&lt;Array.&lt;<a href="global.html#odf_coordinate">odf_coordinate</a>>>></code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>지오메트리의 좌표정보</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circleSize</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
                &lt;nullable&gt;<br>
            
            </span>
        

        

        <div class="param-description">
            <p>(선택)geometry type이 'circle'일 경우 원의 크기</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">properties</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
                &lt;nullable&gt;<br>
            
            </span>
        

        

        <div class="param-description">
            <p>(선택)feature에서 관리할 정보</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="StyleFunction.html">StyleFunction</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="Style.html">Style</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            

            
            </span>
        

        

        <div class="param-description">
            <p>(선택)Tile 레이어 생성</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_geojson_format_object">odf_geojson_format_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">format</span>
        

        
            


    <span class="param-type">
        <code>'geojson'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>geojson 형식</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">originSRS</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원본 좌표계 (ex)'EPSG:3857'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">outputSRS</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>변환 좌표계 (ex)'EPSG:5186'</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_geometryType">odf_geometryType</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'point'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>point 타입 (table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'multipoint'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>multipoint 타입</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'linestring'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>linestring 타입</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'multilinestring'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>multilinestring 타입</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'polygon'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>polygon 타입</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'multipolygon'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>multipolygon 타입</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'circle'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>circle 타입</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_gml3_format_object">odf_gml3_format_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">format</span>
        

        
            


    <span class="param-type">
        <code>'gml3'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>gml3 형식</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">featureNS</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>feature 네임스페이스.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">featureType</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;string></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>gml3 형식</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">srsName</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>ggeometry를 만들때 사용할 좌표계</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">surface</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>gml:Polygon 요소 대신 gml:Surface를 쓸지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">curve</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>gml:LineString 요소 대신 gml:Curve 쓸지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">multiCurve</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>gml:MultiLineString 대신 gml:MultiCurve를 쓸지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">multiSurface</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>gml:MultiPolygon 대신 gml:multiSurface를 쓸지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">schemaLocation</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>GML을 작성할 때 사용할 선택적 schemaLocation</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">hasZ</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>좌표에 Z 값이 있는지 여부</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_graiden_color_info">odf_graiden_color_info</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">color</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_rgba_color">odf_rgba_color</a></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odf_hex_color">odf_hex_color</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>색상 값</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">propotion</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>적용할 색상의 비율</p>
<ul>
<li>0~1 사이의 값</li>
<li>첫번째 인덱스는 0으로 고정, 마지막 인덱스는 1로 고정</li>
<li>propotion 값은 이전 propotion 값보다 커야함</li>
</ul>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_hex_color">odf_hex_color</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'#[숫자][숫자][숫자][숫자][숫자][숫자]';</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>hex 색상(숫자 6자리)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'#[숫자][숫자][숫자][숫자][숫자][숫자][숫자][숫자]';</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>hex 색상(숫자 6자리 +투명도 포함)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_kml_format_object">odf_kml_format_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">format</span>
        

        
            


    <span class="param-type">
        <code>'kml'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>kml 형식</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">extractStyles</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>스타일 추출 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">showPointNames</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지점이 포함된 플레이스마크의 라벨로 이름 표시 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">defaultStyle</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기본 스타일</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">writeStyles</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>KML 생성시 스타일 정보 생성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">crossOrigin</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>로드된 이미지의 속성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">iconUrlFunction</span>
        

        
            


    <span class="param-type">
        <code>function</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>url 문자열을 받아서 url 문자열을 반환하는 함수.아이콘 경로를 변경하거나 KMZ 배열 버퍼에서 얻은 데이터 url을 대체하는 데 사용</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_layer_geoJson_object">odf_layer_geoJson_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">type</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>geoJson 타입. 'FeatureCollection'</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_layer_key">odf_layer_key</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'image'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>Image 레이어 생성 (table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'vector'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>Vector 레이어 생성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'heatmap'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>Heatmap 레이어 생성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'imagetile'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>Tile 레이어 생성</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_layer_option">odf_layer_option</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">source</span>
        

        
            


    <span class="param-type">
        <code>odf.Source</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 소스</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">mkType</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_layer_source_type">odf_layer_source_type</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 소스 제공
(ex)'geoserver' 또는 'geojson' 또는 'empty'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">id</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이어 식별 id</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_layer_source_type">odf_layer_source_type</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'geoserver'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>geoserver 제공 소스로 레이어 생성(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'geojson'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>geojson을 소스로 vector레이어 생성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'empty'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>빈 vector레이어 생성</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_marker_positioning">odf_marker_positioning</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">'top-left'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 상단 좌측에 위치(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'top-center'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 상단 중앙에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'top-right'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 상단 우측에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'center-left'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 중앙 좌측에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'center-center'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 중앙 중앙에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'center-right'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 중앙 우측에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'bottom-left'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 하단 우측에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'bottom-center'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 하단 중앙에 위치(기본값)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">'bottom-right'</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>position 값 기준 하단 좌측에 위치</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_mvt_format_object">odf_mvt_format_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">format</span>
        

        
            


    <span class="param-type">
        <code>'mvt'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>kml 형식</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_rgba_color">odf_rgba_color</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Array</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">odf_rgba_color[0];</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>빨간색 값(0~255의 값)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">odf_rgba_color[1];</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>초록색 값(0~255의 값)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">odf_rgba_color[2];</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>파란색 값(0~255의 값)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">odf_rgba_color[3];</span>
        

        
            


    <span class="param-type">
        <code>number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>투명도(0~1의 값)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_scaleInfo">odf_scaleInfo</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">level</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_scaleInfo_object">odf_scaleInfo_object</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>zoomLevel별 resolution과 sldScale 정보</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_scaleInfo_object">odf_scaleInfo_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">zoomLevel</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>geoserver 제공 소스로 레이어 생성(table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">resolution</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>geojson을 소스로 vector레이어 생성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">sldScale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>빈 vector레이어 생성</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_fill_symbolizer">odf_sld_fill_symbolizer</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">kind</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>'Fill' 다각형 스타일 (table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>채우기 색상 (ex)'#338866'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">fillOpacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>채우기색 투명도 0~1</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">outlineColor</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>윤곽선 색상 (ex)'#338866'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">outlineWidth</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>윤곽선 두께</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">outlineOpacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>윤곽선 투명도 0~1</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">outlineDasharray</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>윤곽선 대시 간격 조절. 선의 길이 대비 공백의 길이 표현 (ex) [16,10]</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_icon_symbolizer">odf_sld_icon_symbolizer</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">kind</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>'Icon' 심볼 스타일 (table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">image</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>심볼 이미지 경로 ※ geoserver가 접근할 수 있는 경로</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">opacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>심볼 이미지 투명도 0~1</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotate</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>심볼 이미지 회전각(도)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">size</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>심볼 이미지 크기</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offset</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기준 좌표의 x/y축 이동량 (단위 : pixel), [x이동량,y이동량]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offsetGeometry</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기준되는 geometry 타입의 컬럼명 ※기본값 : 'the_geom'</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_line_label">odf_sld_line_label</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">LinePlacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_sld_line_label_inner">odf_sld_line_label_inner</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨표현방식- 선을 따라서 표현  (table)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_line_label_inner">odf_sld_line_label_inner</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">PerpendicularOffset</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라인 기준 상단에 위치할지 하단에 위치할지  (table)</p>
<ul>
<li>양수 : 라벨이 라인의 위에 위치</li>
<li>음수 : 라벨이 라인의 아래에 위치 (ex) [10]</li>
</ul>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_line_symbolizer">odf_sld_line_symbolizer</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">kind</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>'Line' 라인 스타일 (table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라인 색상 (ex)'#338866'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">cap</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라인의 끝 표현 방식</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">butt</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(Default) sharp square edge 끝부분을 수직으로 절단</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">round</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>rounded edge 끝부분이 둥근 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">square</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>slightly elongated square edge 끝부분에 사각형 추가</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">join</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라인이 꺽이는 부분 표현 방식</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">miter</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(Default) sharp corner 코너가 뾰족    /＼</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">round</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>rounded corner 코너가 동글동글</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">size.bevel</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>diagonal corner 코너의 끝이 잘림 /￣＼</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">opacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라인의 투명도 0~1</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라인의 두께</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">dasharray</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>대시 간격 조절. 선의 길이 대비 공백의 길이 표현 (ex) [16,10]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">dashOffset</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>선의 시작점에서 얼마나 떨어진 곳에서부터 점선을 표시할지. dasharray의 첫번째 요소보다 같거나 작은값</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_mark_symbolizer">odf_sld_mark_symbolizer</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">kind</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>'Mark' 점 스타일 (table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">wellKnownName</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>포인트에 표현될 도형 종류</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">circle</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">square</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>네모</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">triangle</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>세모</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">star</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>별 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">cross</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>'+' 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">x</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>'x' 모양</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">radius</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>포인트에 표현될 도형의 반지름</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>포인트에 표현될 도형의 채우기색 (ex)'#FF0000'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">fillOpacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>포인트에 표현될 도형의 채우기색 투명도 0~1</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">strokeColor</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>포인트에 표현될 도형의 윤곽선 색</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">strokeOpacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>포인트에 표현될 도형의 윤곽선 투명도 0~1</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">strokeWidth</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>포인트에 표현될 도형의 윤곽선 두께</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offset</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기준 좌표의 x/y축 이동량 (단위 : pixel), [x이동량,y이동량]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offsetGeometry</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기준되는 geometry 타입의 컬럼명 ※기본값 : 'the_geom'</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_option">odf_sld_option</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">rules</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_sld_rule">odf_sld_rule</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>적용 rule 배열  (table)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_point_label">odf_sld_point_label</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">PointPlacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_sld_point_label_inner">odf_sld_point_label_inner</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨표현방식- 기준점에 표현  (table)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_point_label_inner">odf_sld_point_label_inner</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">AnchorPoint</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_sld_point_label_inner_anchorpoint">odf_sld_point_label_inner_anchorpoint</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기준점을 기준으로 라벨이 배치되는 위치 결정  (table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">Displacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_sld_point_label_inner_displacement">odf_sld_point_label_inner_displacement</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 기준점 좌표 이동(단위:pixel)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">Rotation</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 회전 각도</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_point_label_inner_anchorpoint">odf_sld_point_label_inner_anchorpoint</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">AnchorPointX</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨이 x 기준점의 어느부분에 표시되는지(0~1) AnchorPointX : [0.5]  (table)</p>
<ul>
<li>0(default) :left</li>
<li>0.5 :center</li>
<li>1 : right</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">AnchorPointY</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨이 y 기준점의 어느부분에 표시되는지(0~1) (ex)[0.5]</p>
<ul>
<li>0(default) :bottom</li>
<li>0.5 :center</li>
<li>1 : top</li>
</ul>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_point_label_inner_displacement">odf_sld_point_label_inner_displacement</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">DisplacementX</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 x 기준점 좌표 이동량  (table)</p>
<ul>
<li>양수 : 왼쪽 방향 이동</li>
<li>음수 : 오른쪽 방향 이동</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">DisplacementY</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 y 기준점 좌표 이동량</p>
<ul>
<li>양수: 위쪽 방향 이동</li>
<li>음수 : 아래쪽 방향 이동</li>
</ul>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_rule">odf_sld_rule</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">scaleDenominator</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>스타일 적용 시킬 최대/최소 축척 제한 (table)</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">min</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>적용 가능 최소 축척</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">max</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>적용 가능 최대 축척</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">filter</span>
        

        
            


    <span class="param-type">
        <code>Array</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>스타일 적용 대상 도형 제한</p>
<p>★ 기본 비교</p>
<ul>
<li>filter[0] : 비교연산자 ('==' , '!=' ,  '&gt;' , '&lt;' , '&gt;=', '&lt;=')</li>
<li>filter[1] : 칼럼명</li>
<li>filter[2] : 기준 값</li>
</ul>
<p>★ like 비교</p>
<ul>
<li>filter[0] : '*='</li>
<li>filter[1] : 칼럼명</li>
<li>filter[2] : 비교 문자열 (wildCard=&quot;*&quot; singleChar=&quot;.&quot; escape=&quot;!&quot;)
(ex 1) *<em>2  =&gt; [somthing] + '<em>2'
(ex 2) *</em>.   =&gt; [somthing] + '</em>' +[어떤 문자이든 한개의 문자]</li>
</ul>
<p>★ null 비교</p>
<ul>
<li>filter[0] : 비교연산자 ('==' , '!=')</li>
<li>filter[1] : 칼럼명</li>
<li>filter[2] : null</li>
</ul>
<p>★ 두개 이상의 조건</p>
<ul>
<li>filter[0] : 논리연산자('&amp;&amp;','||')</li>
<li>filter[1] : 조건1</li>
<li>filter[2] : 조건2
(ex) filter:['&amp;&amp;',['&gt;=','id','3'],['!=',id,null]]</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">symbolizers</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;odf_sld_symbolizer></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>적용 스타일 설정</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">symbolizers[0];</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_sld_mark_symbolizer">odf_sld_mark_symbolizer</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>점 스타일 옵션</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">symbolizers[1];</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_sld_icon_symbolizer">odf_sld_icon_symbolizer</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>심볼 스타일 옵션</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">symbolizers[2];</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_sld_line_symbolizer">odf_sld_line_symbolizer</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>선 스타일 옵션</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">symbolizers[3];</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_sld_fill_symbolizer">odf_sld_fill_symbolizer</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>면 스타일 옵션</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">symbolizers[4];</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_sld_text_symbolizer">odf_sld_text_symbolizer</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 스타일 옵션</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_sld_text_symbolizer">odf_sld_text_symbolizer</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">kind</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>'Text' 라벨 스타일 (table)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">font</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 폰트 (ex) ['Times']</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">fontStyle</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 모양</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">normal</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기본</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">italic</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이탤릭체(italic체로 디자인된 폰트)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">oblique</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기본 글씨체를 비스듬하게 기울여 적용</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">fontWeight</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 두께</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">normal</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기본</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">bold</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>굵게</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">label</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>표현할 text  (ex) '{{id}} {{name}}',</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">size</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 크기</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">haloColor</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>후광 색상 (ex) '#ffffff'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">haloWidth</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>후광 두께</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">overflow</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>오버플로우 여부(라벨이 도형 영역을 넘어서는 경우에도 표현 할지 여부)(POLYGON 타입 적용)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">followLine</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이블이 선의 곡선을 따르도록 할지 여부
※LabelPlacement 값이 LinePlacement일 경우, 적용</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">true</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이블이 선의 곡선을 따르도록 설정</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">false</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이블이 선의 곡선을 따르지 않도록 설정</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">repeat</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>레이블 반복 간격 조절</p>
<ul>
<li>0 : 라벨 반복 x</li>
<li>양수 값 :  라인에 따라 라벨을 표시하는 빈도 조정. 값이 클수록 띄엄띄엄 나타남</li>
</ul>
<p>※LabelPlacement 값이 LinePlacement일 경우, 적용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">maxDisplacement</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>선을 따라 레이어의 변위를 제어. repeat 속성과 함께 사용할 경우, repeat속성보다 작은 값을 설정
※LabelPlacement 값이 LinePlacement일 경우, 적용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">LabelPlacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;(<a href="global.html#odf_sld_point_label">odf_sld_point_label</a>|<a href="global.html#odf_sld_line_label">odf_sld_line_label</a>)></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라벨 표현 방식</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_StyleFunctionOption">odf_StyleFunctionOption</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">seperatorFunc</span>
        

        
            


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>필터링 조건을 검사하는 function(true나 false를 반환) 또는 'default'.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption">odf_styleOption</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>seperatorFunc의 조건이 참일 경우 적용할 스타일</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">priority</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>여러 seperatorFunc에서 참일경우, 스타일 적용 우선순위</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">callbackFunc</span>
        

        
            


    <span class="param-type">
        <code>function</code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>스타일 펑션 내에서 seperatorFunc의 결과가 true일 경우 정의된 스타일을 반환하기 전, 호출되어 스타일을 변경할 수 있는 콜백함수
매개변수로 style, feature, resolution을 전달받아 해당 function 내에서 feature, resolution 값을 이용하여 style을 변경할 수 있음</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_styleOption">odf_styleOption</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">geometryType</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_geometryType">odf_styleOption_geometryType</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>스타일을 적용할 지오메트리의 타입(아래 값들중 하나)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">image</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이미지 스타일  속성</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">icon</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_icon">odf_styleOption_icon</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>아이콘 스타일 속성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_circle">odf_styleOption_circle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원 스타일 속성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">chart</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_chart">odf_styleOption_chart</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>차트 스타일 속성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">regularShape</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_regularShape">odf_styleOption_regularShape</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>regularShape 스타일 속성</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text</span>
        

        
            


    <span class="param-type">
        <code>odf_styleOption_text</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 스타일 속성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_stroke">odf_styleOption_stroke</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>윤곽선 스타일 속성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">fill</span>
        

        
            


    <span class="param-type">
        <code>odf_styleOption_fill</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>채우기 스타일 속성</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_styleOption_chart">odf_styleOption_chart</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">type</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>차트 타입</p>
<ul>
<li>pie =&gt; 파이 차트</li>
<li>pie3D =&gt; 3D 파이 차트</li>
<li>donut =&gt; 도넛 모양 차트</li>
<li>bar =&gt; 막대 차트</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">radius</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(pie/pie3D/donut) 차트의 크기</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">barWidth</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(bar) 막대 너비.(단위 px) (기본값 10)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">barMaxHegiht</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(bar) 막대 최대 높이.(단위 px)  (기본값 50)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">barMinHegiht</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(bar) 막대 최소 높이. (단위 px) (기본값 1)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">barBufferSize</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(bar) 막대와 막대 사이의 여백 너비. (단위 px) (기본값 0)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">datas</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(동적데이터) feature의 속성 명을 정의 (추후 styleFunction의 콜백에서 활용)
※ (ex) data : [&quot;COL1&quot;,&quot;COL2&quot;,&quot;COL3&quot;,&quot;COL4&quot;]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">data</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>(정적 데이터) 미리 정의해 놓은 값을 데이터로 이용
※ (ex) data : [1,5,3,2]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">colors</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>차트에 적용할 색상</p>
<ol>
<li>미리 정의되어있는 색상 값 이용</li>
</ol>
<ul>
<li>classic =&gt; [&quot;#ffa500&quot;,&quot;blue&quot;,&quot;red&quot;,&quot;green&quot;,&quot;cyan&quot;,&quot;magenta&quot;,&quot;yellow&quot;,&quot;#0f0&quot;]</li>
<li>dark =&gt; [&quot;#960&quot;,&quot;#003&quot;,&quot;#900&quot;,&quot;#060&quot;,&quot;#099&quot;,&quot;#909&quot;,&quot;#990&quot;,&quot;#090&quot;]</li>
<li>pale =&gt; [&quot;#fd0&quot;,&quot;#369&quot;,&quot;#f64&quot;,&quot;#3b7&quot;,&quot;#880&quot;,&quot;#b5d&quot;,&quot;#666&quot;]</li>
<li>pastel =&gt; [&quot;#fb4&quot;,&quot;#79c&quot;,&quot;#f66&quot;,&quot;#7d7&quot;,&quot;#acc&quot;,&quot;#fdd&quot;,&quot;#ff9&quot;,&quot;#b9b&quot;]</li>
<li>neon =&gt; [&quot;#ff0&quot;,&quot;#0ff&quot;,&quot;#0f0&quot;,&quot;#f0f&quot;,&quot;#f00&quot;,&quot;#00f&quot;]
※ (ex) colors : &quot;dark&quot;</li>
</ul>
<ol start="2">
<li>미리 정의되어있는 스타일 사용 (헥사 색상의 배열)
※ (ex) colors : ['#FF4B4B','#FF7272','#FF9999','#FFC0C0','#FFE7E7']</li>
</ol>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offsetX</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기준점으로부터 텍스트 x좌표 위치 이동</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offsetY</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기준점으로부터 텍스트 Y좌표 위치 이동</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>회전값 (단위:라디안)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">snapToPixel</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>true(sharp) 또는 false(blur)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_styleOption_circle">odf_styleOption_circle</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">fill</span>
        

        
            


    <span class="param-type">
        <code>odf_styleOption_fill</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원 채우기 스타일</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_stroke">odf_styleOption_stroke</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원 윤곽선 스타일</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">radius</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원의 반지름</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">snapToPixel</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>true(sharp) 또는 false(blur)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">displacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>위치이동. 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0])</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원의 크기(radius로 정해진 크기의 n배), 배열일 때, [가로  축척, 세로 축척] 형태임</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원의 회전각도 (scale이 배열형태로 되어 타원형태가 되었을때 의미있음)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotateWithView</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>뷰와 함께 모양을 회전할지 여부(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">declutterMode</span>
        

        
            


    <span class="param-type">
        <code>"declutter"</code>
    </span>
    |

    <span class="param-type">
        <code>"obstacle"</code>
    </span>
    |

    <span class="param-type">
        <code>"none"</code>
    </span>
    |

    <span class="param-type">
        <code>undefined</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>디클러터 모드.</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_StyleOption_fill">odf_StyleOption_fill</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_StyleOption_fillPattern">odf_StyleOption_fillPattern</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">image</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴 이미지</p>
                <h4 class="method-heading">Properties</h4>

<ul class="method-params">


    <li>
        
            <span class="param-name">icon</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴 이미지 아이콘</p>
        </div>

    </li>
    
</ul>

            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">opacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴 투명도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">pattern</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_StyleOption_fillPattern_pattern">odf_StyleOption_fillPattern_pattern</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴명</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">patternColor</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">fill</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_StyleOption_fill">odf_StyleOption_fill</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴 배경 색상</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offset</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴 위치 이동 (오른쪽 아래 방향), hash/dot/circle/cross pattern 패턴에서 사용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">size</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>도형의 크기, hash/dot/circle/cross pattern 패턴에서 사용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">spacing</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>도형간의 간격, hash/dot/circle/cross pattern 패턴에서 사용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">angle</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    |

    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>회전각</p>
<ul>
<li>true :45도</li>
<li>false : 0도</li>
<li>[Number] : 입력한 각도</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>패턴 크기</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_StyleOption_fillPattern_pattern">odf_StyleOption_fillPattern_pattern</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">hatch</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>해치(스트라이프)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">cross</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>십자형(체크)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">dot</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>점(채워진 원)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>원(빈 원)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">square</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>정사각형(빈 사각형)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">tile</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>타일(채워진 사각형)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">woven</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>직물(─│─│ 모양)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">crosses</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>십자가(x 모양)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">caps</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>모자(^ 모양)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">nylon</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>나일론(┌ ┘ 모양)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">hexagon</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>육각형</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">cemetry</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>십자가 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">sand</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>모래(불규칙한 사각형 점)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">conglomerate</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>역암(블규칙 도형)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">gravel</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>자갈(블규칙 도형)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">brick</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>벽돌</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">dolomite</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>백운석(비스듬한 벽돌 모양)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">coal</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>석탄(채워진 삼각형)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">breccia</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>각력암(빈 삼각형)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">clay</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>점토</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">flooded</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>침수(─_─_)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">chaos</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>혼돈(기하학무늬)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">grass</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>잔디</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">swamp</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>늪</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">wave</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>물결(^^^^)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">vine</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>덩굴(│)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">forest</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>숲(작은원,큰원)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">scrub</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>스크럽(v,o)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">tree</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>나무</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">pine</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>소나무1</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">pines</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>소나무2</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rock</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>돌1</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rocks</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>돌2</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_styleOption_geometryType">odf_styleOption_geometryType</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">pointcircle</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>점(원) 형태의 지오메트리 스타일(image/text 속성 적용가능)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">pointicon</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>심볼 형태의 지오메트리 스타일(image/text 속성 적용가능)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">polygon</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>면 형태의 지오메트리 스타일(fill/stroke/text 속성 적용가능)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">linestring</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>선 형태의 지오메트리 스타일</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">free</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>[default]자유양식 지오메트리 스타일</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_styleOption_icon">odf_styleOption_icon</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">anchor</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>아이콘 위치 조정 (ex) [70, 0.7]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">anchorOrigin</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>기준점으로부터의 아이콘 위치. (default)'top-left' 또는 'top-right' 또는 'bottom-left' 또는 'bottom-right' 값 중 하나</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">anchorXUnits</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>아이콘 x축 위치 조정 단위.(default)'fraction' 또는 'pixels'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">anchorYUnits</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>아이콘 y축 위치 조정 단위.(default)'fraction' 또는 'pixels'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이미지 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">crossOrigin</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>CORS 관련 셋팅. (default)'anonymous'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offset</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>x축 y축 좌표위치 이동</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offsetOrigin</span>
        

        
            


    <span class="param-type">
        <code>'bottom-left'</code>
    </span>
    |

    <span class="param-type">
        <code>'bottom-right'</code>
    </span>
    |

    <span class="param-type">
        <code>'top-left'</code>
    </span>
    |

    <span class="param-type">
        <code>'top-right'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>오프셋 원점</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">opacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>투명도</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>크기를 정해진 값의 n배로 셋팅</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">snapToPixel</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>true(sharp) 또는 false(blur)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotateWithView</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지도가 회전할 때 이미지도 적절하게 회전할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>회전 (ex) 30*Math.PI/180</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">size</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>자연수 배열 (단위 : 픽셀)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">imgSize</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>자연수 배열 (단위 : 픽셀)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">src</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>이미지 경로</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">img</span>
        

        
            


    <span class="param-type">
        <code>HTMLImageElement</code>
    </span>
    |

    <span class="param-type">
        <code>HTMLCanvasElement</code>
    </span>
    |

    <span class="param-type">
        <code>ImageBitmap</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>아이콘의 이미지 객체</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">displacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>위치이동. 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0])</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>아이콘의 너비(픽셀). <code>scale</code>과 함께 이용할 수 없음</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">height</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>아이콘의 높이(픽셀). <code>scale</code>과 함께 이용할 수 없음</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">declutterMode</span>
        

        
            


    <span class="param-type">
        <code>"declutter"</code>
    </span>
    |

    <span class="param-type">
        <code>"obstacle"</code>
    </span>
    |

    <span class="param-type">
        <code>"none"</code>
    </span>
    |

    <span class="param-type">
        <code>undefined</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>디클러터 모드.</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_styleOption_regularShape">odf_styleOption_regularShape</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">fill</span>
        

        
            


    <span class="param-type">
        <code>odf_styleOption_fill</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>채우기 스타일</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_styleOption_stroke">odf_styleOption_stroke</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>윤곽선 스타일</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">points</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>별과 정다각형의 점 수. 다각형의 경우 점의 개수는 변의 개수</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">radius</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>정다각형의 반경</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">radius1</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>별의 외부 반경</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">radius2</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>별의 내부 반경</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">angle</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>모양의 각도(라디안). 값이 0이면 모양의 점 중 하나가 위를 향하게 됩니다. 기본값은 0</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">snapToPixel</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>true를 사용하면 &quot;선명한&quot; 렌더링(흐림 없음)이 가능하고, false를 사용하면 &quot;정확한&quot; 렌더링이 가능합니다. 모양의 위치에 애니메이션이 적용되는 경우 정확성이 중요합니다. 그렇지 않으면 모양이 눈에 띄게 흔들릴 수 있습니다. (기본값 true)</p>
<ul>
<li>true : 출력 캔버스에 모양을 그릴 때 정수개의 픽셀이 X 및 Y 픽셀 좌표로 사용되는 경우 .</li>
<li>false 분수를 사용할 수 있는 경우 .</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>라디안 단위의 회전입니다(시계 방향으로 양의 회전)(기본값 0)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotateWithView</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>뷰와 함께 모양을 회전할지 여부(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">displacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>모양의 변위(픽셀). 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0])</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>규모. (기본값 1) 2차원 스케일링이 필요하지 않은 경우 반경, 반경1 및 반경2에 대한 적절한 설정을 사용하면 더 나은 결과를 얻을 수 있음</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">declutterMode</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>디클러터 모드. &quot;declutter&quot;,&quot;obstacle&quot; ,&quot;none&quot;</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_styleOption_stroke">odf_styleOption_stroke</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">lineCap</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>선의 끝 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">lineJoin</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>선이 꺾이는 부분의 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">lineDash</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>점선</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">lineDashOffset</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>윤곽선 Line dash offset.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">miterLimit</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>윤곽선 Miter limit.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>너비</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_StyleOption_text">odf_StyleOption_text</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">text</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 값</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">textAlign</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 수직정렬</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">textBaseline</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 수평정렬</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">font</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>폰트 크기(필수) 및 글씨체(필수), 두께(옵션) (ex) 'bold 10px sans-serif'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">placement</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트를 타나낼 위치를 점 기준으로 할지, 피쳐의 모양에 따라 나타나게 할지 여부.'line' 또는 'point'(default)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">overflow</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부(placement 속성이 'line'일때 적용)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">maxAngle</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트가 꺾이는 정도를 제한(placement 속성이 'line'일때 적용) (단위:라디안) (ex)Math.PI*270/180</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offsetX</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트의 수평 이동(양수일 때 오른쪽으로 이동)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">offsetY</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트의 수직 이동(양수일 때 아래로 이동)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 크기를 정해진 값의 n배로 적용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotateWithView</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>지도가 회전할때 텍스트도 적절하게 회전할지 여부</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 회전 각도(단위:라디안) (ex)Math.PI*270/180</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">justify</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 상자 내의 텍스트 맞춤</p>
<ul>
<li>'left', 'center', 'right'</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">fill</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_StyleOption_fill">odf_StyleOption_fill</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 채우기 스타일 옵션</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke</span>
        

        
            


    <span class="param-type">
        <code>odf_StyleOption_stroke</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 테두리 스타일 옵션</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">backgroundFill</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odf_StyleOption_fill">odf_StyleOption_fill</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>텍스트 배경 채우기 스타일 속성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">backgroundStroke</span>
        

        
            


    <span class="param-type">
        <code>odf_StyleOption_stroke</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>택스트 배경 테두리 스타일 속성</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">padding</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>text와 background영역 사이의 여백(placement 속성이 'point'일때 적용) (ex)[10,5,5,5]</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_wfs_format_object">odf_wfs_format_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">format</span>
        

        
            


    <span class="param-type">
        <code>'wfs'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wfs 형식</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_wkt_format_object">odf_wkt_format_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">format</span>
        

        
            


    <span class="param-type">
        <code>'wkt'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wkt 형식</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odf_wmsgetfeatureinfo_format_object">odf_wmsgetfeatureinfo_format_object</h4>






    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">format</span>
        

        
            


    <span class="param-type">
        <code>'wmsgetfeatureinfo'</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wmsgetfeatureinfo 형식</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odfFlatStyle">odfFlatStyle</h4>




<div class="description">
    <p>odf flat 스타일</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">valiables</span>
        

        
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>사용자 정의 변수(스타일 표현식에서 ['var','[사용자정의변수명]']으로 사용 가능)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">fill-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>채우기 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>윤곽선 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke-width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>너비</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke-line-cap</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>선의 끝 모양</p>
<ul>
<li><code>butt</code>, <code>round</code>(기본값), <code>square</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke-line-join</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>선이 꺾이는 부분의 모양</p>
<ul>
<li><code>bevel</code>, <code>round</code>(기본값), <code>miter</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke-line-dash</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>점선 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke-line-dash-offset</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>Line dash offset. 기본값 0</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">stroke-miter-limit</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
                <code>10</code>
            
            </span>
        

        <div class="param-description">
            <p>Miter limit.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-value</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트 값</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-font</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>폰트 크기(필수) 및 글씨체(필수), 두께(옵션) (ex) 'bold 10px sans-serif', 기본값 :'10px sans-serif'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-max-angle</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트가 꺾이는 정도를 제한(placement 속성이 'line'일때 적용) (단위:라디안) (ex)Math.PI*270/180</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-offset-x</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트의 수평 이동(양수일 때 오른쪽으로 이동)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-offset-y</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트의 수직 이동(양수일 때 아래로 이동)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-overflow</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부(placement 속성이 'line'일때 적용). 기본값 false</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-placement</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트를 타나낼 위치를 점 기준으로 할지, 피쳐의 모양에 따라 나타나게 할지 여부.'line' 또는 'point'(default)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-repeat</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>간격을 픽셀 단위로 반복합니다. 설정하면 이 간격으로 텍스트가 반복됩니다.(placement 속성이 'line'일때 적용되며 'text-align'속성보다 우선 적용됩니다.)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트 크기를 정해진 값의 n배로 적용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-rotate-with-view</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>지도가 회전할때 텍스트도 적절하게 회전할지 여부(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트 회전 각도(단위:라디안) (ex)Math.PI*270/180</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-align</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트 수직정렬</p>
<ul>
<li>'left', 'right', 'center', 'end', 'start'</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-justify</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트 상자 내의 텍스트 맞춤</p>
<ul>
<li>'left', 'center', 'right'</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-baseline</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트 수직정렬
-'bottom', 'top', 'middle'(기본값), 'alphabetic','hanging', 'ideographic'</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-padding</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>text와 background영역 사이의 여백(placement 속성이 'point'일때 적용) (ex)[10,5,5,5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-fill-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트 색상 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-background-fill-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>텍스트 배경 채우기 색상(ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-stroke-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트  색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-stroke-line-cap</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 선의 끝 모양</p>
<ul>
<li><code>butt</code>, <code>round</code>(기본값), <code>square</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-stroke-line-join</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 선이 꺾이는 부분의 모양</p>
<ul>
<li><code>bevel</code>, <code>round</code>(기본값), <code>miter</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-stroke-line-dash</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 점선 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-stroke-line-dash-offset</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 선 Line dash offset. 기본값 0</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-stroke-miter-limit</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 선 Miter limit.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-stroke-width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 선 두께</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-background-stroke-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 배경 테두리 선 색상</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-background-stroke-line-cap</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 배경 테두리 선의 끝 모양</p>
<ul>
<li><code>butt</code>, <code>round</code>(기본값), <code>square</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-background-stroke-line-join</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 배경 테두리 선이 꺾이는 부분의 모양</p>
<ul>
<li><code>bevel</code>, <code>round</code>(기본값), <code>miter</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-background-stroke-line-dash</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 배경 테두리 점선 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-background-stroke-line-dash-offset</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 배경 테두리 선 Line dash offset. 기본값 0</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-background-stroke-miter-limit</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 배경 테두리 선 Miter limit.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">text-background-stroke-width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>택스트 배경 테두리 선 두께</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-src</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>이미지 소스 URI</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-anchor</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>Anchor. Default value is the icon center.(기본값 [0.5, 0.5])</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-anchor-origin</span>
        

        
            


    <span class="param-type">
        <code>'bottom-left'</code>
    </span>
    |

    <span class="param-type">
        <code>'bottom-right'</code>
    </span>
    |

    <span class="param-type">
        <code>'top-left'</code>
    </span>
    |

    <span class="param-type">
        <code>'top-right'</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>기준점으로부터의 아이콘 위치 (기본값 'top-left')</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-anchor-x-units</span>
        

        
            


    <span class="param-type">
        <code>'fraction'</code>
    </span>
    |

    <span class="param-type">
        <code>'pixels'</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>아이콘 x축 위치 조정 단위.(기본값 'fraction')</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-anchor-y-units</span>
        

        
            


    <span class="param-type">
        <code>'fraction'</code>
    </span>
    |

    <span class="param-type">
        <code>'pixels'</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>아이콘 y축 위치 조정 단위.(기본값 'fraction')</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>이미지 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-cross-origin</span>
        

        
            


    <span class="param-type">
        <code>null</code>
    </span>
    |

    <span class="param-type">
        <code>String</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>CORS 관련 셋팅. (기본값 'anonymous')</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-offset</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>x축 y축 좌표위치 이동 (기본값 [0, 0])</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-displacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>위치이동. 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0])</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-offset-origin</span>
        

        
            


    <span class="param-type">
        <code>'bottom-left'</code>
    </span>
    |

    <span class="param-type">
        <code>'bottom-right'</code>
    </span>
    |

    <span class="param-type">
        <code>'top-left'</code>
    </span>
    |

    <span class="param-type">
        <code>'top-right'</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>오프셋 원점(기본값 'top-left')</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-opacity</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>투명도(기본값 1)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>크기를 정해진 값의 n배로 셋팅(기본값 1)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>아이콘의 너비(픽셀). <code>scale</code>과 함께 이용할 수 없음</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-height</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>아이콘의 높이(픽셀). <code>scale</code>과 함께 이용할 수 없음</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>회전(기본값 0) (ex) 30*Math.PI/180</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-rotate-with-view</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>지도가 회전할 때 이미지도 적절하게 회전할지 여부 (기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-size</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>자연수 배열 (단위 : 픽셀)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">icon-declutter-mode</span>
        

        
            


    <span class="param-type">
        <code>"declutter"</code>
    </span>
    |

    <span class="param-type">
        <code>"obstacle"</code>
    </span>
    |

    <span class="param-type">
        <code>"none"</code>
    </span>
    |

    <span class="param-type">
        <code>undefined</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>디클러터 모드.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-points</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형의 점 수. 다각형의 경우 점의 개수는 변의 개수</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-fill-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형의 채우기 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-stroke-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형 윤곽선 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-stroke-width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형 윤곽선 두께</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-stroke-line-cap</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형 윤곽선의 끝 모양</p>
<ul>
<li><code>butt</code>, <code>round</code>(기본값), <code>square</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-stroke-line-join</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형 윤곽선이 꺾이는 부분의 모양</p>
<ul>
<li><code>bevel</code>, <code>round</code>(기본값), <code>miter</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-stroke-line-dash</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형 윤곽선 점선 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-stroke-line-dash-offset</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형 윤곽선 Line dash offset. 기본값 0</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-stroke-miter-limit</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형 윤곽선 Miter limit.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-radius</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형의 반경</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-radius2</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>정다각형의 내부 반경</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-angle</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>모양의 각도(라디안). 값이 0이면 모양의 점 중 하나가 위를 향하게 됩니다. 기본값은 0</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-displacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>모양의 변위(픽셀). 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0])</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>라디안 단위의 회전입니다(시계 방향으로 양의 회전)(기본값 0)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-rotate-with-view</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>뷰와 함께 모양을 회전할지 여부(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>규모. (기본값 1) 2차원 스케일링이 필요하지 않은 경우 반경, 반경1 및 반경2에 대한 적절한 설정을 사용하면 더 나은 결과를 얻을 수 있음</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">shape-declutter-mode</span>
        

        
            


    <span class="param-type">
        <code>"declutter"</code>
    </span>
    |

    <span class="param-type">
        <code>"obstacle"</code>
    </span>
    |

    <span class="param-type">
        <code>"none"</code>
    </span>
    |

    <span class="param-type">
        <code>undefined</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>디클러터 모드.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-radius</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 반지름</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-fill-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 채우기 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-stroke-color</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 윤곽선 색상 (ex) 'red' | '#55dd20' | [255,0,0,0.5]</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-stroke-width</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 테두리 선 두께</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-stroke-line-cap</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 테두리 선의 끝 모양</p>
<ul>
<li><code>butt</code>, <code>round</code>(기본값), <code>square</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-stroke-line-join</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 테두리 선이 꺾이는 부분의 모양</p>
<ul>
<li><code>bevel</code>, <code>round</code>(기본값), <code>miter</code>.</li>
</ul>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-stroke-line-dash</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 테두리 선 점선 모양</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-stroke-line-dash-offset</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 테두리 선 Line dash offset. 기본값 0</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-stroke-miter-limit</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 테두리 선 Miter limit.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-displacement</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>위치이동. 양수 값은 모양을 오른쪽 및 위쪽으로 이동 (기본값 [0,0])</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-scale</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 크기(radius로 정해진 크기의 n배). 배열일 때, [가로  축척, 세로 축척] 형태임</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-rotation</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>원의 회전각도 (circle-scale이 배열형태로 되어 타원형태가 되었을때 의미있음)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-rotate-with-view</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>뷰와 함께 모양을 회전할지 여부(기본값 false)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">circle-declutter-mode</span>
        

        
            


    <span class="param-type">
        <code>"declutter"</code>
    </span>
    |

    <span class="param-type">
        <code>"obstacle"</code>
    </span>
    |

    <span class="param-type">
        <code>"none"</code>
    </span>
    |

    <span class="param-type">
        <code>undefined</code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>디클러터 모드.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">z-index</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    |

    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        
            <span class="param-attributes">
            
                &lt;optional&gt;<br>
            

            
            </span>
        

        
            <span class="param-default">
            
            </span>
        

        <div class="param-description">
            <p>스타일 z-index</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odfStyleExpression">odfStyleExpression</h4>


    <p class="summary"><p>odf 표현식</p>
<ul>
<li>
<p>읽기 연산자</p>
<ul>
<li><code>['band', bandIndex, xOffset, yOffset]</code> [타일 ​​레이어]</li>
<li><code>['get', 'attributeName', typeHint]</code> 레이어에서 프로퍼티 명이 'attributeName'인 값 조회.</li></ul></li></ul></p>



<div class="description">
    <p>odf 표현식</p>
<ul>
<li>
<p>읽기 연산자</p>
<ul>
<li><code>['band', bandIndex, xOffset, yOffset]</code> [타일 ​​레이어]</li>
<li><code>['get', 'attributeName', typeHint]</code> 레이어에서 프로퍼티 명이 'attributeName'인 값 조회. typeHint는 옵셔널한 값
'string', 'color', 'number', 'boolean', 'number[]'</li>
<li><code>['geometry-type']</code> 도형의 지오메트리 타입
'LineString', 'Point', 'Polygon', <code>MultiLineString</code>, <code>MultiPoint</code>, <code>MultiPolygon</code></li>
<li><code>['resolution']</code> 현재 해상도 값</li>
<li><code>['time']</code> 현재 해상도 값</li>
<li><code>['var', 'varName']</code> 스타일 변수에서 값을 가져옵니다. 해당 변수가 정의되지 않은 경우 오류가 발생</li>
<li><code>['zoom']</code> 현재 확대/축소 수준</li>
<li><code>['id']</code> feature의 id</li>
</ul>
</li>
<li>
<p>수학 연산자</p>
<ul>
<li><code>['*', value1, value2, ...]</code> <code>value1</code> * <code>value2</code> * ...</li>
<li><code>['/', value1, value2]</code> <code>value1</code> / <code>value2</code></li>
<li><code>['+', value1, value2, ...]</code> <code>value1</code> + <code>value2</code> + ...</li>
<li><code>['-', value1, value2]</code> <code>value1</code> - <code>value2</code></li>
<li><code>['clamp', value, low, high]</code> <code>low</code>와 <code>high</code> 사이에 값을 고정. <code>value</code>가 <code>low</code>보다 작으면 <code>low</code> 반환, <code>value</code>가 <code>high</code>보다 크면 <code>high</code> 반환</li>
<li><code>['%', value1, value2]</code> <code>value1 % value2</code></li>
<li><code>['^', value1, value2]</code> <code>value1</code> ^ <code>value2</code></li>
<li><code>['abs', value1]</code> <code>value1</code>의 절대값</li>
<li><code>['floor', value1]</code> <code>value1</code>과 작거나 같은 가장 가까운 정수 반환(내림)</li>
<li><code>['round', value1]</code> <code>value1</code>과 가장 가까운 정수 반환(반올림)</li>
<li><code>['ceil', value1]</code> <code>value1</code>과 크나 같은 가장 가까운 정수 반환(올림)</li>
<li><code>['sin', value1]</code> <code>value1</code>의 sin값 반환</li>
<li><code>['cos', value1]</code> <code>value1</code>의 cosine값 반환</li>
<li><code>['atan', value1, value2]</code> atan2(value1, value2) 반환. <code>value2</code>가 없을 경우 atan(value1) 반환</li>
<li><code>['sqrt', value1]</code> <code>value1</code>의 제곱근 반환</li>
</ul>
</li>
<li>
<p>변환 연산자</p>
<ul>
<li><code>['case', condition1, output1, ...conditionN, outputN, fallback]</code> <code>condition1</code> 조건이 참이면 <code>output1</code> 반환, <code>conditionN</code>이 참이면 <code>outputN</code> 반환. 일치하는 조건이 없으면 <code>fallback</code> 반환</li>
<li><code>['match', input, match1, output1, ...matchN, outputN, fallback]</code> <code>input</code> 값이 <code>match1</code>과 같으면 <code>output1</code> 반환,<code>matchN</code>과 같으면 <code>outputN</code> 반환. 일치하는 값이 없으면 <code>fallback</code> 반환</li>
<li><code>['interpolate', interpolation, input, stop1, output1, ...stopN, outputN]</code> 값을 보간하여 반환. <code>input</code> 값이 <code>stop1</code>과 같으면 <code>output1</code> 반환,<code>stopN</code>과 같으면 <code>outputN</code> 반환</li>
</ul>
</li>
</ul>
<ul>
<li><code>input</code>값이 <code>stop1</code>과 <code>stop2</code> 사이의 값이라면 <code>output1</code>과  <code>output2</code>사이의 값을 보간하여 반환</li>
<li>interpolation은 보간 방법 정의. ['linear'] :  선형 보간법 ,['exponential', base] : 지수보간법(base는 stop A에서 stop B까지의 증가율)</li>
</ul>
<ul>
<li>논리 연산자
<ul>
<li><code>['&lt;', value1, value2]</code> <code>value1</code> &lt; <code>value2</code></li>
<li><code>['&lt;=', value1, value2]</code> <code>value1</code> &lt;= <code>value2</code></li>
<li><code>['&gt;', value1, value2]</code> <code>value1</code> &gt; <code>value2</code></li>
<li><code>['&gt;=', value1, value2]</code> <code>value1</code> &gt;= <code>value2</code></li>
<li><code>['==', value1, value2]</code> <code>value1</code> == <code>value2</code></li>
<li><code>['!=', value1, value2]</code> <code>value1</code> != <code>value2</code></li>
<li><code>['!', value1]</code> !<code>value1</code></li>
<li><code>['all', value1, value2, ...]</code> <code>value1</code>과 <code>value2</code> ...가 모두 true일때 true 반환, 그 외 모두 false</li>
<li><code>['any', value1, value2, ...]</code> <code>value1</code> 또는 <code>value2</code> ... 중 하나라도 true면 true 반환. 모두 false일때 false 반환</li>
<li><code>['between', value1, value2, value3]</code>  <code>value1</code>이 <code>value2</code>와 <code>value3</code> 사이에 있으면 true, 아니면 false 반환. <code>value1</code>이 <code>value2</code> 또는 <code>value3</code>과 동일해도 true</li>
<li><code>['in', needle, haystack]</code> <code>needle</code>이 <code>haystack</code>안에서 발견되면  true, 아니면 false.</li>
</ul>
</li>
</ul>
<ul>
<li><code>haystack</code>은 숫자 또는 문자열로 구성된 배열. 현재는 상수만 지원됨.</li>
</ul>
<ul>
<li>변환 연산자
<ul>
<li><code>['concat', value1, ...valueN]</code> 문자열을 합쳐서 반환</li>
<li><code>['array', value1, ...valueN]</code> 숫자 배열 생성. 현재는 2~4개 요소만 생성 가능</li>
<li><code>['color', red, green, blue, alpha]</code> 색상 값 생성. alpha 값은 0~1 사이의 값</li>
<li><code>['palette', index, colors]</code> colors(색상배열)에서 index 위치의 색상 추출</li>
</ul>
</li>
</ul>
<ul>
<li>colors는 문자열의 배열이며, 색상은 hex 형태('#aaa') 또는 rgb(a) 형태(<code>'rgb(134, 161, 54)'</code> 또는 <code>'rgba(134, 161, 54, 1)'</code>)가 가능</li>
</ul>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Array.&lt;(Sting|Number|<a href="global.html#odfStyleExpression">odfStyleExpression</a>)></code>
    </span>
    

        </li>
    </ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odfStyleRule">odfStyleRule</h4>




<div class="description">
    <p>odf 스타일 규칙</p>
<pre class="prettyprint source lang-javascript"><code>   const rules = [
   {
   filter: ['>', ['get', 'population'], 1_000_000],
   style: {
   'circle-radius': 10,
   'circle-fill-color': 'red',
   }
   },
   {
   else: true,
   style: {
   'circle-radius': 5,
   'circle-fill-color': 'blue',
   },
   },
   ];
</code></pre>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odfStyleRuleObject">odfStyleRuleObject</a>></code>
    </span>
    

        </li>
    </ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odfStyleRuleObject">odfStyleRuleObject</h4>




<div class="description">
    <p>odf 스타일 규칙</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">filter</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfStyleExpression">odfStyleExpression</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>스타일 적용 조건.</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">else</span>
        

        
            


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>true이면 해당 스타일 적용</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">style</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfFlatStyle">odfFlatStyle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>적용 스타일(flat 스타일)</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odfWebGLAttributeObject">odfWebGLAttributeObject</h4>




<div class="description">
    <p>사용자 정의 속성 정보</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">size</span>
        

        
            


    <span class="param-type">
        <code>Number</code>
    </span>
    

        

        

        

        <div class="param-description">
            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">callback</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfWebGLCustomAttributeDefine">odfWebGLCustomAttributeDefine</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>사용자 정의 속성 값을 반환하는 함수</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="odfWebGLCustomAttributeDefine">odfWebGLCustomAttributeDefine<span class="signature">(대상)</span><span class="return-type-signature"> &rarr; {?}</span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">대상</span>
            

            
                


    <span class="param-type">
        <code><a href="Feature.html">Feature</a></code>
    </span>
    

            

            

            

            <div class="param-description"><p>feature</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>?</code>
            
            
                <p>사용자 정의 속성 값</p>
            
        </li>
    
    </ul>


















    </article>

            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="odfWebGLVectorTileStyle">odfWebGLVectorTileStyle</h4>




<div class="description">
    <p>webGL vector tile 레이어 스타일 렌더 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">builder</span>
        

        
            


    <span class="param-type">
        <code><a href="global.html#odfFlatStyle">odfFlatStyle</a></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>사용자 정의 속성 값을 사용할 수 있는 flat style</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributes</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odfWebGLAttributeObject">odfWebGLAttributeObject</a>></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>사용자 정의 속성 정보</p>
<pre class="prettyprint source lang-javascript"><code>{
  builder : {
  'fill-color': ['get', 'fillColor'], //사용자 정의 속성인 'fillColor' 값으로 채우기 색 설정
  'stroke-color': ['get', 'strokeColor'], //사용자 정의 속성인 'strokeColor' 값으로 윤곽선 색 설정
  'stroke-width': ['get', 'strokeWidth'],//사용자 정의 속성인 'strokeWidth' 값으로 윤곽선 두께 설정
  'circle-radius': 4, //원의 크기
  'circle-fill-color': '#777',//원 채우기 색상
  },
  attributes : {
  fillColor: {
  size: 2,
  callback: (feature) => {
  const style = this.getStyle()(feature, 1)[0];
  const color = asArray(style?.getFill()?.getColor() || '#eee');

  //색상을 두개의 부동소수점 배열로 묶는다(압축)
  return odf.ColorFactory.packColor(color);
  },
  },
  strokeColor: {
  size: 2,
  callback: (feature) => {
  const style = this.getStyle()(feature, 1)[0];
  const color = asArray(style?.getStroke()?.getColor() || '#eee');
  return odf.ColorFactory.packColor(color);
  },
  },
  strokeWidth: {
  size: 1,
  callback: (feature) => {
  const style = this.getStyle()(feature, 1)[0];
  return style?.getStroke()?.getWidth() * 2 || 0;
  },
  },
  }
  }
</code></pre>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="positionType">positionType</h4>




<div class="description">
    <p>아래 값들중 선택하여 배열에 넣을 수 있다.
위치 유형 ('right-up' / 'right-down' / 'left-up' / 'left-down')</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Array</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">right-up</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>오른쪽 위 : 위치가 개요지도 버튼의 오른쪽 위에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">right-down</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>오른쪽 아래 : 위치가 개요지도 버튼의 오른쪽 아래에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">left-up</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>왼쪽 위 : 위치가 개요지도 버튼의 왼쪽 위에 위치</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">left-down</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>왼쪽 아래 : 위치가 개요지도 버튼의 왼쪽 아래에 위치</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="service">service</h4>




<div class="description">
    <p>레이어 호출 종류</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">wfs</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wfs 레이어 호출</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">wms</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>wms 레이어 호출</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">group</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>group 레이어 호출 (레이어그룹 호출 시 그룹명으로 레이어 생성 시 속해있는 레이어들 호출)</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">cluster</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>cluster 분석 레이어 호출</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">hotspot</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>hotspot 분석 레이어 호출</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">heatmap</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>heatmap 분석 레이어 호출</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">aggregate</span>
        

        
            


    <span class="param-type">
        <code>String</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>aggregate 분석 레이어 호출</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
                


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="splitFeatureCallback">splitFeatureCallback<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>

















<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

            
                

<article class="member">
    <div class="method-type">
        
    </div>
<h4 class="member-name" id="svgOption">svgOption</h4>




<div class="description">
    <p>svg 레이어 생성 옵션</p>
</div>



    <h4>Type</h4>
    <ul>
        <li>
            


    <span class="param-type">
        <code>Object</code>
    </span>
    

        </li>
    </ul>





    <h4 class="method-heading">Properties</h4>
    

<ul class="method-params">


    <li>
        
            <span class="param-name">svgContainer</span>
        

        
            


    <span class="param-type">
        <code>HTMLElement</code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>svg html element</p>
        </div>

    </li>
    

    <li>
        
            <span class="param-name">extent</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;Number></code>
    </span>
    

        

        

        

        <div class="param-description">
            
        </div>

    </li>
    

    <li>
        
            <span class="param-name">attributions</span>
        

        
            


    <span class="param-type">
        <code>Array.&lt;String></code>
    </span>
    

        

        

        

        <div class="param-description">
            <p>귀속 정의</p>
        </div>

    </li>
    
</ul>




<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>








</article>
            
    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:51 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>