<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>GeOnPaas ui widgets: DivideMapControl</title>
    
      <link type="text/css" rel="stylesheet" href="styles/vendor/prism-tomorrow-night.css">
    
    <link type="text/css" rel="stylesheet" href="styles/styles.css">
    
    
    <style>
      :root {
      
      
        --nav-width: 370px;
      
      
        --nav-heading-margin-top: 0.5em;
      
      }
    </style>
    
</head>
<body>

<header class="layout-header">
  
  <h1>
    <a href="./index.html">
      GeOnPaas ui widgets
    </a>
  </h1>
  <nav class="layout-nav">
    <ul><li class="nav-heading">Classes</li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BasemapControl.html">BasemapControl</a></span><span class="nav-desc"><p>배경지도 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="BookmarkControl.html">BookmarkControl</a></span><span class="nav-desc"><p>북마크 컨트롤 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ClearControl.html">ClearControl</a></span><span class="nav-desc"><p>지도 그리기 이벤트 초기화 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ColorFactory.html">ColorFactory</a></span><span class="nav-desc"><p>색 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Control.html">Control</a></span><span class="nav-desc"><p>사용자 정의 컨트롤 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Coordinate.html">Coordinate</a></span><span class="nav-desc"><p>좌표 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DivideMapControl.html">DivideMapControl</a></span><span class="nav-desc"><p>지도 분할 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DownloadControl.html">DownloadControl</a></span><span class="nav-desc"><p>다운로드 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="DrawControl.html">DrawControl</a></span><span class="nav-desc"><p>그리기 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Easing.html">Easing</a></span><span class="nav-desc"><p>애니메이션 효과</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="event.html">event</a></span><span class="nav-desc"><p>이벤트 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Extent.html">Extent</a></span><span class="nav-desc"><p>영역 관련 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Feature.html">Feature</a></span><span class="nav-desc"><p>Feature 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FeatureFactory.html">FeatureFactory</a></span><span class="nav-desc"><p>Feature 생성을 위한 FeatureFactory 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FormatFactory.html">FormatFactory</a></span><span class="nav-desc"></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="FullScreenControl.html">FullScreenControl</a></span><span class="nav-desc"><p>전체화면 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="HomeControl.html">HomeControl</a></span><span class="nav-desc"><p>홈 이동 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Layer.html">Layer</a></span><span class="nav-desc"><p>레이어 관리 클래스로, 레이어는 odf.LayerFactory를 통해서만 생성가능하다.</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerFactory.html">LayerFactory</a></span><span class="nav-desc"><p>레이어 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="LayerInfoControl.html">LayerInfoControl</a></span><span class="nav-desc"><p>레이어 정보 조회 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Map.html">Map</a></span><span class="nav-desc"><p>지도 생성, 조작, 컴퍼넌트, 레이어 추가 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Marker.html">Marker</a></span><span class="nav-desc"><p>마커 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MeasureControl.html">MeasureControl</a></span><span class="nav-desc"><p>지도 측정 도구 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MousePositionControl.html">MousePositionControl</a></span><span class="nav-desc"><p>마우스 좌표 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="MoveControl.html">MoveControl</a></span><span class="nav-desc"><p>현재 화면 기준으로 이전/다음 화면 이동 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="OverviewMapControl.html">OverviewMapControl</a></span><span class="nav-desc"><p>인덱스맵 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Popup.html">Popup</a></span><span class="nav-desc"><p>팝업 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="PrintControl.html">PrintControl</a></span><span class="nav-desc"><p>프린트 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Projection.html">Projection</a></span><span class="nav-desc"><p>좌표 변환 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="RotationControl.html">RotationControl</a></span><span class="nav-desc"><p>화면을 회전 시키는 기능
alt + shift 드래그로 지도 회전</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ScaleControl.html">ScaleControl</a></span><span class="nav-desc"><p>축척 표시 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SLD.html">SLD</a></span><span class="nav-desc"><p>WMS 스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="Style.html">Style</a></span><span class="nav-desc"><p>스타일 관리 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFactory.html">StyleFactory</a></span><span class="nav-desc"><p>스타일 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="StyleFunction.html">StyleFunction</a></span><span class="nav-desc"><p>스타일 Function 생성 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="SwiperControl.html">SwiperControl</a></span><span class="nav-desc"><p>지도 스와이퍼 설정 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZipControl.html">ZipControl</a></span><span class="nav-desc"><p>Server없이 Layer 생성하는 클래스</p></span></li><li class="nav-heading"><span class="nav-item-type type-class" title="class">C</span><span class="nav-item-name is-class"><a href="ZoomControl.html">ZoomControl</a></span><span class="nav-desc"><p>지도 줌 설정클래스</p></span></li></ul><li class="nav-heading"><a href="global.html">Globals</a></li>
  </nav>
</header>


<main class="layout-main ">
  <div class="container">
    <p class="page-kind">Class</p>
    <h1 class="page-title">DivideMapControl</h1>
    




<section>


<header class="class">


    
        
        <!-- <h2>DivideMapControl</h2> -->

        

        
            <h4 class="method-heading">Summary</h4>
            <div class="class-summary"><p>DivideMapControl 생성자</p>
<pre class="prettyprint source lang-javascript"><code>let divideMapControl = new odf.DivideMapControl({
       dualMap: [
         {
           position: 1,
           mapOption: {
             //지정안한 map옵션은 mainmap 생성시 사용한 mapoption적용
             basemap: {baroEMap:['eMapColor']},
           },
           //controlOption: {//사용할 컨트롤 지정
           // basemap: true,
           // zoom: false,
           // clear: false,
           // download: false,
           // print: false,
           // overviewmap: false,
           // draw: false,
           // measure: false,
           // move: false,
           //},
         },
       ],
       quadMap: [
         {
           // position: 1, //지정안하면 기본 1
           mapOption: {
             basemap: {baroEMap:['eMapColor']},
           },
         },
         {
           //position: 2, //지정안하면 기본 3
           mapOption: {
             basemap:{baroEMap: ['eMapWhite']},
           },
         },
         {
           //position: 4,//지정안하면 기본 4
           mapOption: {
             basemap: {baroEMap:['eMapEnglish']},
           },
         },
       ],
       config : {
         dualMap:{
           divType : 'vertical'//'vertical'(수직분할), 'horizonal'(수평분할)
         }
       }
     });//DivideMapControl 생성
</code></pre></div>
        

        
            <h4 class="method-heading">Description</h4>
            <div class="class-description"><p>지도 분할 설정 클래스</p></div>
        
    
</header>

<article>
    <div class="container-overview">



    
        





    


    
    <h3 class="subtitle">Constructor</h3>
    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="DivideMapControl">new DivideMapControl<span class="signature">(options)</span><span class="return-type-signature"></span>
    </h4>













    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">options</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>분할지도 생성을 위한 option</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">dualMap</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_divideMapOption">odf_divideMapOption</a>></code>
    </span>
    

            

            

            

            <div class="param-description"><p>2분할지도 option</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">threepleMap</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_divideMapOption">odf_divideMapOption</a>></code>
    </span>
    

            

            

            

            <div class="param-description"><p>3분할지도 option</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">quadMap</span>
            

            
                


    <span class="param-type">
        <code>Array.&lt;<a href="global.html#odf_divideMapOption">odf_divideMapOption</a>></code>
    </span>
    

            

            

            

            <div class="param-description"><p>4분할지도 option</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">config</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>분할지도 상세 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">createElementFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>분할지도 내 컨트롤 ui 생성 여부, 기본값 true</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">dualMap</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>2분할지도 상세 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">divType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>2분할지도 분할 유형.</p>
<ul>
<li>'vertical' : 수직 분할 (기본값)
┌─┬─┐
│1│2│
└─┴─┘</li>
<li>'horizonal' : 수평 분할
┌───┐
│ 1 │
├───┤
│ 2 │
└───┘</li>
</ul></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">threepleMap</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>3분할지도 상세 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">divType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>3분할지도 분할 유형</p>
<ul>
<li>'vertical' : 수직 분할 (기본값)
┌─┬─┬─┐
│1│2│3│
└─┴─┴─┘</li>
<li>'horizonal' : 수평 분할
┌───┐
│ 1 │
├───┤
│ 2 │
├───┤
│ 3 │
└───┘</li>
<li>'complex-01' : 복합형 1
┌─┬───┐
│ │ 2 │
│1├───┤
│ │ 3 │
└─┴───┘</li>
<li>'complex-02' : 복합형 2
┌─────┐
│  1  │
├──┬──┤
│2 │ 3│
└──┴──┘</li>
<li>'complex-03' : 복합형 3
┌──┬─┐
│2 │ │
├──┤1│
│3 │ │
└──┴─┘</li>
<li>'complex-04' : 복합형 4
┌──┬──┐
│ 1│2 │
├──┴──┤
│  3  │
└─────┘</li>
</ul></div>
            
        </li>

    
</ul>
            
        </li>

    

        <li>
            
                <span class="param-name">quadMap</span>
            

            
                


    <span class="param-type">
        <code>Object</code>
    </span>
    

            

            

            

            <div class="param-description"><p>4분할지도 상세 생성 옵션</p></div>
            
                <p class="param-properties">Properties</p>
                

<ul class="method-params">
    

        <li>
            
                <span class="param-name">divType</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>4분할지도 분할 유형.</p>
<ul>
<li>'complex' : 수직,수평 분할 (기본값)
┌───┬───┐
│ 1 │ 2 │
├───┼───┤
│ 3 │ 4 │
└───┴───┘</li>
<li>'vertical' : 수직 분할
┌─┬─┬─┬─┐
│1│2│3│4│
└─┴─┴─┴─┘</li>
<li>'horizonal' : 수평 분할
┌─────┐
│  1  │
├─────┤
│  2  │
├─────┤
│  3  │
├─────┤
│  4  │
└─────┘</li>
</ul></div>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    
</ul>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    
    </div>

    

    

     

    

    


    

    
        <h3 class="subtitle">Methods</h3>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getConstructorOptions">getConstructorOptions<span class="signature">()</span><span class="return-type-signature"> &rarr; {Array.&lt;Object>}</span>
    </h4>





<div class="method-description">
    
    <p>컨트롤 생성 옵션 반환</p>
<pre class="prettyprint source lang-javascript"><code> let divideMapControl = new odf.DivideMapControl({...});
 divideMapControl.getConstructorOptions();
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>Array.&lt;Object></code>
            
            
                <p>컨트롤 생성 옵션 반환</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="getDividMaps">getDividMaps<span class="signature">()</span><span class="return-type-signature"> &rarr; {<a href="global.html#odf_divideMaps">odf_divideMaps</a>}</span>
    </h4>





<div class="method-description">
    
    <p>분할지도 지도객체 조회</p>
<pre class="prettyprint source lang-javascript"><code>let divideMapControl = new odf.DivideMapControl();//DivideMapControl 생성
divideMapControl.setMap(map); //DivideMapControl 객체에 map 객체 연결
divideMapControl.getDividMaps();//분할지도 지도객체 조회
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>



    <h4 class="method-heading">Returns</h4>
    <ul>
    
        <li class="method-returns">
            
                <code>odf_divideMaps</code>
            
            
                <p>-분할지도 지도 객체</p>
            
        </li>
    
    </ul>


















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="removeMap">removeMap<span class="signature">()</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DivideMapControl map 객체 연결 해제</p>
<pre class="prettyprint source lang-javascript"><code> divideMapControl.removeMap(); //DivideMapControl 객체에 map 연결 해제
</code></pre>
</div>













<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setConnect">setConnect<span class="signature">(flag)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>분할지도 뷰공유 활성화/비활성화</p>
<pre class="prettyprint source lang-javascript"><code>let divideMapControl = new odf.DivideMapControl();//DivideMapControl 생성
divideMapControl.setMap(map); //DivideMapControl 객체에 map 객체 연결
divideMapControl.setConnect(true); //dualMap 활성화
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도 뷰공유 여부(default true) =&gt;true : 지도 뷰공유, false:지도 비공유</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setMap">setMap<span class="signature">(_map, createElementFlag<span class="signature-attributes">nullable</span>)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>DivideMapControl map 객체 연결
DivideMapControl HTMLElement를 만들고, 이벤트 바인딩 시켜, map 객체와 연결</p>
<pre class="prettyprint source lang-javascript"><code>let divideMapControl = new odf.DivideMapControl();//DivideMapControl 생성
divideMapControl.setMap(map,createElementFlag); //DivideMapControl 객체에 map 객체 연결
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">_map</span>
            

            
                


    <span class="param-type">
        <code><a href="Map.html">Map</a></code>
    </span>
    

            

            
                <span class="param-attributes">
                

                

                
                </span>
            

            

            <div class="param-description"><p>DivideMapControl 객체와 연결할 Map 객체</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">createElementFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            
                <span class="param-attributes">
                

                
                    &lt;nullable&gt;<br>
                

                
                </span>
            

            

            <div class="param-description"><p>DivideMapControl 버튼 생성 여부 : default = true (Element) 생성, false = (Element) 생성 x</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
            


    <article class="method">




    


    

    <div class="method-type">
    
    </div>

    <h4 class="method-name" id="setOn">setOn<span class="signature">(key, flag, sizeFlag, connectFlag)</span><span class="return-type-signature"></span>
    </h4>





<div class="method-description">
    
    <p>분할지도 활성화/비활성화</p>
<pre class="prettyprint source lang-javascript"><code>let divideMapControl = new odf.DivideMapControl();//DivideMapControl 생성
divideMapControl.setMap(map); //DivideMapControl 객체에 map 객체 연결
divideMapControl.setOn('dualMap',true); //dualMap 활성화
//divideMapControl.setOn('dualMap',false); //dualMap 비활성화
</code></pre>
</div>









    <h4 class="method-heading">Parameters</h4>
    

<ul class="method-params">
    

        <li>
            
                <span class="param-name">key</span>
            

            
                


    <span class="param-type">
        <code>String</code>
    </span>
    

            

            

            

            <div class="param-description"><p>(비)활성화 시킬 대상 =&gt; 'dualMap' 또는 'quadMap'</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">flag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>활성화 여부 =&gt;true : 활성화, false:비활성화</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">sizeFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>사이즈 변환여부(default true) =&gt;true : 사이즈 변환, false:사이즈 변환x</p></div>
            
        </li>

    

        <li>
            
                <span class="param-name">connectFlag</span>
            

            
                


    <span class="param-type">
        <code>Boolean</code>
    </span>
    

            

            

            

            <div class="param-description"><p>지도 동기화 여부(default true) =&gt;true : 지도 동기화, false:지도 비동기화</p></div>
            
        </li>

    
</ul>





<div class="details">
    

    

    

    

    

    

    

    

    

    

    

    

    

    

    
</div>




















    </article>

        
    

    

    
</article>

</section>




  </div>
</main>

<footer class="layout-footer">
  <div class="container">
    Documentation generated by <a href="https://github.com/jsdoc3/jsdoc">JSDoc 3.6.11</a> on Tue Jan 21 2025 11:05:51 GMT+0900 (대한민국 표준시)
  </div>
</footer>



<script src="scripts/prism.dev.js"></script>
</body>
</html>