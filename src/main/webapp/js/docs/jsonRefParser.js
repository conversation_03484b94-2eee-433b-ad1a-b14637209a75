var jsonRefParser = {
		
		specJson: {},
		
		init: function(json) {
			jsonRefParser.specJson = json;
		},
		
		refParse: function(schema) {
			
			var result = {};
			
			if(schema.$ref != undefined) {
				
				var defName = schema.$ref.replace('#/definitions/', '');
				var obj = jsonRefParser.specJson.definitions[defName];
				$.each(obj.properties, function(key, val) {

					if(val.type == "array") {
						result[key] = [];
						var parsed = jsonRefParser.refParse(val.items);
						result[key].push(parsed);
					} else {
						result[key] = val.example;
					}
					
				});
			}
			
			return result;
		},
		
		refModelCombine(specDefinition, nowDefinition, resultDefinitions) {
			var pattern = /"\$ref":"#\/definitions\/([^"]+)"/gi;
			try{
			var matched = pattern.exec(JSON.stringify(nowDefinition));
			}catch(err){
				console.log(err)
			}
			
			while(matched) {
				
				var modelKey = matched[1];
				resultDefinitions[modelKey] = specDefinition[modelKey];
				jsonRefParser.refModelCombine(specDefinition, specDefinition[modelKey], resultDefinitions);
				try{
				matched = pattern.exec(JSON.stringify(nowDefinition))
				}catch(err){
					console.log(err)
				}
			}
		}
}