document.body.onload = function test() {	
    // 필요한 변수 선언
    const input = document.querySelector("input[type='search']"),
        clearBtn = document.querySelector("button[functionName='clear']"),
        prevBtn = document.querySelector("button[functionName='prev']"),
        nextBtn = document.querySelector("button[functionName='next']"),
        targetElement = document.querySelector("#mCSB_2_container");
    
    let	contentBody = document.querySelector("#map-doc > main > .container");
    
    let contentMark = new Mark(contentBody),
        results,
        currentClass = "current",
        offsetTop = 200,
        currentIndex = 0,
        resultCount = 0,
        totalCount = 0;

    // input 의 기능 설정
    input.addEventListener("input", function() {
        let searchVal = this.value;
        contentMark.unmark( {
            done: function(totalMatches) {
                contentMark.mark(searchVal, {
                    separateWordSearch: false,
                    acrossElements: true,
                    done: function(totalMatches) {
                    	try {
                    		results = contentBody.querySelectorAll("mark");
                    		//console.log(results);
                    		if(searchVal != "" && searchVal != null && results.length != 0) {
                    			// 검색 결과 중 첫번째 element 에는 current 클래스를 줘서 주황색으로 강조되도록 한다.
                    			results[0].classList.add(currentClass);
                    		} else {
                    			$(".countBody").html("");
                    		}
                    		totalCount = totalMatches;
                    		if (totalCount) {
                    			resultCount = 1;
                    			$(".countBody").html(resultCount+ " / " +totalCount);
                    			jumpTo();
                    		}
                    	} catch(e) {
                    		$(".countBody").html("0 / 0");
                    	}
                    }
                });
            }
        });
    });

    // 이전, 다음 버튼 기능 설정
    nextBtn.addEventListener("click", prevNextHandler);
    prevBtn.addEventListener("click", prevNextHandler);
    nextBtn.after(prevBtn);
    function prevNextHandler() {
        // 이전, 다음 버튼 공통 실행
        if (results.length) {
            currentIndex += (this === prevBtn? -1 : 1);
            if (currentIndex < 0) {
                currentIndex = results.length - 1;
            }
            if (currentIndex > results.length - 1) {
                currentIndex = 0;
            }
            jumpTo();
        }
    }

    // 이전, 다음 버튼 각각에 count 기능 추가
    $("[functionName=next]").click(function() {
        if (resultCount < totalCount) {
            resultCount = resultCount + 1;
        } else {
            resultCount = 1;
        }
        if(totalCount != 0) {
        	$(".countBody").html(resultCount+ " / " +totalCount);
        }
    })

    $("[functionName=prev]").click(function() {
        if (resultCount > 1) {
            resultCount = resultCount - 1;
        } else {
            resultCount = totalCount;
        }
        if(totalCount != 0) {
        	$(".countBody").html(resultCount+ " / " +totalCount);
        }
    })
    
    // jumpTo 를 통해 해당 검색결과로 이동한다
    function jumpTo() {
        if (results.length) {
            let position;
            let current = results[currentIndex];
            results.forEach(result => result.classList.remove(currentClass));
            if (current) {
            	//console.log(current);
                current.classList.add(currentClass);
                // 코드 블럭 안의 컨텐츠로 이동이 불가하여 해당 컨텐츠를 담고있는 코드블럭 자체의 위치를 구해서 이동하는 걸로 구현
                if(current.parentElement.classList.contains("token")) {
                	// 코드 블럭 안에 있을 경우 (대상이 span 태그에 포함된 경우)
                	position = current.parentElement.parentElement.parentElement.offsetTop - offsetTop;
                } else if(current.parentElement.classList.contains("language-js")) {
                	// 코드 블럭 안에 있을 경우 (대상이 span 태그에 포함되지 않은 경우)
                	position = current.parentElement.parentElement.offsetTop - offsetTop;
                } else {
                	// 코드 블럭을 제외한 부분
                	position = current.offsetTop - offsetTop;                	
                }
                
                if(position < 0) {
                	// header 에 결과값이 있을 경우 position 보정 (main 은 그대로)
                	position *= -1;
                }
                
                targetElement.style.top = "-" + position + "px";                	
                
                //window.scrollTo(0, position);
                //console.log(position);
            }
        }
    }

    // 닫기 기능과 버튼 추가
    function closeSearchBox() {
        // 변경된 사항들을 초기화한다.
        contentMark.unmark();
        input.value = "";
        currentIndex = 0;
        resultCount = 0;
        totalCount = 0;
        $(".countBody").html("");

        $(".searchBody").removeClass("show");
        $(".searchBody").addClass("hide");
    }
    clearBtn.addEventListener("click", closeSearchBox);
    
    // ctrl + f, esc 이벤트 작성
    document.onkeydown = function(e) {
        // ctrl + f Event
        if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) { 
            e.preventDefault();
            //console.log("CTRL + F 이벤트 동작");
            $(".searchBody").removeClass("hide");
            $(".searchBody").addClass("show");
            input.focus();
        }

        // esc Event
        if (e.keyCode == 27 || e.which == 27) {
            e.preventDefault();
            //console.log("ESC 이벤트 동작");
            closeSearchBox();
        }
    }
    // 검색어 변경시 기본 셋팅값들 초기화
    input.addEventListener("input", resetParams);
    
    function resetParams() {
    	// 이전 검색결과의 currentIndex 를 초기화
    	currentIndex = 0;
    	contentBody = document.querySelector("#map-doc > main > .container");
    	contentMark = new Mark(contentBody);
    }
    const menuList = document.querySelector(".menuList");
    menuList.addEventListener("click", function(){
    	// doc 변경 시 검색창 종료 및 변수들 초기화
    	closeSearchBox();
    })
    
};

// 화면 우측 상단에 플로팅 되도록 설정
$(document).ready(function () {
    $(".searchBody").addClass("hide");
    $(".searchBody").addClass("position_absolute");

    let position_set = $(".searchBody").offset().top - 165;
    $(window.parent).scroll(function () {
        if ($(this).scrollTop() >= position_set) {
            $(".searchBody").addClass("position_fixed");
            $(".searchBody").removeClass("position_absolute");
        }
        else {
            $(".searchBody").addClass("position_absolute");
            $(".searchBody").removeClass("position_fixed");
        }
    });
});