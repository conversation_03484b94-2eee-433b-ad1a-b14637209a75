/**
 * 
 */

$(document).ready(function() {
		
	site.setGnbMenu();
	site.eventScrollButton();
	site.eventScrollBar();
});


var site = {
		
		setGnbMenu : function() {
			
			$("#gnbMenu li a").each(function(index, item) {
				
				if($(this).attr("href") == window.location.pathname) {
					$(this).attr("class", "active");
					return false;
				}
			});
		},
		
		eventTab : function() {
			
			$(".tabs button").on("click", function(event) {
				
				$(this).parent().parent().find(".tabs button").removeClass("blueType").addClass("greyType");
				$(this).removeClass("greyType").addClass("blueType");
				
				$(this).parent().parent().find(".tab-content div.tab-pannel").removeClass("current");
				var id = $(this).attr("data-id");
				$("#" + id).addClass("current");
				
			});
		},
		
		eventScrollButton: function() {
			/* top bottom 버튼 */
		    $('.btnTop').on({
		        "click":function(){
		            $('#content .mCSB_container, #content .mCSB_dragger').animate({top:0}),500;
		        }
		    });
		    $('.btnBottom').on({
		        "click":function(){
		            $('#content .mCSB_dragger').animate({top:$(' #content .mCSB_scrollTools').outerHeight() - $('#content .mCSB_dragger_bar').outerHeight()}),500;
		            $('#content .mCSB_container').animate({top:$('#content').outerHeight() - $('.innerContent').outerHeight()})
		        }
		    });
		},
		
		eventScrollBar: function() {
			
			$(".mScroll").mCustomScrollbar({
		        scrollbarPosition:'outside',
		        scrollInertia: 500,
		        mouseWheelPixels: 200,
		        mouseWheel:{
		        	disableOver:['canvas']
		        }
		    });
		}
}

