<!doctype html>

<title>CodeMirror: Elm mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="elm.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Elm</a>
  </ul>
</div>

<article>
<h2>Elm mode</h2>

<div><textarea id="code" name="code">
import Color exposing (..)
import Graphics.Collage exposing (..)
import Graphics.Element exposing (..)
import Time exposing (..)

main =
  Signal.map clock (every second)

clock t =
  collage 400 400
    [ filled    lightGrey   (ngon 12 110)
    , outlined (solid grey) (ngon 12 110)
    , hand orange   100  t
    , hand charcoal 100 (t/60)
    , hand charcoal 60  (t/720)
    ]

hand clr len time =
  let angle = degrees (90 - 6 * inSeconds time)
  in
      segment (0,0) (fromPolar (len,angle))
        |> traced (solid clr)
</textarea></div>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        mode: "text/x-elm"
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-elm</code>.</p>
  </article>
