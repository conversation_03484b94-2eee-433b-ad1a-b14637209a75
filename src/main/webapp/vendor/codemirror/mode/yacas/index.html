<!doctype html>

<title>CodeMirror: yacas mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel=stylesheet href=../../lib/codemirror.css>
<script src=../../lib/codemirror.js></script>
<script src=../../addon/edit/matchbrackets.js></script>
<script src=yacas.js></script>
<style type=text/css>
  .CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}
</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">yacas</a>
  </ul>
</div>

<article>
<h2>yacas mode</h2>


<textarea id="yacasCode">
// example yacas code
Graph(edges_IsList) <-- [
    Local(v, e, f, t);

    vertices := {};

    ForEach (e, edges) [
        If (IsList(e), e := Head(e));
        {f, t} := Tail(Listify(e));

        DestructiveAppend(vertices, f);
        DestructiveAppend(vertices, t);
    ];

    Graph(RemoveDuplicates(vertices), edges);
];

10 # IsGraph(Graph(vertices_IsList, edges_IsList)) <-- True;
20 # IsGraph(_x) <-- False;

Edges(Graph(vertices_IsList, edges_IsList)) <-- edges;
Vertices(Graph(vertices_IsList, edges_IsList)) <-- vertices;

AdjacencyList(g_IsGraph) <-- [
    Local(l, vertices, edges, e, op, f, t);

    l := Association'Create();

    vertices := Vertices(g);
    ForEach (v, vertices)
        Association'Set(l, v, {});

    edges := Edges(g);

    ForEach(e, edges) [
        If (IsList(e), e := Head(e));
        {op, f, t} := Listify(e);
        DestructiveAppend(Association'Get(l, f), t);
        If (String(op) = "<->", DestructiveAppend(Association'Get(l, t), f));
    ];

    l;
];
</textarea>

<script>
  var yacasEditor = CodeMirror.fromTextArea(document.getElementById('yacasCode'), {
    mode: 'text/x-yacas',
    lineNumbers: true,
    matchBrackets: true
  });
</script>

<p><strong>MIME types defined:</strong> <code>text/x-yacas</code> (yacas).</p>
</article>
