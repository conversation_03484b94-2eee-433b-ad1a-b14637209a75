<!doctype html>

<title>CodeMirror: Soy (Closure Template) mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="../htmlmixed/htmlmixed.js"></script>
<script src="../xml/xml.js"></script>
<script src="../javascript/javascript.js"></script>
<script src="../css/css.js"></script>
<script src="soy.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Soy (Closure Template)</a>
  </ul>
</div>

<article>
<h2>Soy (Closure Template) mode</h2>
<form><textarea id="code" name="code">
{namespace example}

/**
 * Says hello to the world.
 */
{template .helloWorld}
  {@param name: string}
  {@param? score: number}
  Hello <b>{$name}</b>!
  <div>
    {if $score}
      <em>{$score} points</em>
    {else}
      no score
    {/if}
  </div>
{/template}

{template .alertHelloWorld kind="js"}
  alert('Hello World');
{/template}
</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-soy",
        indentUnit: 2,
        indentWithTabs: false
      });
    </script>

    <p>A mode for <a href="https://developers.google.com/closure/templates/">Closure Templates</a> (Soy).</p>
    <p><strong>MIME type defined:</strong> <code>text/x-soy</code>.</p>
  </article>
