<!doctype html>

<title>CodeMirror: MUMPS mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="mumps.js"></script>
<style>.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="https://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png" alt=""></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">MUMPS</a>
  </ul>
</div>

<article>
<h2>MUMPS mode</h2>


<div><textarea id="code" name="code">
 ; Lloyd Milligan
 ; 03-30-2015
 ;
 ; MUMPS support for Code Mirror - Excerpts below from routine ^XUS
 ;
CHECKAV(X1) ;Check A/V code return DUZ or Zero. (Called from XUSRB)
 N %,%1,X,Y,IEN,DA,DIK
 S IEN=0
 ;Start CCOW
 I $E(X1,1,7)="~~TOK~~" D  Q:IEN>0 IEN
 . I $E(X1,8,9)="~1" S IEN=$$CHKASH^XUSRB4($E(X1,8,255))
 . I $E(X1,8,9)="~2" S IEN=$$CHKCCOW^XUSRB4($E(X1,8,255))
 . Q
 ;End CCOW
 S X1=$$UP(X1) S:X1[":" XUTT=1,X1=$TR(X1,":")
 S X=$P(X1,";") Q:X="^" -1 S:XUF %1="Access: "_X
 Q:X'?1.20ANP 0
 S X=$$EN^XUSHSH(X) I '$D(^VA(200,"A",X)) D LBAV Q 0
 S %1="",IEN=$O(^VA(200,"A",X,0)),XUF(.3)=IEN D USER(IEN)
 S X=$P(X1,";",2) S:XUF %1="Verify: "_X S X=$$EN^XUSHSH(X)
 I $P(XUSER(1),"^",2)'=X D LBAV Q 0
 I $G(XUFAC(1)) S DIK="^XUSEC(4,",DA=XUFAC(1) D ^DIK
 Q IEN
 ;
 ; Spell out commands
 ;
SET2() ;EF. Return error code (also called from XUSRB)
 NEW %,X
 SET XUNOW=$$HTFM^XLFDT($H),DT=$P(XUNOW,".")
 KILL DUZ,XUSER
 SET (DUZ,DUZ(2))=0,(DUZ(0),DUZ("AG"),XUSER(0),XUSER(1),XUTT,%UCI)=""
 SET %=$$INHIBIT^XUSRB() IF %>0 QUIT %
 SET X=$G(^%ZIS(1,XUDEV,"XUS")),XU1=$G(^(1))
 IF $L(X) FOR I=1:1:15 IF $L($P(X,U,I)) SET $P(XOPT,U,I)=$P(X,U,I)
 SET DTIME=600
 IF '$P(XOPT,U,11),$D(^%ZIS(1,XUDEV,90)),^(90)>2800000,^(90)'>DT QUIT 8
 QUIT 0
 ;
 ; Spell out commands and functions
 ;
 IF $PIECE(XUSER(0),U,11),$PIECE(XUSER(0),U,11)'>DT QUIT 11 ;Terminated
 IF $DATA(DUZ("ASH")) QUIT 0 ;If auto handle, Allow to sign-on p434
 IF $PIECE(XUSER(0),U,7) QUIT 5 ;Disuser flag set
 IF '$LENGTH($PIECE(XUSER(1),U,2)) QUIT 21 ;p419, p434
 Q 0
 ;
  </textarea></div>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
         mode: "mumps",
         lineNumbers: true,
         lineWrapping: true
      });
    </script>

  </article>
