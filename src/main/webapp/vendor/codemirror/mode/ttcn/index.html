<!doctype html>

<title>CodeMirror: TTCN mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../../addon/edit/matchbrackets.js"></script>
<script src="ttcn.js"></script>
<style>
    .CodeMirror {
        border-top: 1px solid black;
        border-bottom: 1px solid black;
    }
</style>
<div id=nav>
    <a href="https://codemirror.net"><h1>CodeMirror</h1>
        <img id=logo src="../../doc/logo.png" alt="">
    </a>

    <ul>
        <li><a href="../../index.html">Home</a>
        <li><a href="../../doc/manual.html">Manual</a>
        <li><a href="https://github.com/codemirror/codemirror">Code</a>
    </ul>
    <ul>
        <li><a href="../index.html">Language modes</a>
        <li><a class=active href="http://en.wikipedia.org/wiki/TTCN">TTCN</a>
    </ul>
</div>
<article>
    <h2>TTCN example</h2>
    <div>
        <textarea id="ttcn-code">
module Templates {
  /* import types from ASN.1 */
  import from Types language "ASN.1:1997" all;

  /* During the conversion phase from ASN.1 to TTCN-3 */
  /* - the minus sign (Message-Type) within the identifiers will be replaced by underscore (Message_Type)*/
  /* - the ASN.1 identifiers matching a TTCN-3 keyword (objid) will be postfixed with an underscore (objid_)*/

  // simple types

  template SenderID localObjid := objid {itu_t(0) identified_organization(4) etsi(0)};

  // complex types

  /* ASN.1 Message-Type mapped to TTCN-3 Message_Type */
  template Message receiveMsg(template (present) Message_Type p_messageType) := {
    header := p_messageType,
    body := ?
  }

  /* ASN.1 objid mapped to TTCN-3 objid_ */
  template Message sendInviteMsg := {
      header := inviteType,
      body := {
        /* optional fields may be assigned by omit or may be ignored/skipped */
        description := "Invite Message",
        data := 'FF'O,
        objid_ := localObjid
      }
  }

  template Message sendAcceptMsg modifies sendInviteMsg := {
      header := acceptType,
      body := {
        description := "Accept Message"
      }
    };

  template Message sendErrorMsg modifies sendInviteMsg := {
      header := errorType,
      body := {
        description := "Error Message"
      }
    };

  template Message expectedErrorMsg := {
      header := errorType,
      body := ?
    };

  template Message expectedInviteMsg modifies expectedErrorMsg := {
      header := inviteType
    };

  template Message expectedAcceptMsg modifies expectedErrorMsg := {
      header := acceptType
    };

} with { encode "BER:1997" }
        </textarea>
    </div>

    <script> 
      var ttcnEditor = CodeMirror.fromTextArea(document.getElementById("ttcn-code"), {
        lineNumbers: true,
        matchBrackets: true,
        mode: "text/x-ttcn"
      });
      ttcnEditor.setSize(600, 860);
      var mac = CodeMirror.keyMap.default == CodeMirror.keyMap.macDefault;
      CodeMirror.keyMap.default[(mac ? "Cmd" : "Ctrl") + "-Space"] = "autocomplete";
    </script>
    <br/>
    <p><strong>Language:</strong> Testing and Test Control Notation
        (<a href="http://en.wikipedia.org/wiki/TTCN">TTCN</a>)
    </p>
    <p><strong>MIME types defined:</strong> <code>text/x-ttcn,
        text/x-ttcn3, text/x-ttcnpp</code>.</p>
    <br/>
    <p>The development of this mode has been sponsored by <a href="http://www.ericsson.com/">Ericsson
    </a>.</p>
    <p>Coded by Asmelash Tsegay Gebretsadkan </p>
</article>

