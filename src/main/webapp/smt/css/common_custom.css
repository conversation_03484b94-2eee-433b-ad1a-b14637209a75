@charset "UTF-8";
.popup{
    z-index: 999999;
}
.btn.sm {
    letter-spacing: -2.5px;
}
#optionTable .head .btnOpTableClose{
	background: url("../images/toc/ico-toc-close_white.png") no-repeat center;
}
#userMenu .userInfo .alarm .btnAlarmDiv.loading .alarmLoading {
	position: absolute;
	left: 0;
	top: 0;
	width: 33px;
    height: 33px;
	border-radius: 50%;
	display: block;
	content: '';
		animation: 0.8s loading linear infinite;
	background: #2F5597 url("../images/common/ico-alarm-loading.png") no-repeat center;
}
#userMenu .userInfo .alarm .btnAlarmDiv.loading .alarmLoading:before {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	content: '';
	display: block;
	width: 9px;
	height: 13px;
	z-index: 10;
	background: url("../images/common/ico-alarm-loading-2.png") no-repeat center;
	animation: 1s loading2 linear infinite alternate;
}
#userMenu .userInfo {
    align-items: inherit;
}
#header .menu li.geocoding:hover:after, #header .menu li.geocoding.active:after {
	display: none;
}
#widget .dep1> li .basemap_tool span:hover{
		color: #fff;
	}
#widget .dep1> li .divideMap_tool span:hover{
		color: #fff;
	}
#widget .dep1> li .homeControl_moveHomeBtn span:hover{
		color: #fff;
	}
#widget .dep1> li .drawControl_toolBtn span:hover{
		color: #fff;
	}		
#widget .dep1> li .measureControl_toolBtn span:hover{
		color: #fff;
	}
#widget .dep1> li .clearControl_clearBtn span:hover{
		color: #fff;
	}	
#widget .dep1> li .divideMap_tool span:hover{
		color: #fff;
	}	
#widget .dep1> li .printControl_printBtn span:hover{
		color: #fff;
	}	
#widget .dep1> li .pnuGetter_searchBtn span:hover{
		color: #fff;
	}	
#widget .dep1> li .downloadControl_toolBtn span:hover{
		color: #fff;
	}		
#optionTable #close{
	display : none;
}
.searchLoading{
	position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.55);
    z-index: 99999999;
}
.searchLoading .box {
	position: absolute;
    display: flex;
    align-items: center;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}
.searchLoading .loadingImg {
	
    width: 28px;
    height: 28px;
    margin: 0 auto;
    background: url(../images/common/ico-search-loading.png) no-repeat center;
    background-size: cover;
    animation: loading 7s infinite linear;
}
.searchLoading p {
	display: block;
    margin-left: 10px;
    color: #fff;
    font-size: 16px;
    font-family: "Pretendard";
    text-align: center;
}

@keyframes loading{
	from {
		transform : rotate(0);
	}
	to{
		transform : rotate(360deg);
	}
}

/* line 1455, scss/common.scss */
.app {
	display: flex;
	flex-direction: column;
	height: 100%;
}

/* line 1456, scss/common.scss */
.app > .inner {
	padding: 30px;
	background: #fff;
}

.thisAddr .addrItem .roadAddr, .thisAddr .addrItem .jibunAddr, .thisAddr .addrItem .coordAddr {
	display:flex;
	align-items: normal;
}
.odfMarker > .thisAddr {
	width: 300px;
    top: -107px;
    left: -26px;
}
/* line 1457, scss/common.scss */
.app > .inner.type02 {
	display: flex;
	height: calc(100% - 120px);
	padding: 60px;
	background: #fafafa;
}

/* line 1459, scss/common.scss */
.app .tempList {
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 1856px;
}

/* line 1460, scss/common.scss */
.app .tempList .inner {
	display: flex;
	flex-wrap: wrap;
	padding: 0 228px;
}

/* line 1461, scss/common.scss */
.app .tempList .temp {
	width: 440px;
	margin-right: 40px;
	border: 2px solid transparent;
	box-sizing: border-box;
	border-radius: 4px;
	cursor: pointer;
}

/* line 1462, scss/common.scss */
.app .tempList .temp:hover, .app .tempList .temp.active {
	border: 2px solid #333;
}

/* line 1463, scss/common.scss */
.app .tempList .temp .inner {
	padding: 20px;
}

/* line 1464, scss/common.scss */
.app .tempList .temp:nth-of-type(3), .app .tempList .temp:nth-of-type(6){
	margin-right: 0;
}


/* line 1465, scss/common.scss */
.app .tempList .temp .txtBox {
	margin-top: 10px;
}

/* line 1466, scss/common.scss */
.app .tempList .temp .tit {
	font-size: 18px;
	font-family: '맑은 고딕';
	color: #333;
}

/* line 1467, scss/common.scss */
.app .tempList .temp .desc {
	margin-top: 10px;
	font-size: 16px;
	font-family: '맑은 고딕';
	color: #333;
	letter-spacing: -1.2px;
}

.sampleFrame {
    border-radius: 0px;
}

.sampleFrame > .cont .contentArea {
    overflow : hidden;
}
#header .logo {
    text-align: center;
    margin: 10px 0;
}
.chartLayerSelect{
	width: 240px;
	height: 40px;
}

.chartContentTab{
	margin-top:20px;
	display : flex;
	background-color : white;
	/* justify-content: space-between */
}
.setChartBtn{
	background-color: #8faadc;
	padding: 0 15px;
	height: 40px;
    border-radius: 4px;
    box-sizing: border-box;
    font-family: "Pretendard";
    font-weight: normal;
    font-size: 14px;
    color: #fff;
    margin-left: 20px;
}
.chartLabel {
	display: block;
    width: 60px;
    text-align: center;
	color: #555;
    letter-spacing: -1.7px;
    font-size: 17px;
    margin-top: 8px;
}
.tagList a span{
	cursor: pointer;
}
.tabContWrap.type03 > .tabNav .tabList li .leng .red{
	color: #ff4600;
}
#webAppDetailModal .tabContWrap.type02 .tabCont {
    padding-top: 30px;
}
.subCont{	
	display: block;
    margin-top: 9px;
    margin-left: 16px;	
    max-height: 100px;
    overflow-y: auto;
}

#header .menu li > .tool span{
	padding-top: 55px;
    letter-spacing: 0px;
    /* color : #f1f1f1; */
}

.resultArea dl dd {
	margin-right: 18px;
}

.tabContWrap > .tabNav .tabList {
	border: 1px solid #ECECEC;
    background: #fff;
    border-radius:50px;
}

.tabContWrap.type05 > .tabNav .tabList li {
color:#959595;
margin : 0;
border :0; 
background : unset;
}
.tabContWrap.type05 > .tabNav .tabList li.active {
	position: relative;
    color: #000;
    border-radius: 50px;
    background-color: #D0E4FF;
}

.fileSelect .fileLocal {
border:0;
border-bottom:1px solid #E9E9E9;
border-radius:0;
}

.secInner .fileSelect .btnFileSelect {
border:2px solid #7EB7FF;
color:#7EB7FF;
height:30px;
line-height:30px;
border-radius:0;
}
.btn.blue {
background-color:#32A1FF;
border-radius:0;
}

.tabSec .titSec {
font-family:"Pretendard Bold";
}

.btnWidgetAdd{
background:#32A1FF;
}

.popup .head{
background:#0A2D3D
}

.tabContWrap.type04 > .tabNav .tabList li.active{
background:#D0E4FF;
color:#000;
border-radius:50px;
}

.tabContWrap.type04 > .tabNav .tabList li{
border-radius:50px;
color:#959595;
background:unset;
}

.section.type02 .titSec strong:before{
background:#7EB7FF;
}

.tabContWrap.type03 > .tabNav .tabList li.active{
border:2px solid #32A1FF;
border-bottom:-1;
border-radius:50px;
box-sizing:border-box;
color:#000;
background:#fff;
}

.tabContWrap.type03 > .tabNav .tabList li{
border-radius:50px;
border:0;
background:unset;
}

.tabContWrap.type03 > .tabNav .tabList li:last-child {
	border-right: 0;
}

.tabContWrap.type03 > .tabNav .tabList li.active:last-child {
	border-right: 2px solid #32A1FF;
}

.popup.sm .head.blue{
background:#0A2D3D;
}

.tabContWrap.type06 .tabCont{
	border:0;
}
#header .menu li .dep2{
padding:10px;
}
#header .menu li.tocToggle > .tool{
    background-position: center top 17px;
}
.tabContWrap.type03 > .tabNav .tabList li:last-child{
border-right:0;
} 
.btnTip {
    margin-left: 5px;
}
.btn.btnOptAdd{
height:40px;
}
#header .menu li.webMap .dep2 .tool.type01{
	background-image: url(../images/header/ico-tool-new_map.png);
}
#header .menu li.webMap .dep2 .tool.type01:hover{
	background-image: url(../images/header/ico-tool-new_map-active.png);
}
#header .menu li.webMap .dep2 .tool.type02 {
	background-image: url(../images/header/ico-header-seach.png);
}
#header .menu li.webMap .dep2 .tool.type02:hover {
background-image: url(../images/header/ico-header-seach_on.png);
}
#versionInfoModalVue .tabContWrap .infoTxt span{
	white-space: pre;
	font-size:14px;
}
#versionInfoModalVue .tabContWrap .infoTxt{
	text-align: center;
}
#header .menu li.help > .tool {
    background-image: url(../images/header/ico-header-help.png);    
}
#header .menu li.help > .tool:hover{
    background-image: url(../images/header/ico-header-help_on.png);
}
#header .menu li.versionInfo > .tool {
    background-image: url(../images/header/ico-header-version.png);
}
#header .menu li.versionInfo > .tool:hover{
    background-image: url(../images/header/ico-header-version_on.png);
}
#header .menu li.logout > .tool {
    background-image: url(../images/header/ico-tool-logout.png);
}
#header .menu li.logout > .tool:hover{
    background-image: url(../images/header/ico-tool-logout-active.png);
}
.errorMsg{
	color: #fc3e3e;
    display: block;
    margin-top: 5px;
    font-size: 14px;
}
/*
 ---[내지도]----
*/
#webMapSearchModal .tabContWrap .titSec strong{
	    position: relative;
    font-size: 18px;
    color: #2e2e2e;
    text-indent: 10px;
    font-family: "Pretendard Bold";
    letter-spacing: -1px;
    padding-left: 10px;
}
#webMapSearchModal .tabContWrap .titSec strong:before {
    position: absolute;
    left: 0;
    top: 6px;
    width: 3px;
    height: 14px;
    background: #d6d6d6;
    display: block;
    content: '';
}
/* ---내지도--- */
.textAreaTr .errorMsg{
	margin-bottom: 5px;
	height:16px;
}
#detailSetting .errorMsg{
	font-size:11px;
}
.editBox.textarea.active{ 
	height: 100%;
}
.editBox textarea{
	height: 100% !important;
}
.textAreaTr{
	height: 90px;
}
.editBox.input.active{
	height: 100%
}
 .textAreaTr .pos-r, .pos-r.layerDcBox, .pos-r.appDcBox{
	min-height: 45px;
}  
#userMapSjEditBox, #userAppSjEditBox{
	width: 500px !important;
}
.popup .cont .inner {
    padding: 20px;
    max-height: 640px !important;
}
.select_sort_category{
	width: 130px;
	margin-right: 5px;
	height: 30px;
}
.select_sort{
	width: 110px;
	margin-right: 5px;
	height: 30px;
}
.cardList .card .txtArea .info{
	margin-bottom: 5px;
}
.cardList .card .txtArea .inner .desc{
	margin-bottom: 10px;
}
#webMapSearchModal .resultArea {
    height: 85px;
}
#webMapSearchModal .errorMsg {
    height: 15px;
    margin-bottom: 5px;
}
.sortBox{
	float: right;
}
.thisAddr .addrItem .coordTag {
    display: inline-block;
    width: 55px;
    border-radius: 3px;
    text-align: center;
    color: #fff;
    font-size: 12px;
    background : #ff7200;
}
/* line 1101, scss/common.scss */
.pagination .pageNum:hover, .pagination .pageNum.active{
	color: #2f5597;
	font-family: "Pretendard Bold";
}