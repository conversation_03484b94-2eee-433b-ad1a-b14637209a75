@charset "UTF-8";
.cctvControl_cctvBtn{
	background-image: url(../../images/widget/widget-CCTV-gray.png);
    min-height: 41px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.cctvControl_cctvBtn:hover{
	background-image: url(../../images/widget/widget-CCTV-white.png);
	color:#fff;
}
.cctvControl_cctvGrpDiv{
	position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    word-break: keep-all;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
.cctvControl_exBtn{
	width: 45px;
	background-image: url(../../images/widget/widget-CCTV-highway-gray.png);
	background-position: center 10px;
	 background-repeat: no-repeat;
}
.cctvControl_itsBtn{
	width: 45px;
	background-image: url(../../images/widget/widget-CCTV-road-gray.png);
	background-position: center 10px;
	 background-repeat: no-repeat;
}
.cctvControl_clearBtn{
	width: 45px;
	background-image: url(../../images/widget/widget-CCTV-clear-gray.png);
	background-position: center 8px;
	 background-repeat: no-repeat;
}
.cctvControl_exBtn:hover{
	background-image: url(../../images/widget/widget-CCTV-highway.png);

}
.cctvControl_itsBtn:hover{
	background-image: url(../../images/widget/widget-CCTV-road.png);
}
.cctvControl_clearBtn:hover{
	background-image: url(../../images/widget/widget-CCTV-clear-blue.png);
}
.cctvControl_headerCloseBtn {
	width: 20px;
    height: 20px;
    background: url(../../images/popup/ico-pop-close.png) no-repeat center;
    margin-right: 10px;
}
.cctvControl_cctvHeader{
	background: linear-gradient(to right, #2F5597, #8FAADC);
	display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
}
.cctvControl_headerCloseSpan{
	display:none;
}