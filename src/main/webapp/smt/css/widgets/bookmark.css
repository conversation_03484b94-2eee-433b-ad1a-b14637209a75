@charset "UTF-8";
.bookmark_btn{
	background-image: url(../../images/widget/widget-bookmark-gray.png);
    min-height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.bookmark_btn:hover{
	background-image: url(../../images/widget/widget-bookmark-white.png);
	color: #fff;
}

.bookmarkDiv .bookMarkControl_bookmarkDeleteSpan{
    top: 8px
}
.bookmarkDiv .bookMarkControl_errorTxt {
	position: absolute;
	top:50px;
}
.bookmarkDiv .bookMarkControl_addBookMarkSpan{
	top : 10px;
} 
.bookMarkControl_rowDiv {
    padding: 10px 10px 10px 10px;
}

.bookmarkDiv .bookMarkControl_bookMarkInput{
    width: 200px !important;
    margin-right: 6px !important;;
    margin-bottom:5px;
}
.bookmarkDiv .bookMarkControl_addBtn{
	/* background-color: #2F5597; */
}
.bookmarkDiv .bookMarkControl_bookMarkControlContent{
	width: 266px;	
	position: absolute;
    right: 65px;
}
.bookmarkDiv .bookMarkControl_addBookMarkSpan{
	font-family: "Pretendard";
	font-size: 15px;
	position: absolute;
    top: -17px;
}
.bookmarkDiv .bookMarkControl_bookmarkDeleteSpan{
	font-size: 15px;
	position: absolute;
    top: -17px;
}
.bookmarkDiv .bookMarkControl_addBtn{
	/* width: 63px; */
}
.bookmarkDiv .bookMarkControl_bookmarkDeleteBtn{
	/* width: 63px; */
}

.bookmarkDiv .bookMarkControl_bookmarkNmSpan{
	color: #333;
    margin-left: 10px;
    	top: 21px;
}

.bookmarkDiv .bookMarkControl_bookmarkBtn{
	margin-right: 5px;
    box-sizing: border-box;
    width: 100%;
    font-size: 14px;
    font-family: "Pretendard";
    color: #5d484894;
    border-width: 0px;
    border-style: solid;
    border-width: 1.5px;
    border-color: #dbd5dc;
}
.bookmarkDiv{
	position: absolute;
	top: 67px;
	right: 20px;
}
.bookmarkDiv.off {
	display:none;
}
