@charset "UTF-8";

/* line 945, scss/common.scss */
#toc i.ico-multi-line {
  display: block;
	background-image: url(../../images/toc/ico-layer-line.png);
}

/* line 946, scss/common.scss */
#toc i.ico-multi-point {
  display: block;
	background-image: url(../../images/toc/ico-layer-dot.png);
}

/* line 947, scss/common.scss */
#toc i.ico-multi-polygon {
  display: block;
	background-image: url(../../images/toc/ico-layer-plane.png);
}

/* line 948, scss/common.scss */
#toc i.ico-hitmap {
  display: block;
	background-image: url(../../images/ico/ico-layer-hitmap.png);
}    

#toc i.ico-geoTiff {
  display: block;
	background-image: url(../../images/ico/ico-layer-g.png);
}
#toc i.ico-layerGroup {
  display: block;
	background-image: url(../../images/ico/ico-layer-web2.png);
}
#toc i.ico-webLayer {
  display: block;
	background-image: url(../../images/ico/ico-layer-web.png);
}
#toc i.ico-layerType-point{
  display: block;
	background-image: url(../../images/widget/ico/ico-layer-dot.png);
}
#toc i.ico-layerType-line{
  display: block;
	background-image: url(../../images/widget/ico/ico-layer-line.png);
}
#toc i.ico-layerType-polygon{
  display: block;
	background-image: url(../../images/widget/ico/ico-layer-plane.png);
}
#toc i.ico-customImage{
  display: block;
}
#toc i{
	background-size: contain;
}
    
#confirmPop{
	z-index : 99999999999999;	
}
    
/* toc 위젯 */
/* line 5, scss/toc.scss */

#toc {
	display: none;
	height: 100%;
	z-index: 10;
}

/* line 6, scss/toc.scss */
#toc.active {
	display: flex;
}

/* line 8, scss/toc.scss */
#toc.active .toc .inner{
/* 	width:auto; */
	height: 100%;
}

/* line 9, scss/toc.scss */
#toc.active .toc .head {
	height: 50px;
}

/* line 11, scss/toc.scss */
#toc.active .toc.hide .inner {
	width: 50px;
	height: 100%;
}

/* line 12, scss/toc.scss */
#toc.active .toc.hide .head {
	visibility: visible;
}

/* line 16, scss/toc.scss */
#toc .toc {
	height: 100%;
	box-shadow: 1.5px 2.6px 4px 0 rgba(0, 0, 0, 0.35);
}


/* line 16, scss/toc.scss */
#toc .toc.dep2 {
	display:none;
}
#toc .toc.dep2.active {
	display:block;
}


/* line 17, scss/toc.scss */
#toc .toc .inner {
	width: 0;
	height: 0;
}

/* line 18, scss/toc.scss */
#toc .toc .head {
	overflow: hidden;
	display: flex;
	justify-content: space-between;
	height: 0;
	transition: .4s;
}

/* line 19, scss/toc.scss */
#toc .toc .head .titBox {
	display: flex;
	align-items: center;
}

/* line 20, scss/toc.scss */
#toc .toc .head .titToc {
    line-height: 100%;
    margin-top: 15px;
    margin-bottom: 15px;
    text-overflow: ellipsis;
	margin-left: 20px;
	color:#fff;
	font-size: 19px;
	font-family: "Pretendard";
	font-weight: normal;
}

/* line 21, scss/toc.scss */
#toc .toc .head .btnGroup {
	display: flex;
	align-items: center;
	margin-right: 20px;
}

/* line 22, scss/toc.scss */
#toc .toc .head .btnTocClose {
	margin-left: 30px;
	width: 20px;
	height: 20px;
	background: url("../../images/toc/ico-toc-close.png") no-repeat center;
}

/* line 23, scss/toc.scss */
#toc .toc .head .btnTocHide {
	width: 14px;
	height: 26px;
	background: url("../../images/toc/ico-toc-hide_white.png") no-repeat center;
/* 	background: url("../../images/toc/ico-toc-show.png") no-repeat center; */
}

/* line 25, scss/toc.scss */
#toc .toc .cont {
	max-height: calc(100% - 50px);
	min-height: calc(100% - 110px);
}

/* line 26, scss/toc.scss */
#toc .toc .cont .odf_toc {
	width: auto;
	padding: 9px 11px;
}

/* line 27, scss/toc.scss */
#toc .toc .cont .inner.type02 {
	padding: 20px;
}

/* line 31, scss/toc.scss */
#toc .toc.dep1 .head {
	background:#7EB7FF;
}


/* line 32, scss/toc.scss */
#toc .toc.dep1 .inner {
	background:#fff;
	padding: 0px;
}

/* line 35, scss/toc.scss */
#toc .toc.dep2 .head {
	background:#D0E4FF;
}

/* line 36, scss/toc.scss */
#toc .toc.dep2 .inner {
	background: #fff;
}

/* line 39, scss/toc.scss */
#toc .toc.hide .inner {
	width: 0;
	height: 0;
}

/* line 40, scss/toc.scss */
#toc .toc.hide .head {
	visibility: hidden;
	height: 100%;
	justify-content: flex-end;
	flex-direction: column-reverse;
}

/* line 41, scss/toc.scss */
#toc .toc.hide .head .titToc {
	line-height: 50px;
	writing-mode: vertical-lr;
	margin-left: 0;
	margin-top: 15px;
}

/* line 42, scss/toc.scss */
#toc .toc.hide .head .btnGroup {
	margin: 19px 0 0 0;
	flex-direction: column-reverse;
}

/* line 43, scss/toc.scss */
#toc .toc.hide .head .btnGroup .btnTocHide {
	transform: rotate(180deg);
}

/* line 44, scss/toc.scss */
#toc .toc.hide .head .btnGroup .btnTocClose {
	margin: 0 0 19px 0;
}

/* line 47, scss/toc.scss */
#toc .toc.hide .cont {
	display: none;
}

/* line 49, scss/toc.scss */
#toc .toc .toc_tocTool {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding : 10px;
	background:#EBF5FC;
}

/* line 50, scss/toc.scss */
#toc .toc .toc_tocTool .toc_btnGroup {
	margin-left:0;
	display: flex;
}    

/* line 52, scss/toc.scss */
#toc .toc .toc_tocTool .toc_btnGroup:last-of-type button {
	width: 27px;
	height: 25px;
	margin: 0;
}

/* line 53, scss/toc.scss */
#toc .toc .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllView.toc_off {
	margin-right: 2px;
	background-image: url("../../images/toc/ico-group-view-show.png");
}
#toc .toc .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllView{
	background-image: url("../../images/toc/ico-group-view-hide.png");
}
#toc .toc .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllView.toc_off:hover{
	background-image: url("../../images/toc/ico-group-view-hide.png");
}

/* line 54, scss/toc.scss */
#toc .toc .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllView:hover, #toc .toc .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllView.active{
	background-image: url("../../images/toc/ico-group-view-show.png");
}

/* line 56, scss/toc.scss */
#toc .toc .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllRemove {
	background-image: url("../../images/toc/ico-group-remove-hide.png");
}

/* line 57, scss/toc.scss */
#toc .toc .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllRemove:hover {
	background-image: url("../../images/toc/ico-group-remove-show.png");
}

/* line 62, scss/toc.scss */
#toc .toc .toc_tocTool button {
	text-indent: 16px;
	font-family: 'Pretendard Bold';
 	margin-right: 14px;
    color: #417AC5;
    font-size: 15px;
    background-repeat:no-repeat;
    background-position:center;
}

/* line 63, scss/toc.scss */
#toc .toc .toc_tocTool button.toc_btnTocAdd {
	background: url("../../images/toc/ico-toctool-add.png") no-repeat left center;
}

/* line 64, scss/toc.scss */
#toc .toc .toc_tocTool button.toc_btnTocSearch {
	background: url("../../images/toc/ico-toctool-search.png") no-repeat left center;
}

/* line 65, scss/toc.scss */
#toc .toc .toc_tocTool button.toc_btnTocUpload {
	margin-right: 0;
	background: url("../../images/toc/ico-toctool-upload.png") no-repeat left center;
}

/* line 68, scss/toc.scss */
.toc_tocContentList {
	margin-top: 10px;
}
#toc .rstcustom__node{
	margin-bottom:2px;
}
#toc .rstcustom__rowWrapper {
	padding: 0 2px 0 0;
}
#toc .rstcustom__node .toc_children{
	
}
#toc .toc_children.toc_depth1 .rstcustom__rowContents{
	background-color:#f1f1f1;
	margin-bottom: 10px
}
#toc .toc_children.toc_depth2 .rstcustom__rowContents{
	background-color:#e9e9e9;
	margin-bottom: 10px
}
#toc .toc_parent{
	
}
#toc .toc_children{
	margin-left: 20px;
}
#toc .rstcustom__rowContents{
	
}
#toc .rstcustom__nodeContent{
	padding-left :2px !important;
}
/* line 69, scss/toc.scss */
#toc .toc .groupList .groupBox {
	margin-bottom: 10px;
	padding: 20px 10px 20px 20px;
	background: #fff;
	border: 1px solid #eef1f8;
	border-radius: 4px;
	cursor: pointer;
}


/* line 70, scss/toc.scss */
#toc .toc .groupList .groupBox:last-of-type {
	margin-bottom: 0px;
}


/* line 71, scss/toc.scss */
#toc .toc .groupList .groupBox .row {
	position: relative;
	display: flex;
	justify-content: space-between;
}

/* line 72, scss/toc.scss */
#toc .toc .groupList .groupBox .titGroup {
	display: block;
	padding-left: 25px;
	font-size: 16px;
	font-family: 'Pretendard Bold';
	max-width: 185px;
	color: #555;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	line-height: 20px;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	background: url("../../images/toc/ico-group-hide.png") no-repeat left 2px;
}

#toc .rstcustom__rowTitle div div{
/* 	width: 15px !important;
	height: 15px !important; */
}


/* line 74, scss/toc.scss */
#toc .toc .groupList .groupBox .toc_btnGroup {
	display: flex;
}

/* line 75, scss/toc.scss */
#toc .toc .groupList .groupBox .toc_btnGroup button {
	width: 23px;
	height: 23px;
}

/* line 76, scss/toc.scss */
#toc .toc .groupList .groupBox .btnGroup button.btnGroupView {
	margin-right: 6px;
	background-image: url("../../images/toc/ico-group-view-hide.png");
}


#toc .toc_btnLayerView {
	background-image: url("../../images/toc/ico-view.png");
}

/* line 77, scss/toc.scss */
#toc .toc_btnLayerView.on {
	background-image: url("../../images/toc/ico-view-hover.png");
}

#toc .toc_groupContent .toc_btnLayerView{
	background-image: url("../../images/layer/ico-group-view-hide.png");
}
#toc .toc_groupContent .toc_btnLayerView.on{
	background-image: url("../../images/layer/ico-group-view-show.png");
}
/* line 79, scss/toc.scss */
#toc .toc .groupList .groupBox .btnGroup button.btnGroupRemove {
	background-image: url("../../images/toc/ico-group-remove-hide.png");
}
#toc .toc_btnLayerDelete {
	background-image: url("../../images/toc/ico-group-remove-hide.png");
}
#toc .toc_btnLayerDelete:hover, .toc_btnLayerDelete.on  {
	background-image: url("../../images/toc/ico-group-remove-show.png");
}
/* line 80, scss/toc.scss */
#toc .toc .groupList .groupBox .btnGroup button.btnGroupRemove:hover, #toc .toc .groupList .groupBox .btnGroup button.btnGroupRemove.active{
	background-image: url("../../images/toc/ico-group-remove-show.png");
}

/* line 82, scss/toc.scss */
#toc .toc .groupList .groupBox .btnGroup button.btnGroupEdit{
	margin-right: 6px;
	background-image: url("../../images/toc/ico-group-edit.png");
}

/* line 83, scss/toc.scss */
#toc .toc .groupList .groupBox .btnGroup button.btnGroupEdit:hover, #toc .toc .groupList .groupBox .btnGroup button.btnGroupEdit.active {
	background-image: url("../../images/toc/ico-group-edit-hover.png");
}

/* line 88, scss/toc.scss */
#toc .toc .groupList .groupBox.active .titGroup{
	color: #1a1a1a;
	background: url("../../images/toc/ico-group-show.png") no-repeat left 2px;
}

/* line 89, scss/toc.scss */
#toc .toc .groupList .groupBox.active .layerList {
	display: block;
}

/* line 93, scss/toc.scss */
#toc .toc .groupList .groupBox .layerList .layerBox:last-of-type{
	border-bottom: 0;
}

/* line 98, scss/toc.scss */
#toc .toc .layerList {
	display: none;
	margin-top: 12px;
}

/* line 99, scss/toc.scss */
#toc .toc .layerList .layerBox{
	padding: 0;
	cursor: pointer;
	border: 0;
	border-bottom: 1px solid #eef1f8;
}

/* line 101, scss/toc.scss */
#toc .toc .layerBox{
	padding: 8px 10px 10px;
	background: #fff;
	border: 1px solid #eef1f8;
	border-radius: 4px;
}

/* line 102, scss/toc.scss */
#toc .toc .layerBox .inner{
	position: relative;
	padding: 10px;
	background: #fff;
}

/* line 103, scss/toc.scss */
#toc .toc .layerBox .editBox{
	top: 10px;
	transform: translate(0);
	justify-content: center;
}

/* line 105, scss/toc.scss */
#toc .toc .layerBox:hover .inner{
	background: #f6f9fe;
}

/* line 106, scss/toc.scss */
#toc .toc .layerBox:hover .editBox{
	background: #f6f9fe;
}

/* line 108, scss/toc.scss */
#toc .toc .layerBox .tit{
	display: flex;
	font-size: 16px;
	font-family: 'Pretendard';
	font-weight: normal;
}

/* line 109, scss/toc.scss */
#toc .toc .layerBox .tit i.ico{
	display: block;
	width: 13px;
	height: 13px;
	margin-right: 0;
	margin-top: 4px;
	background-position: center;
}

/* line 110, scss/toc.scss */
#toc .toc .layerBox .tit i.ico.ico-dot{
	background-image: url("../../images/toc/ico-layer-dot.png");
}

/* line 111, scss/toc.scss */
#toc .toc .layerBox .tit i.ico.ico-line{
	background-image: url("../../images/toc/ico-layer-line.png");
}

/* line 112, scss/toc.scss */
#toc .toc .layerBox .tit i.ico.ico-plane{
	background-image: url("../../images/toc/ico-layer-plane.png");
}

/* line 113, scss/toc.scss */
#toc .toc .layerBox .tit i.ico.ico-hitman{
	background-image: url("../../images/toc/ico-layer-hitman.png");
}

/* line 115, scss/toc.scss */
#toc .toc .layerBox .tit p{
	flex: 1;
	padding-right: 5px;
	margin-left: 5px;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	line-height: 20px;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
}

/* line 118, scss/toc.scss */
#toc .toc .layerBox .btnGroup{
	display: flex;
	margin-top: 25px;
	justify-content: flex-end;
}

/* line 119, scss/toc.scss */
#toc .toc .layerBox .btnGroup button{
	width: 23px;
	height: 23px;
	margin-right: 5px;
	background-position: center;
	background-repeat: no-repeat;
}


#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button {
		width: 30px;
		height: 30px;
    background-position: center;
    background-repeat: no-repeat;
    background-color:#fff;
	border:1px solid #E0E0E0;
}

/* line 120, scss/toc.scss */
#toc .toc .layerBox .btnGroup button.btnLayerEdit{
	background-image: url("../../images/toc/ico-edit.png");
}

/* line 121, scss/toc.scss */
#toc .toc .layerBox .btnGroup button.btnLayerEdit:hover, #toc .toc .layerBox .btnGroup button.btnLayerEdit.active{
	background-image: url("../../images/toc/ico-edit-hover.png");
}

/* line 123, scss/toc.scss */
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLayerDelete {
	background-image: url("../../images/toc/ico-remove.png");
	background-position: center;
}

/* line 124, scss/toc.scss */
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton >  button.toc_btnLayerDelete:hover, 
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton >  button.toc_btnLayerDelete.on {
	background-image: url("../../images/toc/ico-remove-hover.png");
	background-position: center;
}
/* line 127, scss/toc.scss */
.toc_btnLayerView.on 
{
	background-image: url("../../images/layer/ico-view-hover.png");
	background-position: center;
}

/* line 129, scss/toc.scss */
#toc .toc .layerBox .btnGroup button.btnLayerBookmark{
	background-image: url("../../images/toc/ico-layer-bookmark.png");
	background-position: center;
}

/* line 126, scss/toc.scss */
/* #toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLayerView {
	background-image: url("../../images/toc/ico-view.png");
	background-position: center;
} */


/* line 130, scss/toc.scss */
#toc .toc .layerBox .btnGroup button.btnLayerBookmark:hover, #toc .toc .layerBox .btnGroup button.btnLayerBookmark.active{
	background-image: url("../../images/toc/ico-layer-bookmark-hover.png");
	background-position: center;
}

/* line 132, scss/toc.scss */
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLayerGrid {
	background-image: url("../../images/toc/ico-option.png");
	background-position: center;
}

/* line 133, scss/toc.scss */
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLayerGrid:hover,
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLayerGrid.on {
	background-image: url("../../images/toc/ico-option-hover.png");
	background-position: center;
}

/* line 135, scss/toc.scss */
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLayerDetail{
	margin-right: 0;
	background-image: url("../../images/toc/ico-layer-more.png");
	background-position: center;
}

/* line 136, scss/toc.scss */
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLayerDetail:hover, 
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLayerDetail.active {
	background-image: url("../../images/toc/ico-layer-more-hover.png");
	background-position: center;
}


#toc .toc_hidden ,  .rstcustom__toolbarButton > button > span{
	display: block;
	margin: 0;
	padding: 0;
	overflow: hidden;
	font-size: 0;
	line-height: 0;
}

#toc .toc_hidden ,  .rstcustom__toolbarButton > button{
	width:27px;
	height:27px;
	background-repeat:no-repeat;
	background-color: #EBF5FC;
	background-position: center;
}
 
.odf_toc {
	float : none;
	background-color : inherit;
}

#toc .toc_groupContent .rstcustom__rowTitle{
    display: block;
    margin-left: 15px;
    padding-left: 16px !important;
    font-size: 16px;
    font-family: 'Pretendard Bold';
    max-width: 185px;
    color: #555;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
}

/* .toc_layerContent .rstcustom__rowTitle div { */
/* 	display: block; */
/*     font-size: 16px; */
/*     font-family: 'Pretendard'; */
/*     font-weight: bold; */
/*     max-width: 185px; */
/*     color: #555; */
/*     -webkit-line-clamp: 2; */
/*     -webkit-box-orient: vertical; */
/*     line-height: 20px; */
/*     overflow: hidden; */
/*     text-overflow: ellipsis; */
/*     word-break: break-all; */
/* 	height: 20px; */
/* } */

#toc .toc_layerContent .rstcustom__rowTitle div span:nth-child(1){
	float: left;
	margin: 0px 5px 0px 5px;
}
#toc .toc_layerContent .rstcustom__rowContents{
	width : 100%;
	align-items: start;
	display: block;
	background:#f7f7f7;
	border:1px dashed #ccc!important;
}

#toc .toc_layerContent .rstcustom__rowTitle{
    margin-left: 7px;
    padding-left: 7px !important;
}

#toc .rstcustom__collapseButton:before {
	content : url("../../images/toc/ico-group-show.png");
	
}
#toc .rstcustom__expandButton:before {
    content: url("../../images/toc/ico-group-hide.png");
}
#toc .rstcustom__rowContents {
	box-shadow : none;
    border: 0 solid #eef1f8;
}
#toc .toc_layerContent .rstcustom__toolbarButton{
	display: flex;
    margin-top: 25px;
    justify-content: flex-end;
}
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLabelView{
	background-image: url("../../images/toc/ico-bookmark-hover.png");
}
#toc .toc_layerContent > .rstcustom__rowContents > .rstcustom__rowToolbar > .rstcustom__toolbarButton > button.toc_btnLabelView.off{
	background-image: url("../../images/toc/ico-bookmark.png");
}
#toc .toc_layerContent .rstcustom__rowToolbar{
	justify-content: flex-end;
    margin-top: -28px;
}

#toc .toc_layerContent .rstcustom__rowTitle .toc_layerText{
    overflow: hidden;
	line-height: 36px;
    width: 261px;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    font-size: 16px;
    font-family: 'Pretendard';
    font-weight: normal;
    height: 30px;
    margin-top: -8px;
}
#toc .toc_groupContent .rstcustom__rowTitle span{
    overflow: hidden;
    line-height: 36px;
    width: 178px;
    white-space: normal;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    font-size: 16px;
    font-family: 'Pretendard';
    font-weight: normal;
    height: 30px;
    margin-top: -8px;
    position: absolute;
    left: 50px;
}


#toc .toc_btnChangeTitle.toc_group{
	background-image: url(../../images/toc/ico-group-edit.png);
}

#toc .toc_btnChangeTitle.toc_layer{
	background-image: url(../../images/toc/ico-edit.png);
}
#toc .toc_groupContent .rstcustom__toolbarButton:nth-child(1) {
    margin-right: 4px;
}
#toc .rstcustom__toolbarButton div{
    left: 40px;
}
#toc .rstcustom__toolbarButton .toc_editBox.toc_layer{
	position: absolute;
}
#toc .toc_tocContentBox{

}
#toc .rstcustom__rowWrapper .toc_children.toc_groupContent.toc_depth1{
	margin-left: 20px;
}
#toc .rstcustom__rowWrapper .toc_children.toc_layerContent.toc_depth2{
	margin-left: 40px;
}


/*레이어 상세 영역 css*/
#toc.active .toc #layerDetailWidget.inner{
	width: auto;
	padding: 9px 11px;
}

/*스타일 위젯 css*/
#toc .toc.dep1 .inner, #analysis .toc.dep1 .inner, #detailSetting .toc.dep1 .inner, #library .toc.dep1 .inner{
	background:#fff;
}
#toc .toc.dep1 .head .btnTocHide{
	
}

.toc_children.toc_groupContent .rstcustom__rowContents .rstcustom__rowToolbar button{
border:1px solid #aebdf3;
background-color:#fff;
}
.toc_children.toc_groupContent .rstcustom__rowContents{
background-color:#ebf5fc!important;
}

#toc .toc.dep2 .head .btnTocHide{
background: url(../../images/toc/ico-toc-hide_dark.png) no-repeat center;
}
#toc .toc_editBox.toc_layer label.toc_hidden{
	display: none;
} 