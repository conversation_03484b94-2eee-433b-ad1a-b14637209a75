@charset "UTF-8";
/*스와이퍼 위젯 css*/
/* line 2479, scss/common.scss */
.swiper_SwiperControlContent .flex {
    padding-bottom: 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid #e9e9e9;
}

/* line 2480, scss/common.scss */
.swiper_SwiperControlContent .flex label {
    position: relative;
    font-size: 18px;
    color: #2e2e2e;
    text-indent: 10px;
    font-family: "Pretendard Bold";
    margin-right: 20px;
}

/* line 2481, scss/common.scss */
.swiper_SwiperControlContent .flex label:before {
    position: absolute;
    left: 0;
    top: 6px;
    width: 3px;
    height: 14px;
    background: #d6d6d6;
    display: block;
    content: '';
}

/* line 2483, scss/common.scss */
.swiper_SwiperControlContent .flex input[type="number"] {
    width: 40px;
    height: 28px;
    padding: 0;
    text-align: center;
}

/* line 2485, scss/common.scss */
.swiper_SwiperControlContent .swiper_swiperBar {
    margin-right: 10px;
    flex: 1;
    -webkit-appearance: none;
    outline: none;
    height: 2px;
    border-radius: 4px;
    background: #e1e1e1;
}

/* line 2488, scss/common.scss */
.swiper_SwiperControlContent .swiper_swiperBar::-webkit-slider-thumb {
    -webkit-appearance: none;
}

/* line 2489, scss/common.scss */
.swiper_SwiperControlContent .swiper_swiperBar::-ms-track {
    width: 100%;
    cursor: pointer;
    background: transparent;
    /* border-color: transparent;
	color: transparent; */
}

/* line 2490, scss/common.scss */
.swiper_SwiperControlContent .swiper_swiperBar::-webkit-slider-thumb {
    -webkit-appearance: none;
    background: #555;
    cursor: pointer;
    height: 10px;
    width: 10px;
    box-shadow: 1px 1px 1px #000000, 0px 0px 1px #0d0d0d;
    border-radius: 50%;
}

/* line 2501, scss/common.scss */
.swiper_headerDiv {
    display: flex;
}

/* line 2502, scss/common.scss */
.swiper_headerDiv>div {
    flex: 1;
    margin-right: 20px;
}

/* line 2503, scss/common.scss */
.swiper_headerDiv>div>div>span {
    display: block;
    font-size: 16px;
    font-family: '맑은 고딕';
    font-weight: bold;
    margin-bottom: 10px;
    color: #555;
}

/* line 2504, scss/common.scss */
.swiper_headerDiv>div:last-of-type {
    margin-right: 0;
}

/* line 2505, scss/common.scss */
.swiper_headerDiv>div select {
    width: 100%;
}

/* line 2508, scss/common.scss */
.swiper_footerDiv {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

/* line 2509, scss/common.scss */
.swiper_footerDiv button {
    width: 67px;
    height: 40px;
    color: #fff;
    font-size: 14px;
    font-family: 'Pretendard';
    transition: .4s;
    border-radius: 4px;
    margin-right: 5px;
}

/* line 2510, scss/common.scss */
.swiper_footerDiv button:last-of-type {
    margin-right: 0;
}

/* line 2511, scss/common.scss */
.swiper_footerDiv button.swiper_createBtn {
    background-color: #2f5597;
}

/* line 2512, scss/common.scss */
.swiper_footerDiv button.swiper_createBtn:hover {
    background-color: #23437b;
}

/* line 2514, scss/common.scss */
.swiper_footerDiv button.swiper_deleteBtn {
    background-color: #555;
}

/* line 2515, scss/common.scss */
.swiper_footerDiv button.swiper_deleteBtn:hover {
    background-color: #333;
}



/*스와이퍼 MINI TOC*/
.swiper_toc .swiper_miniTOCFrame {
    position: absolute;
    left: 10px;
    top: 10px;
}

.swiper_toc .swiper_miniTOCFrame>button {
    background: #fff url(../../images/toc/ico-small-toc-show.png) no-repeat center;
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
}

.swiper_toc .swiper_miniTOCFrame>button.swiper_on {
    background: #fff url(../../images/toc/ico-small-toc-hide.png) no-repeat center;
}

.swiper_toc .swiper_miniTOCFrame>button>span {
    display: none;
}

.swiper_toc .swiper_miniTOCFrame .swiper_tocArea {
    position: absolute;
    left: calc(100% + 3px);
    top: 0px;
    width: 300px;
    overflow-y: hidden;
    transition: .4s;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
}

.swiper_toc .swiper_miniTOCFrame .toc_tocContentList {
    margin-top: 0px;
}

/*분할지도 내 toc header영역*/
.swiper_toc .swiper_tocArea .toc_tocTool {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
}

.swiper_toc .swiper_tocArea .toc_tocTool .toc_btnGroup {
    display: flex;
}

.swiper_toc .swiper_tocArea .toc_tocTool .toc_btnGroup:last-of-type button {
    width: 27px;
    height: 25px;
    margin: 0;
}

.swiper_toc .swiper_tocArea .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllView {
    margin-right: 2px;
    background-image: url(../../images/toc/ico-toc-all-view-hide.png);
}

.swiper_toc .swiper_tocArea .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllView.toc_off {
    background-image: url("../../images/toc/ico-toc-all-view-show.png");
}





/* swiper위젯*/
/*#swiperDiv {*/
/*    position: absolute;*/
/*    top: 65px;*/
/*    right: 71px;*/
/*    display: block;*/
/*    z-index: 1;*/
/*}*/
/*#swiperBtn {*/
/*    background-image: url(../images/widget/widget-swiper-gray.png);*/
/*    min-height: 41px;*/
/*    background-repeat: no-repeat;*/
/*    background-position: center 10px;*/
/*}*/

.swiper_swiperBtn {
    background-image: url(../../images/widget/widget-swiper-gray.png);
    min-height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
}

li.swiperWidget:hover .swiper_swiperBtn,
.swiper_widget>li:hover  .swiper_swiperBtn{
    background-image: url(../../images/widget/widget-swiper-white.png);
    color: #fff;
}

.swiper_SwiperControlContent {}

.swiper_footerDiv button {
    width: 67px;
    height: 40px;
}

.swiper_footerDiv span {
    padding-top: 0;
}

/*분할지도 내 toc title 영역*/
.swiper_toc .swiper_tocArea .toc_layerText {
    overflow-x: clip;
    max-width: 170px;
}

.swiper_toc .swiper_tocArea .toc_children .toc_layerText {
    max-width: 150px;
}

.swiper_toc .swiper_tocArea .rstcustom__rowWrapper div {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: 'Pretendard';
    font-weight: normal;
    color: #333;
    padding-right: 0px;
}

.swiper_toc .toc_tocContentBox {
    max-height: 360px;
    overflow: auto;
}

.swiper_toc .swiper_tocArea .rstcustom__rowTitle {
    padding: 0px 0px 0px 10px;
}

.swiper_toc .swiper_tocArea .rstcustom__nodeContent {
    padding-left: 0px !important;
}

.swiper_toc .swiper_tocArea .toc_children {
    margin-left: 20px !important;
}

/*전체레이어 on/off*/
.swiper_toc .swiper_tocArea .rstcustom__collapseButton:before {
    content: url(../../images/toc/ico-group-show.png);
}

.swiper_toc .swiper_tocArea .rstcustom__expandButton:before {
    content: url(../../images/toc/ico-group-hide.png);
}

/*그룹 title*/
.swiper_toc .swiper_tocArea .toc_groupContent .rstcustom__rowTitle {
    display: block;
    margin-left: 15px;
    padding-left: 16px !important;
    font-family: '맑은 고딕';
    font-weight: bold;
    max-width: 185px;
    color: #555;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
}

/*on/off*/
.swiper_toc .swiper_tocArea .toc_btnLayerView {
    background-position: center;
    background-repeat: no-repeat;
    width: 27px;
    height: 27px;
    margin-top: 3px;
    background-image: url("../../images/toc/ico-group-view-hide.png");
}

.swiper_toc .swiper_tocArea .toc_btnLayerView.on {
    background-image: url("../../images/toc/ico-group-view-show.png");
}

/*레이어 on/off*/
.swiper_toc .swiper_tocArea .toc_layerContent>.rstcustom__rowContents>.rstcustom__rowToolbar>.rstcustom__toolbarButton>button.toc_btnLayerView {
    background-image: url("../../images/toc/ico-view.png");
    width: 23px;
    height: 23px;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #E0E0E0;
    text-align: center;
    margin-top: 7px;
}

.swiper_toc .rstcustom__rowLabel {
    margin-top: 3px;
}

.swiper_toc .toc_expanded {
    margin-top: 3px;
}

.swiper_toc .swiper_tocArea .toc_layerContent>.rstcustom__rowContents>.rstcustom__rowToolbar>.rstcustom__toolbarButton>button.toc_btnLayerView.on {

    background-image: url("../../images/toc/ico-view-hover.png");
}


/* line 945, scss/common.scss */
.swiper_toc .swiper_tocArea i.ico-multi-line {
    display: block;
    background-image: url(../../images/toc/ico-layer-line.png);
}

/* line 946, scss/common.scss */
.swiper_toc .swiper_tocArea i.ico-multi-point {
    display: block;
    background-image: url(../../images/toc/ico-layer-dot.png);
}

/* line 947, scss/common.scss */
.swiper_toc .swiper_tocArea i.ico-multi-polygon {
    display: block;
    background-image: url(../../images/toc/ico-layer-plane.png);
}

/* line 948, scss/common.scss */
.swiper_toc .swiper_tocArea i.ico-hitmap {
    display: block;
    background-image: url(../../images/ico/ico-layer-hitmap.png);
}

.swiper_toc .swiper_tocArea i.ico-geoTiff {
    display: block;
    background-image: url(../../images/ico/ico-layer-g.png);
}

.swiper_toc .swiper_tocArea i.ico-customImage {
    display: block;
}

.swiper_toc .swiper_tocArea i {
    background-size: contain;
}



.swiper_basemapContainer #basemapWidget button {
    height: 30px;
    width: 100%;
}

.swiper_basemapContainer #basemapWidget button>span {
    font-family: 'Pretendard';
    display: block;
    letter-spacing: -1px;
    line-height: 15px;
}




.swiper_miniTOCFrame>button{
    background: #fff url(../../images/toc/ico-small-toc-show.png) no-repeat center;
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
}
.swiper_miniTOCFrame>button>span {
    display:none;
}

.swiper_miniTOCFrame .swiper_tocArea {
    position: absolute;
    left: calc(100% + 3px);
    top: 0px;
    width: 300px;
    overflow-y: hidden;
    transition: .4s;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0.5px 0.9px 4px 0 rgb(0 0 0 / 27%);
}

.swiper_miniTOCFrame .swiper_tocArea .toc_tocTool {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
}
.swiper_miniTOCFrame .swiper_tocArea .toc_tocTool .toc_btnGroup{
    display: flex;
}


.swiper_miniTOCFrame .swiper_tocArea .toc_tocTool {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
}

.swiper_miniTOCFrame .swiper_tocArea .toc_tocTool .toc_btnGroup:last-of-type button.toc_btnAllView.toc_off {
    background-image: url(../../images/toc/ico-toc-all-view-show.png);
}
.swiper_miniTOCFrame .swiper_tocArea .toc_tocTooll .toc_btnGroup:last-of-type button.toc_btnAllView {
    margin-right: 2px;
    background-image: url(../../images/toc/ico-toc-all-view-hide.png);
}
.swiper_miniTOCFrame .swiper_tocArea .toc_tocTool .toc_btnGroup:last-of-type button {
    width: 27px;
    height: 25px;
    margin: 0;
}
swiper_miniTOCFrame .swiper_tocArea .rstcustom__nodeContent {
    padding-left: 0px !important;
}
.swiper_miniTOCFrame .swiper_tocArea .rstcustom__rowWrapper div {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-family: 'Pretendard';
    font-weight: normal;
    color: #333;
    padding-right: 0px;
}
.swiper_miniTOCFrame .swiper_tocArea .toc_layerContent>.rstcustom__rowContents>.rstcustom__rowToolbar>.rstcustom__toolbarButton>button.toc_btnLayerView.on {
    background-image: url(../../images/toc/ico-view-hover.png);
}
.swiper_miniTOCFrame .swiper_tocArea .toc_layerContent>.rstcustom__rowContents>.rstcustom__rowToolbar>.rstcustom__toolbarButton>button.toc_btnLayerView {
    background-image: url(../../images/toc/ico-view.png);
    width: 23px;
    height: 23px;
    background-position: center;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #E0E0E0;
    text-align: center;
    margin-top: 7px;
}
.swiper_miniTOCFrame .swiper_tocArea .toc_layerContent>.rstcustom__rowContents>.rstcustom__rowToolbar>.rstcustom__toolbarButton>button {
    width: 23px;
    height: 23px;
    background-position: 50%;
    background-repeat: no-repeat;
    background-color: #fff;
    border: 1px solid #e0e0e0;
    text-align: center;
    margin-top: 7px;
}
.swiper_miniTOCFrame .swiper_tocArea i {
    background-size: contain;
}

.swiper_miniTOCFrame .swiper_tocArea .toc_tocContentBox {
    padding-top: 0px;
    max-height: 327px;
    overflow-y: 327px;
    overflow: auto;
}
.swiper_miniTOCFrame .swiper_tocArea .toc_tocContentList{
    margin-top: 0px;
}
.swiper_miniTOCFrame .swiper_tocArea .rstcustom__rowLabel {
    margin-top: 3px;
}
.swiper_miniTOCFrame .swiper_tocArea .rstcustom__rowTitle {
    padding: 0px 0px 0px 10px;
}
.swiper_miniTOCFrame .swiper_tocArea .toc_layerText {
    overflow-x: clip;
    max-width: 170px;
}



/*위젯 공통*/
.swiper_widget {
    width: 58px;
    transition: .4s;
    box-shadow: 0 0 4px 0 rgb(0 0 0 / 30%);
}

.swiper_widget>li {
    position: relative;
    border-bottom: 1px solid #eeeeee;
    text-align: center;
    padding: 0 5px;
    font-family: 'Pretendard Bold';
    background: #fff;
}

.swiper_widget>li:hover {
    background-color: #436aeb;
    border-bottom: 1px solid #fff;
}
.swiper_widget>li button>span {
    font-family: 'Pretendard';
    display: block;
    letter-spacing: -1px;
    line-height: 15px;
}
.swiper_widget>li:hover>button.basemap_tool>span{
    color : white;
}



/*스와이퍼 위젯*/
.swiper_widget>li#swiperWidget {
    padding: unset;
}
.swiper_widget>li#swiperWidget button.swiper_swiperBtn>span{
    display: block;
    padding-top: 36px;
    letter-spacing: -1px;
    line-height: 15px;
}
/*스와이퍼 안 배경지도 앱*/
.swiper_widget>li#basemapWidget> .basemap_tool {
    background-image: url(../../images/widget/widget-basemap-gray.png);
    min-height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    color: #555;
}
.swiper_widget>li#basemapWidget>button.basemap_tool {
    height: 30px;
    width: 100%;
}
.swiper_widget>li#basemapWidget>button.basemap_tool>span {
    font-family: 'Pretendard';
    display: block;
    letter-spacing: -1px;
    line-height: 15px;
    padding-top: 36px;
}

.swiper_widget>li#basemapWidget>.basemap_toolbox button.basemap_layer {

    height: 30px;
    width: 100%;
}
