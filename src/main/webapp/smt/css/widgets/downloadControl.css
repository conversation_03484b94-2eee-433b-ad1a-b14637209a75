@charset "UTF-8";
.downloadControl_toolBtn , .downloadControl_downloadPngBtn , .downloadControl_downloadPDFBtn{
	background-image: url(../../images/widget/widget-download-gray.png);
    min-height: 41px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.downloadControl_toolBtn:hover{
	background-image: url(../../images/widget/widget-download-white.png);
    min-height: 41px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.downloadControl_downloadPngBtn:hover , .downloadControl_downloadPDFBtn:hover{
	background-image: url(../../images/widget/widget-download.png);
}
.downloadControl_downloadGrpDiv{
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    word-break: keep-all;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}