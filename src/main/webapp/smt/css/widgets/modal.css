@charset "UTF-8";
/* modal css */

.modal_modal{
  background-color: #e8e8e8;
  display : none;
  position: absolute;
  z-index: 99999999;
  height: auto !important; 
  
}
.modal_modal .modal_cont {
  /* background: #fff; */
}

.modal_popup .modal_cont .modal_inner {
  /* max-height: 650px;
  padding: 30px; */
}

.modal_modal.modal_open{
   display : block;
}

.modal_modal-box-backgCtrl{
  width: 100%;
  height: 100%;
  background-color: slategray;
  z-index: 99999998;
  position: absolute;
  top: 0;
  opacity: 0.6;
}

.modal_modal .modal_head.modal_blue {
  background: #5779FF;
}

.modal_modal .modal_head {
	border-radius: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #0A2D3D;
}

.modal_close{
  align-items: center;
  margin-right: 13px;
  width: 20px;
  height: 20px;
  background: url(../../images/popup/ico-pop-close.png) no-repeat center;
  font-family: inherit;
  font-size: inherit;
  display: inline-block;
  border: none;
  background-color: transparent;
  cursor: pointer;
}
.modal_close span{
  display: none;
}
.modal_content{
  background: #fff;
}
.modal_content > div {
  padding: 30px;
}
.modal_head > span {
    margin-left: 20px;
    line-height: 46px;
    color: #fff;
    font-size: 16px;
    font-family: "Pretendard";
    font-weight: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
