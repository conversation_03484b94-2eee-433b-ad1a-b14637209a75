@charset "UTF-8";

/* line 104, scss/widget.scss */
#widget .dep1 > li.resetWidget > .clearControl_clearControlContent > .clearControl_clearBtn {
	height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    width : 50px;
	background-image: url("../images/widget/widget-clear-gray.png");
}

#widget .dep1 > li.resetWidget > .clearControl_clearControlContent > .clearControl_clearBtn:hover {
	background-image: url("../images/widget/widget-clear-white.png");
}
.fullScreenControlWidget .fullScreenControl_fullScreenBtn{
	height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    width : 50px;
	background-image: url("../images/widget/widget-fullscreen-gray.png");
}
.fullScreenControlWidget .fullScreenControl_fullScreenBtn:hover{
	background-image: url("../images/widget/widget-fullscreen-white.png");
	color: #fff;
}
.currentViewControl_moveCurrentView{
		height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    width : 50px;
	background-image: url("../images/widget/widget-currentposition-gray.png");
}
.currentViewControl_moveCurrentView:hover{
	background-image: url("../images/widget/widget-currentposition-white.png");
}
.roadView_roadViewBtn_off{
	height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    width : 50px;
	background-image: url("../images/widget/widget-roadview-gray.png");
}
.roadView_roadViewBtn_on{
	background-image: url("../images/widget/widget-roadview-white.png");
	background-color: #2f5597;
}
.roadView_roadViewSpan{
	width:52px;
}
.roadView_roadViewBtn_on .roadView_roadViewSpan{
	color : white
}
#widget .dep1 > li.homeWidget > .homeControl_homeControlContent > .homeControl_moveHomeBtn {
	background-image: url("../images/widget/widget-home-gray.png");
}
#widget .dep1 button{
	height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    width : 50px;
}

#widget .dep1 > li.homeWidget > .homeControl_homeControlContent > .homeControl_moveHomeBtn:hover {
	/*background-image: url("../images/toolbar/ico-home-01-active.png");*/
	background-image: url("../images/widget/widget-home-white.png");
}

#widget .dep1 .rotationControl_rotateSpan {
	background-image: url("../images/widget/widget-rotate-gray.png");
	height: 34px;
    background-repeat: no-repeat;
    background-position: center;
    width: 34px;
    padding-top: 0px;
    display: block;
    margin-top: 12px;
    margin-left: 7px;
}
#widget .dep1 .rotationControl_rotateSpan:hover {
	background-image: url("../images/widget/widget-rotate-white.png");
}

#widget .dep1 .moveControl_preBtn{
	background-image: url("../images/widget/이동이전.png");
	width: 23px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
#widget .dep1 .moveControl_preBtn:hover{
	background-image: url("../images/widget/이동이전hover.png");
	color: #fff;
}

#widget .dep1 .moveControl_nextBtn {
	background-image: url("../images/widget/이동이후.png");
	width: 23px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
#widget .dep1 .moveControl_nextBtn:hover{
	background-image: url("../images/widget/이동이후hover.png");
	color: #fff;
}



#widget .dep1 > li.pnuGetterWidget > .pnuGetter_PNUGetterContent > .pnuGetter_searchBtn:hover {
	background-image: url("../images/toolbar/ico-tool-pnu-active.png");
/* 	background-image: url("../images/toolbar/ico-pnu-01-active.png"); */
}
/* line 110, scss/widget.scss */
#widget .dep1 > li.pnuGetterWidget > .pnuGetter_PNUGetterContent > .pnuGetter_searchBtn > .pnuGetter_searchSpan {
	font-size : 12px;
}
#widget .dep1 > li.pnuGetterWidget > .pnuGetter_PNUGetterContent > .pnuGetter_searchBtn {
	background-image: url("../images/toolbar/ico-tool-pnu.png");
	height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    width : 47px;
}

#widget .dep1 > li.estateWidget > .pnuGetter_PNUGetterContent > .pnuGetter_searchBtn:hover {
	background-image: url("../images/toolbar/ico-tool-pnu-active.png");
/* 	background-image: url("../images/toolbar/ico-pnu-01-active.png"); */
}
/* line 110, scss/widget.scss */
#widget .dep1 > li.estateWidget > .pnuGetter_PNUGetterContent > .pnuGetter_searchBtn > .pnuGetter_searchSpan {
	font-size : 12px;
}
#widget .dep1 > li.estateWidget > .pnuGetter_PNUGetterContent > .pnuGetter_searchBtn {
	background-image: url("../images/toolbar/ico-tool-pnu.png");
	height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    width : 47px;
}
#widget .dep1 {
   	border-radius : 3px;
}
/*그리기 도구 위젯*/
.drawControl_toolBtn{
background-image: url(../images/widget/widget-draw-gray.png);
    min-height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.drawControl_toolBtn:hover{
background-image: url(../images/widget/widget-draw-white.png);
}


.drawControl_drawGrpDiv {
	position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    word-break: keep-all;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}

.drawControl_drawGrpDiv > button > span {
	display: block;
    padding-top: 36px;
    letter-spacing: -1px;
    line-height: 15px;
}    
.drawControl_drawGrpDiv > button {
	min-height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
    padding: 0 7px;
    color: #555;
}    
.drawControl_pointBtn{
	background-image: url(../images/toolbar/ico-draw-01.png);
}
.drawControl_pointBtn:hover{
	background-image: url(../images/toolbar/ico-draw-01-active.png);
}
.drawControl_polygonBtn{
	background-image: url(../images/toolbar/ico-draw-05.png);
}
.drawControl_polygonBtn:hover{
	background-image: url(../images/toolbar/ico-draw-05-active.png);
}
.drawControl_boxBtn{
	background-image: url(../images/toolbar/ico-draw-06.png);
}
.drawControl_boxBtn:hover{
	background-image: url(../images/toolbar/ico-draw-06-active.png);
}
.drawControl_lineBtn{
	background-image: url(../images/toolbar/ico-draw-03.png);
}
.drawControl_lineBtn:hover{
	background-image: url(../images/toolbar/ico-draw-03-active.png);
}
.drawControl_textBtn{
	background-image: url(../images/toolbar/ico-draw-02.png);
}
.drawControl_textBtn:hover{
	background-image: url(../images/toolbar/ico-draw-02-active.png);
}
.drawControl_curveBtn{
	background-image: url(../images/toolbar/ico-draw-04.png);
}
.drawControl_curveBtn:hover{
	background-image: url(../images/toolbar/ico-draw-04-active.png);
}
.drawControl_circleBtn{
	background-image: url(../images/toolbar/ico-draw-07.png);
}
.drawControl_circleBtn:hover{
	background-image: url(../images/toolbar/ico-draw-07-active.png);
}
.drawControl_bufferBtn{
	background-image: url(../images/toolbar/ico-draw-08.png);
}
.drawControl_bufferBtn:hover{
	background-image: url(../images/toolbar/ico-draw-08-active.png);
}



/*측정 위젯*/
.measureControl_toolBtn{
background-image: url(../images/widget/widget-measure-gray.png);
    min-height: 67px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.measureControl_toolBtn:hover{
background-image: url(../images/widget/widget-measure-white.png);
}

.measureControl_measureGrpDiv {
	position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    word-break: keep-all;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
.sampleFrame.themeType01.style02 .drawControl_drawGrpDiv{
    left: 68px;
   	width: 271px;
}
.sampleFrame.themeType01.style04 .drawControl_drawGrpDiv{
    left: 68px;
   	width: 271px;
}
.sampleFrame.themeType01.style02 .measureControl_measureGrpDiv{
   left: 68px;
   width: 152.91px;
}
.sampleFrame.themeType01.style04 .measureControl_measureGrpDiv{
   left: 68px;
   width: 152.91px;
}

.measureControl_measureGrpDiv > button > span {
	display: block;
    padding-top: 36px;
    letter-spacing: -1px;
    line-height: 15px;
}    
.measureControl_measureGrpDiv > button {
	min-height: 67px;
    background-repeat: no-repeat;
    padding: 0 7px;
    color: #555;
}    

.measureControl_areaBtn{
	background-image: url(../images/toolbar/ico-measure-02.png);
	    background-position: center 10px;
}

.measureControl_distanceBtn{
	background-image: url(../images/toolbar/ico-measure-01.png);
	    background-position: center 10px;
}
.measureControl_circleBtn{
	    background-position: center 10px;
		background-image: url(../images/toolbar/ico-measure-04.png);
}

.measureControl_spotBtn{
	background-image: url(../images/toolbar/ico-measure-03.png);
	background-position: center 20px;
}

.measureControl_areaBtn:hover{
	background-image: url(../images/toolbar/ico-measure-02-active.png);
}

.measureControl_distanceBtn:hover{
	background-image: url(../images/toolbar/ico-measure-01-active.png);
}
.measureControl_circleBtn:hover{
	background-image: url(../images/toolbar/ico-measure-04-active.png);
}

.measureControl_spotBtn:hover{
	background-image: url(../images/toolbar/ico-measure-03-active.png);
}
/*출력 위젯*/
.printControl_printBtn{
	background-image: url(../images/widget/widget-print-gray.png);
    min-height: 41px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.webmap .printControl_printBtn:hover{
	background-image: url(../images/widget/widget-print-white.png);
}
/*다운로드 위젯  */
.downloadControl_toolBtn , .downloadControl_downloadPngBtn , .downloadControl_downloadPDFBtn{
	background-image: url(../images/widget/widget-download-gray.png);
    min-height: 41px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.downloadControl_toolBtn:hover{
	background-image: url(../images/widget/widget-download-white.png);
    min-height: 41px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.downloadControl_downloadPngBtn:hover , .downloadControl_downloadPDFBtn:hover{
	background-image: url(../images/widget/widget-download.png);
}
.downloadControl_downloadGrpDiv{
    position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    word-break: keep-all;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
/*cctv 위젯*/
.cctvControl_cctvBtn{
	background-image: url(../images/widget/widget-CCTV-gray.png);
    min-height: 41px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
.cctvControl_cctvBtn:hover{
	background-image: url(../images/widget/widget-CCTV-white.png);
	color:#fff;
}
.cctvControl_cctvGrpDiv{
	position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    word-break: keep-all;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
.cctvControl_exBtn{
	width: 45px;
	background-image: url(../images/widget/widget-CCTV-highway-gray.png);
	background-position: center 10px;
	 background-repeat: no-repeat;
}
.cctvControl_itsBtn{
	width: 45px;
	background-image: url(../images/widget/widget-CCTV-road-gray.png);
	background-position: center 10px;
	 background-repeat: no-repeat;
}
.cctvControl_clearBtn{
	width: 45px;
	background-image: url(../images/widget/widget-CCTV-clear-gray.png);
	background-position: center 8px;
	 background-repeat: no-repeat;
}
.cctvControl_exBtn:hover{
	background-image: url(../images/widget/widget-CCTV-highway.png);

}
.cctvControl_itsBtn:hover{
	background-image: url(../images/widget/widget-CCTV-road.png);
}
.cctvControl_clearBtn:hover{
	background-image: url(../images/widget/widget-CCTV-clear-blue.png);
}
.cctvControl_headerCloseBtn {
	width: 20px;
    height: 20px;
    background: url(../images/popup/ico-pop-close.png) no-repeat center;
    margin-right: 10px;
}
.cctvControl_cctvHeader{
	background: linear-gradient(to right, #2F5597, #8FAADC);
	display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 4px;
}
.cctvControl_headerCloseSpan{
	display:none;
}
/* swiper위젯*/
#swiperDiv{
	position: absolute;
    top: 65px;
    right: 71px;
    display:block
}
#swiperBtn{
	background-image: url(../images/widget/widget-swiper-gray.png);
    min-height: 41px;
    background-repeat: no-repeat;
    background-position: center 10px;
}
#swiperBtn:hover{
	background-image: url(../images/widget/widget-swiper-white.png);
	color: #fff;
}
.swiperControl_SwiperControlContent{
	
}
.swiperControl_footerDiv button{
	width: 67px;
    height: 40px;
}
.swiperControl_footerDiv span{
	padding-top : 0;	
}
/* 스와이퍼 위젯 css 커스텀*/
#swiperBorderDiv{
	display:none;
	position: absolute;
    width: 1px;
    height: 100%;
    /* border-right: 2px solid black; */
    background-color:black;
    top: 0%;
    left: 50%
}
.btnResize{
	position: relative;
    left: calc(50% - 9.8px);
    top: calc(50% - 21px);
    width: 20px;
    height: 42px;
    border-radius: 5px;
    background: #fff url(../images/ico/ico-resize.png) no-repeat center;
    border: 1px solid #2f5597;
    box-sizing: border-box;
}
.rotationTooltip {
	display:none;
}
#rotationControlWidget :hover dl{
    width: 90px;
    position: relative;
    left: calc(100% - 160px);
    top: -40px;
    line-height: 21px;
    margin-bottom: 0;
    display: block;
    z-index: 10;
    background: #e4edff;
    color: #555;
    border-radius: 5px;
    padding: 0 8px;
    font-family: 'Pretendard bold';
    font-size: 12px;;
}
.sampleFrame.themeType01.style02 #rotationControlWidget :hover dl{
    width: 90px;
    position: relative;
    left: 57px;
    top: -40px;
    line-height: 21px;
    margin-bottom: 0;
    display: block;
    z-index: 10;
    background: #e4edff;
    color: #555;
    border-radius: 5px;
    padding: 0 8px;
    font-family: 'Pretendard bold';
    font-size: 12px;;
}
.sampleFrame.themeType01.style04 #rotationControlWidget :hover dl{
    width: 90px;
    position: relative;
    left: 57px;
    top: -40px;
    line-height: 21px;
    margin-bottom: 0;
    display: block;
    z-index: 10;
    background: #e4edff;
    color: #555;
    border-radius: 5px;
    padding: 0 8px;
    font-family: 'Pretendard bold';
    font-size: 12px;;
}
.sampleFrame.themeType01.style02 #bookMarkControlWidget_top{
	right:329px;
}
.sampleFrame.themeType01.style04 #bookMarkControlWidget_top{
	right:329px;
}
.sampleFrame.themeType01.style02 .timeSliderControlWidget_top{
    top: 53px;
    position: absolute;
    width: 28%;
    right: 103px !important;
    left : auto;
}
.sampleFrame.themeType01.style04 .timeSliderControlWidget_top{
    top: 53px;
    position: absolute;
    width: 28%;
    right: 103px !important;
    left : auto;
}
.sampleFrame.themeType01.style02 .swiperControlWidget_top {
    position: absolute;
    top: 59px;
    right: 246px !important;
    left : auto;
}
.sampleFrame.themeType01.style04 .swiperControlWidget_top {
    position: absolute;
    top: 59px;
    right: 246px !important;
    left : auto;
}
.sampleFrame .bookMarkControl_rowDiv {
    margin-bottom: 8px;	
}
.basemap_group {
	width: 65px;
}
.dipImg{
    width: 113px;
}
#rotationControlWidget{
	height: 57px;
}

/* line 228, scss/widget.scss */
#widget .zoomControl_sliderDiv:hover .tooltip {
	display: block;
}

/* line 230, scss/widget.scss */
#widget .tooltip {
	display: none;
	position: absolute;
	left: -53px;
	top: 0;
}

/* line 231, scss/widget.scss */
#widget .tooltip li {
	position: absolute;
	left: 0;
	color: #fff;
	font-size: 10px;
	font-family: 'Pretendard';
	width: 45px;
	text-align: center;
	height: 17px;
	line-height: 17px;
	background: #474747;
}

/* line 232, scss/widget.scss */
#widget .tooltip li:first-of-type {
	top: 16px;
}

/* line 233, scss/widget.scss */
#widget .tooltip li:nth-of-type(2) {
	top: 50px;
}

/* line 234, scss/widget.scss */
#widget .tooltip li:last-of-type {
	left: 10px;
	top: 85px;
	width: 35px;
}

/* line 235, scss/widget.scss */
#widget .tooltip li:after {
	position: absolute;
	right: -2px;
	top: 50%;
	transform: translateY(-50%) rotate(-45deg);
	display: block;
	content: '';
	border: 3px solid #474747;
	border-left: 3px solid transparent;
	border-top: 3px solid transparent;
}

#widget .tooltip {
	display: none;
	position: absolute;
	left: -53px;
	top: 78%;
}
.style_closeBtn {
	margin-left: 170px;
    width: 10px;
    height: 10px;
    background: url(../images/toc/ico-toc-close_white.png) no-repeat center;	
}
.style_closeBtn span {
	display : none;	
}
#map-quadMap-1{
	border-right: 0.2px solid black;
	border-bottom: 0.2px solid black;
	box-sizing: border-box;
}
#map-quadMap-3{
	border-top: 0.2px solid black;
	border-left: 0.2px solid black;
	box-sizing: border-box;
}
#map-dualMap-1{
	border-right: 0.2px solid black;
	box-sizing: border-box;
}
#map-threepleMap-1{
	border-bottom: 0.2px solid black;
	box-sizing: border-box;
}
.mainMap.odf-view.dualMap.d-2-2.dualMap-vertical.threepleMap.d-3-1.threepleMap-complex-03.quadMap.d-4-2.quadMap-complex.on3{
			border-left: 0.2px solid black;
	box-sizing: border-box;
}


.mapContainer #optionTable {
	position: relative;
	bottom: 0px;
	width: 100%;	
}
#app #optionTable {
	position: relative;
	bottom: 0px;
	width: 100%;	
	/*margin-bottom: 35px;*/
}
/*북마크위젯 */
#widget .dep1 .bookMarkWidget #bookMarkBtn {
    background-image: url(../images/widget/widget-bookmark-gray.png);
}
#widget .dep1 .bookMarkWidget #bookMarkBtn:hover {
    background-image: url(../images/widget/widget-bookmark-white.png);
    color: #fff;
}
#bookmarkDiv{
	position: absolute;
    top: 15px;
    right: 78px;
    display: block;
    width: 290px;
}
/*분할지도 시 css적용. 새로운 widget 리스트 생성 홈,분할지도, 초기화*/

.sampleFrame.themeType01.style02 #widget-dMakKey-main{
	right: auto;
    left: 10px;
    top: 63px;
}
.sampleFrame.themeType01.style04 #widget-dMakKey-main{
	right: auto;
    left: 10px;
    top: 63px;
}
#paging_resultArea {
	max-height : 420px;
}
#addressSearch_result{
	height: auto;
}
.addressSearch_result .addressSearch_inner{
	height: auto;
}
.addressSearch_result .addressSearch_head .addressSearch_inner{
	height: auto;
}
/* 국토정보기본도 */
.limsControlWidget .limsControl_onoffBtn {
    background-image: url(../images/widget/widget-lsmd-gray.png);
}

.limsControlWidget .limsControl_onoffBtn:hover {
    background-image: url(../images/widget/widget-lsmd-white.png);
    color: #fff;
}

.limsControl_limsLayerListDiv{
	position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
.limsControl_onoffBtnSpan,.limsControl_limsBaseOnOffBtnSpan , .limsControl_limsAirBtnSpan{
	font-size : 12px;
}
.limsControl_onoffBtnSpan{
word-break: keep-all;
}
/* 오버뷰 */
.overViewMapControlWidget .overViewMapControl_openBtn {
    background-image: url(../../smt/images/widget/widget-overview-gray.png);
}
.overViewMapControlWidget .overViewMapControl_openBtn:hover {
    background-image: url(../../smt/images/widget/widget-overview-white.png);
}
#overview {
	position:absolute;
	right:60px;
}
/* 연속지적도 */
.lsmdControlWidget .lsmdControl_onoffBtn {
    background-image: url(../images/widget/widget-lsmd-gray.png);
}
.lsmdControlWidget .lsmdControl_onoffBtn:hover {
    background-image: url(../images/widget/widget-lsmd-white.png);
    color: #fff;
}
.lsmdControlWidget .lsmdControl_lsmdBaseBtn,.limsControlWidget .limsControl_limsBaseBtn {
    background-image: url(../images/widget/widget-lsmd-normal-gray.png);
}
.lsmdControlWidget .lsmdControl_lsmdBaseBtn:hover ,.limsControlWidget .limsControl_limsBaseBtn:hover {
    background-image: url(../images/widget/widget-lsmd-normal.png);
}
.lsmdControlWidget .lsmdControl_lsmdAirBtn,.limsControlWidget .limsControl_limsAirBtn{
    background-image: url(../images/widget/widget-lsmd-air-gray.png);
}
.lsmdControlWidget .lsmdControl_lsmdAirBtn:hover, .limsControlWidget .limsControl_limsAirBtn:hover {
    background-image: url(../images/widget/widget-lsmd-air.png);
}

.lsmdControl_lsmdLayerListDiv{
		position: absolute;
    top: 0;
    right: 0;
    padding: 0 10px;
    display: flex;
    flex-direction: row;
    border-radius: 3px;
    transition: .2s;
    background: #fff;
    box-shadow: 0.6px 0.8px 4px 0 rgb(0 0 0 / 35%);
    visibility: visible;
    opacity: 1;
    right: 68px;
}
.linkRoadView{
	background-image: url(../images/widget/widget-roadview-gray.png);
}

.linkRoadView:hover{
	background-image: url(../images/widget/widget-roadview-white.png);
}

#chart_popup .modal_head{
	background: linear-gradient(to right, #2F5597, #8FAADC);
}
#chart_popup .modal_head span{
    margin-left: 20px;
    line-height: 46px;
    color: #fff;
    font-size: 16px;
    font-family: "Pretendard";
    font-weight: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;	
}
#chart_popup{
	width: 351px !important;
}
/* 오버뷰맵 */ 
.ol-overviewmap:not(.odf-inner) .ol-overviewmap-map{
	position : absolute;
}
.ol-overviewmap:not(.ol-collapsed){
	background : unset;
}
#top_widget_div .downloadControl_downloadGrpDiv{
    top: 45px;
    right: unset;
}
#spatialAnalysisDetailWidget .spatialAnalysis_selectAdministrativeDistrict_select{
	font-size: 14px;
    padding-left: 3px;
    background: url(../images/common/ico-select.png) no-repeat right 2px center;
}
#optionTable>.inner{
    height : 100%;
}
#optionTable>.inner>#gridWidth.cont,
#optionTable>.inner>#gridWidget.cont
{
    /*height : 100% !important;*/
}
#optionTable>.inner>#gridWidth.cont>.odf-feature-grid,
#optionTable>.inner>#gridWidget.cont>.odf-feature-grid
{
    height : 100%;
}
#app #optionTable>.inner>#gridWidth.cont>.odf-feature-grid>.grid_tableDiv,
#app #optionTable>.inner>#gridWidget.cont>.odf-feature-grid>.grid_tableDiv{
    height: calc(100% - 161px);
}
#optionTable>.inner>#gridWidth.cont>.odf-feature-grid>.grid_tableDiv,
#optionTable>.inner>#gridWidget.cont>.odf-feature-grid>.grid_tableDiv{
    height: calc(100% - 170px);
}

#optionTable>.inner>#gridWidth.cont>.odf-feature-grid>.paging_pagination,
#optionTable>.inner>#gridWidget.cont>.odf-feature-grid>.paging_pagination{
    height : 50px;
    background: white;
}
#optionTable>.inner>#gridWidget.cont>.odf-feature-grid>.grid_tableDiv>.ag-theme-alpine{
	height: 100% !important;
}
#optionTable>.inner>#gridWidget.cont>.odf-feature-grid>.grid_tableDiv>.ag-theme-alpine>div{
	height: 100% !important;
}
#app #optionTable.hide{
	margin-bottom: 0px;
}
#optionTable.hide{
    height: 46px;
}
