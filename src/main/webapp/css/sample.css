@charset "UTF-8";

#showLive{
		float:right;
		border: 1px solid #dbdbdb;
		color: #666;
		padding:5px 13px;
		background-color: #fff;
		font-size: 12px;
		margin-top: 10px;
}

.onoffBtn,
.onoffOnlyBtn {
	margin: 0 5px 5px 0;
    padding: 3px 23px;
    background: #fff;
    border: 1px solid #cbced2;
    color: #585858;
    border-radius: 3px;
    font-family: '맑은 고딕';
    font-weight: bold;
    font-size: 14px;
}

.onoffBtn.on,
.onoffBtn:hover,
.onoffOnlyBtn.on,
.onoffOnlyBtn:hover,
.onoffOnlyBtn.toggle.active,
.onoffBtn.toggle.active{
	color: #fff;
    background: #376fee;
    border: 1px solid transparent;
}

.odf-core-map{
	height: fit-content !important;
}


iframe {
	border-width: 0px;
}

.btnLogArea {
    margin-top: 5px;
    border: 1px solid #376fee;
    background: #e5f6ff;
    border-radius: 3px;
}

.btnLogArea .innerBox {
    padding: 10px;
}

/* line 62, scss/cont.scss */
.tocinput, #rtargetGroupId {
	width: 100px;
	height: 26px;
}

.selectCustom {
	width: 150px;
    height: 26px;
}
.btnDiv {
	margin-top:15px;
}
#map-timeSeries, #map2-timeSeries,
#map3-timeSeries, #map4-timeSeries{
	background : #fff url(../images/common/ico-down.png) no-repeat right 3px center;
}