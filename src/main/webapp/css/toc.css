@charset "UTF-8";
.toc_sub_div input[type=text]{
	width : 200px ;
    height: 30px;
}
.toc_sub_div select{
	width : 200px ;
    height: 30px;
}
.jstree-default .jstree-node, .jstree-default .jstree-icon {
	background-image: url('');
}
#showLive{
			float:right;
			border: 1px solid #dbdbdb;
			color: #666;
			padding:5px 13px;
			background-color: #fff;
			font-size: 12px;
			margin-top: 10px;
		}
.jstree-default .jstree-anchor {
	line-height: 50px;
	height: 50px;
	width: 200px;
}
.jstree-default .jstree-node {
	margin-left: 10px;
}
.toc_content {
	position: relative;
/* 	z-index: 1990; */
}

.toc_div {
	position: absolute;
/* 	z-index: 1990; */
	top: 0px;
	left: 0;
	width: 300px;
	height: 450px;
	border-right: 1px solid #c8c8c8;
	background-color: #fff;
}

.toc_title {
	padding: 5px 15px;
	/* 	background-color: #36b3e6; */
	background-color: #5082f3;
}

.toc_title b {
	color: #fff;
	font-size: 15px;
	font-weight: 700;
	letter-spacing: -.5px;
}

.toc_body {
	/* 	overflow: auto; */
	overflow: hidden;
	/* height: calc(100% - 45px); */
}

.toc_body li {
	padding: 5px;
	border-top: 1px solid #1d1a1a63;
}

.toc_body li img {
	margin-left: 10px;
	width: 30px;
	height: 30px;
}

.toc_sub_div {
	display: none;
	position: absolute;
	z-index: 1990;
	top: 0px;
	left: 315px;
	width: 350px;
	height: 500px;
	border-right: 1px solid #c8c8c8;
	background-color: #fff;
	
}

.toc_sub_div .title {
	padding: 5px 15px;
	background-color: #bdc6ca;
}

.toc_sub_div button {
	position: relative;
	left: 190px;
	width: 25px;
}

.toc_sub_div .body {
	margin-top: 5;
}

.toc_sub_div .body .submenu {
	background-color: #36b3e6;
	height: 30px;
	margin : 10px;
}

.toc_sub_div .body ul {
	margin-left: 10px;
}

.toc_sub_div .body ul li {
	color: white;
	padding-right: 20px;
	list-style: none;
	float: left;
	line-height: 30px;
	vertical-align: middle;
	text-align: center;
}

.toc_sub_div .body ul li a {
	color: white;
	text-decoration: none;
	border-bottom: 1px solid;
}

.toc_sub_div .body .tab_body {
	display: none;
	margin : 10px;
}

.toc_sub_div .body .tab_body .tab_form {
	margin: 10px;
	border: 1px solid;
	padding: 20px;
	margin-top: 10px;
}

.toc_sub_div .body .tab_body .tab_form input {
	margin-bottom: 5px;
}

.toc_sub_div .body .tab_body .tab_form select {
	width: 200px;
	margin-bottom: 5px;
}

.toc_sub_div .body .tab_body .filter_btn {
	margin-top: 10px;
	margin-bottom : 10px;
	text-align: center;
}

#filter_area {
	overflow: scroll;
	height: 300px;
}

#layerStyleTab .symbolSelect {
	margin-top: 10px;
	text-align: center;
	border-bottom: 1px solid black;
	padding-bottom: 10px;
}

#layerStyleTab .area-asideScroll {
	margin: 15px;
}

#layerStyleTab .area-asideScroll .inline {
	margin: 10px 0px 10px 10px;
	padding-bottom: 5px;
	border-bottom: 1px solid #c8c8c8;
}

#layerStyleTab .area-asideScroll .inline div {
	border-top: 1px solid #c8c8c8;
}

#layerStyleTab .area-asideScroll .inline input {
	width: 50px;
}

#layerStyleTab .area-asideScroll .styleBtn {
	text-align: center;
}

#layerStyleTab .area-asideScroll .styleChangeOption {
	width: 100px !important;
}

#layerStyleTab .area-asideScroll .tip {
	font-size: 8px;
}

#layerStyleTab .imageList {
	border: 1px solid #cccccc;
	padding: 10px;
	margin-bottom: 5px;
	height: 100px;
	overflow: auto;
}

#layerStyleTab .imageList img {
	margin: 5px;
	width: 25px;
	height: 25px;
}

#layerStyleTab .applyImageDiv {
	border: 1px solid #cccccc;
	padding: 10px;
}

#layerStyleTab .applyImageDiv p {
	margin: 5px;
}

#layerStyleTab .applyImageDiv .sizeDiv {
	border-top: 1px solid #cccccc;
	padding-top: 10px;
}

#layerStyleTab .imageViewer {
	width: 25px;
	heigth: 25px;
}

/* 20200909 지도드림 css 추가 */
#toc {
	position: absolute;
	top: 0;
/* 	z-index: 30; */
	width: 310px;
	height: 100%;
	box-shadow: 0 0 3px #c2c2c2;
	background: #fff;
}

#toc .tocTop {
	padding-bottom: 5px;
	/*     background: #36B3E6; */
	background: #376fee;
}

#toc .tocTop .trow {
	padding: 13px 10px 7px;
}

#toc .tocTop .trow h4 {
	display: inline-block;
	padding-left: 30px;
	margin-right: 38px;
	color: #ffffff;
	font-size: 17px;
	font-family: "Noto Sans Medium";
	background: url(../images/toc/ico-layer.png) no-repeat left center;
}

button {
	display: inline-block;
	border: none;
	background-color: transparent;
	cursor: pointer;
}

.hidden {
	display: block;
	margin: 0;
	padding: 0;
	width: 0;
	height: 0;
	overflow: hidden;
	font-size: 0;
	line-height: 0;
	visibility: hidden;
}

#toc .tocTop button {
	display: inline-block;
	vertical-align: text-bottom;
}

#toc .tocTop .trow button.addGroup {
	width: 30px;
	height: 30px;
	margin-bottom: -5px;
	background: url(../images/toc/btn-grp.png) no-repeat;
}

#toc .tocTop .trow button.addGroup:hover {
	background: url(../images/toc/btn-grp-hover.png) no-repeat;
}

#toc .tocTop .trow button.addData {
	width: 30px;
	height: 30px;
	margin-bottom: -5px;
	background: url(../images/toc/btn-find.png) no-repeat;
}

#toc .tocTop .trow button.addData:hover {
	background: url(../images/toc/btn-find-hover.png) no-repeat;
}

#toc .tocTop .trow button.saveData {
	width: 30px;
	height: 30px;
	margin-bottom: -5px;
	background: url(../images/toc/btn-save.png) no-repeat;
}

#toc .tocTop .trow button.saveData:hover {
	background: url(../images/toc/btn-save-hover.png) no-repeat;
}

#toc .toc_body li div.layerPd {
	position: relative;
	display: inline-block;
	height: 47px;
	/* 	width: 200px; */
	width: 235px;
	line-height: 47px;
	padding-left: 34px;
	margin-right: 60px;
	font-size: 13px;
	font-family: 'Noto Sans';
}

#toc .toc_body li div.layerPd span.point {
	position: absolute;
	display: inline-block;
	left: 18px;
	top: 20px;
	width: 7px;
	height: 7px;
	background: #bcbfbe;
	border-radius: 50%;
}

#toc .toc_body li div.layerPd span.line {
	position: absolute;
	display: inline-block;
	left: 20px;
	top: 15px;
	width: 2px;
	height: 17px;
	background: #bcbfbe;
	transform: rotate(30deg);
}

#toc .toc_body li div.layerPd span.polygon {
	position: absolute;
	display: inline-block;
	left: 11px;
	top: 15px;
	width: 17px;
	height: 17px;
	border: 2px solid #bcbfbe;
	box-sizing: border-box;
}

#toc .toc_body li div.layerPd span.polygon span {
	position: absolute;
	left: -3px;
	top: -3px;
	display: block;
	width: 4px;
	height: 4px;
	background: #bcbfbe;
}

#toc .toc_body li div.layerPd span.polygon span:nth-child(2) {
	left: 12px;
}

#toc .toc_body li div.layerPd span.polygon span:nth-child(3) {
	left: 12px;
	top: 12px;
}

#toc .toc_body li div.layerPd span.polygon span:nth-child(4) {
	top: 12px;
}

.toc_body li:hover {
	border-left: 4px solid #376fee;
	background-color: #f6f9fe;
}

.toc_body li.clicked {
	border-left: 4px solid #376fee;
	/* 	background-color: #f6f9fe; */
}

.toc_body div.layerPd div.layerBtn {
	float: right;
	padding-top: 8px;
}

.toc_body div.layerPd div.layerBtn {
	float: right;
	padding-top: 8px;
}

.toc_body div.layerPd div.groupBtn {
	float: right;
	/*     padding-right: 40px; */
	padding-top: 8px;
}

.toc_body div.layerPd span.icon {
	position: absolute;
    /* display: inline-block; */
    left: 3px;
    top: 8px;
    width: 25px !important;
    height: 25px !important;
    display: flex;
}

.toc_body div.layerPd div.icon{
	position: absolute;
    left: 3px;
    top: 8px;
    width: 25px !important;
    height: 25px !important;
    display: flex; 
}
.icon span.inner_half_icon {
    width: 12.5px !important;
    border-radius : 0 !important;
    border-width : 1px! important;
    
    position: absolute;
    left: 0px;
    top: 0px;
}

.icon span.inner_half_icon.left {
    border-top-left-radius:12.5px !important;
    border-bottom-left-radius:12.5px !important;
    
    left: 0px !important;
}

.icon span.inner_half_icon.right{
    border-top-right-radius:12.5px !important;
    border-bottom-right-radius:12.5px !important;
    
    left: 15px !important;
}


.icon span.inner_quad_icon {
    border-radius : 0 !important;
    width: 12.5px !important;
    height: 12.5px !important;
    border-width : 1px! important;
    
    position: absolute;
    left: 0px;
    top: 0px;
}

.icon span.inner_quad_icon.left_top {
    border-top-left-radius:100% !important;
    left: 0px !important;
    top: 0px !important;
}

.icon span.inner_quad_icon.right_top {
    border-top-right-radius:100% !important;
    left: 12.5px !important;
    top: 0px !important;
}

.icon span.inner_quad_icon.left_bottom {
    border-bottom-left-radius:100% !important;
    left: 0px !important;
    top: 12.5px !important;
}

.icon span.inner_quad_icon.right_bottom {
    border-bottom-right-radius:100% !important;
    left: 12.5px !important;
    top: 12.5px !important;
}

.toc_body div.layerPd span.group {
position: absolute;
    display: inline-block;
    left: 3px;
    top: 8px;
    width: 30px;
    height: 30px;
    background: url(../images/toc/btn-grp.png) no-repeat;
}
.toc_body div.layerPd span.wmts {
	position: absolute;
    display: inline-block;
    left: 3px;
    top: 8px;
    width: 25px;
    height: 25px;
    background: url(../images/toc/btn-wmts.png) no-repeat;
    background-position: center center;
}
.toc-btn{
	width : 30px;
	height : 30px;
	border: none;
    margin-left: 7px;
    background-size: cover  !important;
}
.plusminus.open {
	background: url(../images/toc/group-on.png) ;
}
.plusminus.close {
	background: url(../images/toc/group-off.png) ;
}
.label {
	background: url(../images/toc/btn-bookmark-on.png) ;
}
.onoff.on{
	background: url(../images/toc/btn-show-on.png) ;
}
.onoff.off{
	background: url(../images/toc/btn-show-off.png) ;
}
.remove {
	background: url(../images/toc/btn-remove-off.png) ;
}

/*이름 변경 input*/
.name input.changedName {
	border-radius: 5px;
	width: 80px;
    margin-top: 12px;
    margin-left: 3px;
}
.name button{
	border-radius: 5px;
	border: solid 1px black;	
	margin : 1px;
}

.name button:hover{
	background-color: #99999955;
	
}

/* .toc_body li a { */
/* 	float: left; */
/* } */

/* #toc .toc_body div.layerPd img.remove:hover { */
/* 	background: url("../images/toc/btn-remove-on"); */
/* } */