<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/bookmark.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<!-- 위젯의 target파라미터에 target 태그를 넘기면, 툴바 button 형식으로 만들어지는 위젯이있고(그리기도구/배경지도위젯 등 ),
			위젯의 기능이 타겟에 만들어지는 위젯(북마크/타임슬라이더/스와이퍼 위젯)이 있습니다.
			이때, 툴바 button 형식으로 만들어지지 않는 위젯을 툴바형식으로 만들기 위한 html 과 script가 추가되어있습니다. -->
	<ul class="toolbar">
		<li>
			<div>
				<button class="bookmark_btn" onclick="toggleContent('#bookmarkDiv')">
					<span>북마크</span>
				</button>
			</div>
		</li>
	</ul>
	<div id="bookmarkDiv" class="bookmarkDiv off"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
	var bookmarkClient = oui.HttpClient({
		baseURL: '::smtAPI::',
    });
	var bookmarkApiOption = {
  		userId: '::userId::',
  		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			bookmarkFunction : 3000,
		}
		 */
	};
    var bookmarkApi = oui.BookmarkApi(bookmarkClient, bookmarkApiOption);

    /* 북마크 관리 위젯 생성  */
    var bookMarkControlWidget = new oui.BookMarkControlWidget({
        api: {
        		bookmarkFunction: bookmarkApi.bookmarkFunction,
        },
        target: document.getElementById('bookmarkDiv'),
    });
	bookMarkControlWidget.addTo(map);
	//지우기함수
	//bookMarkControlWidget.remove();

	/* 툴바에 버튼을 클릭하면, 관련 위젯  활성화/비활성화 하는 함수  */
	function toggleContent(contentTagId){
		document.querySelector(contentTagId).classList.toggle('off');
	}

</script>
</html>
