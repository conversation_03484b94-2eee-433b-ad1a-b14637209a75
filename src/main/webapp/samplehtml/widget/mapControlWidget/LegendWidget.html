<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<style>
 #legend{
 	position: relative;
 }
 #style{
 	position: absolute;
    top: 0px;
    right: 0px;
    padding: 0 25px;
    background-color: white;
    height: 550px;
    overflow-y: scroll;
 }
 .style_inner{
 	height:unset !important;
 }
</style>
<body>
	<div id ="map" style="height:550px;"></div>
	<div id="style" class="cScroll"></div>
	<div id="legend" ></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);



	/* 테스트 레이어 생성 */
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::testPointLayer1::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();



	//vWorld 오픈 api > wms/wfs api 2.0 레퍼런스
	var apiLayer = odf.LayerFactory.produce('api', {
		server:{
			url:'http://api.vworld.kr/req/wms',
			proxyURL : 'proxyUrl.jsp',
			proxyParam : 'url',
		} , // API 주소
		//server : 'http://api.vworld.kr/req/wms',// API 주소
		service: 'wms', // wms/wfs
		layers: 'lt_c_adsigg', //하나 또는 쉼표(,)로 분리된 지도레이어 목록, 최대 4개  ※https://www.vworld.kr/dev/v4dv_wmsguide2_s001.do 에서 사용가능한 레이어 목록 확인 가능
		styles: 'lt_c_adsigg', //LAYERS와 1대1 관계의 하나 또는 쉼표(,)로 분리된 스타일 목록
		//version : '1.3.0',//요청 서비스 버전
		crs : 'EPSG:::srid::',//응답결과 좌표계와 bbox 파라미터의 좌표계
		//transparent : 'true',//지도 배경의 투명도 여부
		//bgcolor:'0xFFFFFF',//배경색
		//exceptions:'text/xml',
		originalOption : {//odf에서 제공해주는 기본 파라미터 적용 여부
			//SERVICE : true,//(기본값 true)
			//REQUEST : true, //(기본값 true)
			//WIDTH : true,//(기본값 true)
			//HEIGHT : true,//(기본값 true)
			BBOX : '::bbox::',
			/* ★BBOX★
			odf에서 기본 제공하는 bbox 배열은 minx,miny,maxx,maxy 순인 반면에
			vworld에서는 EPSG:4326/EPSG:5186/EPSG:5187일 경우 bbox 배열을 miny,minx, maxy,maxx 순으로 입력받음
			해당 경우에는 BBOX 값을 '{{miny}},{{minx}},{{maxy}},{{maxx}}' 와같이 입력하면 x와 y의 순서가 바뀌어 적용됨.
			*/
			//FORMAT : true,//(기본값 false)
			//TRANSPARENT : true,//(기본값 false)
			//STYLES : true,//(기본값 false)
			//CRS : false,//(기본값 false)
			//VERSION : false,//(기본값 false)
		},
		/* 직접해보기에서 api 를 실행할 때는 아래 domain 값 부분을 주석처리해야 api 가 정상 동작합니다. */
		domain:'::vWorldDomain::',//API KEY를 발급받을때 입력했던 URL
		key : '::vWorldApiKey::',//발급받은 api key
	});
	apiLayer.setMap(map);


	var alertList = {
		customAlert: (message) => {
			alert(message);
		},
		customErrorAlert: (message) => {
			alert(message);
		}
	};

	/* 범례 위젯 생성 */
	var legendwWidget = new oui.LegendWidget({
		odf,
        options: {
        		alertList,
            useAccordion:true,
            layerList : [
            		{
	            		layer : wfsLayer,
	            		contentId : wfsLayer.getODFId(),
	            		title : 'WFS 레이어',
	            		geometryType : 'point'
	            },
	            {
	            		layer : apiLayer,
	            		contentId : apiLayer.getODFId(),
	            		title : '브이월드 wms 레이어',
	            		geometryType : 'polygon'
	            }
            ]
        },
        target: document.querySelector('#legend')
    });
	legendwWidget.addTo(map);



	//00.01. 레이어 api 생성
	var userId = '::userId::';
	var smtClient = oui.HttpClient({
		baseURL: '::smtAPI::',
	});
	var layerApi = oui.LayerApi(smtClient, {
		userId: userId,
		crtfckey: '::crtfckey::',
		groupCode: 'MPD003',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			selectSymbol : 3000,
			insertSymbol : 3000,
			deleteSymbol : 3000,
		}
		 */
	});

	//00.02. 컬럼 정보 api 객체 생성
	var columnInfoApi = oui.ColumnInfoApi(smtClient, {
		lyrId: '::TestPointLayer1Id::',
		userId: userId,
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			selectColumn : 3000,
			selectUniqueColumn : 3000,
			selectRangeColumn : 3000,
		}
		 */
	});
	var attributeApi = oui.AttributeApi(smtClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getCountAttributes : 3000,
		}
		 */
	});

	//01. 스타일 위젯 생성
	var styleWidget = new oui.StyleWidget({
		odf,
	    target: document.querySelector('#style'),
	    options: {
	    		imageUrl: 'js/oui/images/widget',
	    		alertList,
	    },
	    api: {
    		// 사용자정의 이미지 조회 function
			selectSymbol: layerApi.selectSymbol,
			// 사용자정의 이미지 추가 function
			insertSymbol: layerApi.insertSymbol,
			// 사용자정의 이미지 삭제 function
			deleteSymbol: layerApi.deleteSymbol,
			// 별칭 및 컬럼 정보 조회
			selectColumn: columnInfoApi.selectColumn,
			// 컬럼정보조회 옵션값 변경
			columnInfoOptionChange: columnInfoApi.changeOption,
			// 레이어 컬럼 유일값 조회
			selectUniqueColumn: columnInfoApi.selectUniqueColumn,
			// 레이어 컬럼 범위 조회
			selectRangeColumn: columnInfoApi.selectRangeColumn,
			// 레이어 속성 정보 목록 갯수 조회
			getCountAttributes : attributeApi.getCountAttributes,
	    }
	});
	styleWidget.addTo(wfsLayer,'::TestPointLayer1Id::',map);
</script>
</html>
