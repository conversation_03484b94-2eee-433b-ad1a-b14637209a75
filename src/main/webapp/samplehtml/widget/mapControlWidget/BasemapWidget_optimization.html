<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/basemap.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<div>현재 좌표계 : <span id="projection"></span></div>
	<ul class="toolbar">
	    <li class="basemapWidget" id="basemapWidget"></li>
	</ul>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, {
		...mapOption,
		//배경지도 최적화 on
		optimization : true,
	});


	/* oui api 연결 객체 생성  */
	var basemapClient =  oui.HttpClient({
		baseURL: '::smtAPI::',
	});
	var basemapApi = oui.BasemapApi(basemapClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getBasemapList : 3000,
		}
		 */
	});

	/* 베이스맵 위젯 생성 */
  	var basemapControlWidget = new oui.BasemapWidget({
		odf: odf, //odf 모듈
		target: document.querySelector('#basemapWidget'),
		api: {
			// 배경지도 조회 api
			getBasemapList: basemapApi.getBasemapList,
		},
		options: {
			//배경지도 최적화 on
			optimization : true,
			// 이미지 사용여부
			useImage: true,

			// toolbox 표현 방향
			toolboxPosition: 'left',//'left', 'right'

			//베이스맵 위젯 테마 설정
			//1)'menu' 메뉴 형 2)'gallary' 갤러리형  4)'mobile' 모바일형
			thema: 'gallary',

			// 사용할 배경지도만 필터링
			// filter: (bcrnMapId/*배경지도id*/) => {
			// let tailNumber = Number(bcrnMapId.substring(10));
			// if (tailNumber >= 2 && tailNumber <= 18) {
			// return true;
			// }
			// return false;
			// },

			//배경지도의 표출 순서를 변경하는 함수
			// sort : (list /*배경지도 정보가 담긴 object의 배열*/)=>{
			// 	return list
			// },


			//지도 선택 시 그룹 창 닫기 여부
			directClose: false,

			//사용자 정의 alert 사용
			alertList: {
				customAlert: (message) => {
					//callAlertMessage(message);
				},
				customErrorAlert: (message) => {
					//callAlert('error', message);
				}
			},

			//프록시 정보 셋팅
			proxyObject: {
				proxyURL: "::DeveloperUrl::/proxyUrl.jsp",
				proxyParam: "url"
			},

			//하이브리드 사용여부 기본값 true
			useHybrid : true,
			//빈 지도 사용여부. 기본값 true
			useNoDisplay : false
		}
	});
  	basemapControlWidget.addTo(map);
  	//지우기 함수
	//basemapControlWidget.remove();



		/* wms 레이어 생성 */
		var wmsLayer = odf.LayerFactory.produce('geoserver', {
			method: 'get',
			server: '::WmsAPI::',
			layer: '::polygonLayer1::',
			service: 'wms',
		});
		wmsLayer.setMap(map);

		var sld = odf.StyleFactory.produceSLD({
			rules: [{
				name: 'My Rule', /*룰 이름*/
				symbolizers: [{
					kind: 'Fill',
					/*채우기색
					rgba 값 입력시 자동변환. 네번째 인수 (투명도) 존재시 fillOpacity 보다 우선 적용됨
					 */
					color: '#FF9966',
					/*채우기 투명도 0~1*/
					fillOpacity: 0.7,
					/*윤곽선색
						rgba 값 입력시 자동변환. 네번째 인수 (투명도) 존재시 outlineOpacity보다 우선 적용됨
					 */
					outlineColor: '#338866',
					/*윤곽선 두께*/
					outlineWidth: 2,
				},],
			},],
		});

		//sld 적용
		wmsLayer.setSLD(sld);


		// wfs 레이어 생성
		// LayerFactory의 produce 함수는 option이 다양하니 개발자지원센터 '지도문서'를 확인하세요
		var wfsLayer = odf.LayerFactory.produce('geoserver'/*레이어를 생성하기위 한 테이터 호출 방법*/, {
			method: 'get',//'post'
			server: '::WfsAPI::', // 레이어가 발행된 서버 주소
			layer: '::polygonLayer2::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
			service: 'wfs', // 호출하고자 하는 레이여 형태(wms, wfs, wmts)
		}/*레이어 생성을 위한 옵션*/);
		wfsLayer.setMap(map);
		// 해당 layer가 한눈에 보이는 보여주는 extent로 화면 위치 이동 및 줌 레벨 변경
		wfsLayer.fit();


		document.querySelector('#projection').innerText = map.getView().getProjection().getCode();
		odf.event.addListener(map, 'change:view', () => {
			document.querySelector('#projection').innerText = map.getView().getProjection().getCode();
		})

</script>
</html>
