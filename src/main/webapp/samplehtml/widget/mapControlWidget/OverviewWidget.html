<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/overViewMapControl.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<ul class="toolbar">
		<li class="overViewMapControlWidget" id="overViewMapControlWidget"></li>
	</ul>
	<div id="overview" class="overview"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 오버뷰 위젯 생성 */
	var overViewMapControlWidget = new oui.OverViewMapControlWidget({
		options: { element: document.getElementById('overview') },
		target: document.getElementById('overViewMapControlWidget'),
	});
	overViewMapControlWidget.addTo(map);
	//지우기함수
	//overViewMapControlWidget.remove();
</script>
</html>
