<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/zoomControl.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<div id="zoomControl" class="zoomControl"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 확대/축소 위젯 생성  */
	var zoomControlWidget = new oui.ZoomControlWidget({
		options: {
			// 줌슬라이더 사용 여부(기본값 true)
			// slider  :true
		},
		target: document.getElementById('zoomControl'),
	});
	zoomControlWidget.addTo(map)
	//지우기함수
	//zoomControlWidget.remove();
</script>
</html>
