<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/basemap.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>

	<!-- mapProviders 활성화 시키는 경우 옵션에 맞는 라이브러리를 설정해야합니다. -->
	<script type="text/javascript" src="//dapi.kakao.com/v2/maps/sdk.js?appkey=::kakaoAppKey::"></script>
	<script type="text/javascript" src="https://oapi.map.naver.com/openapi/v3/maps.js?ncpClientId=::naverAppKey::"></script>
	<script>
		(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.googleapis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})({
			key: "::googleAppKey::",
			v: "weekly",
		});
	</script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<ul class="toolbar">
	    <li class="basemapWidget" id="basemapWidget"></li>
	</ul>
	<div style="margin-top: 15px; position: absolute;">
		<button class="onoffOnlyBtn toggle grp1 active" onclick="setTheme('gallary');">갤러리 테마</button>
		<button class="onoffOnlyBtn toggle grp1" onclick="setTheme('menu');">메뉴 테마</button>
		<button class="onoffOnlyBtn toggle grp1" onclick="setTheme('mobile');">모바일 테마</button>
	</div>
</body>
<style>
	/* 네이버, 카카오, 구글지도를 활성화하면 odf-map class를 가진 element의 position이 'absolute'로 변경됩니다. */
	.odf-map {
		position: absolute;
	}
</style>
<script>
	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* oui api 연결 객체 생성  */
	var basemapClient =  oui.HttpClient({
		baseURL: '::smtAPI::',
	});
	var basemapApi = oui.BasemapApi(basemapClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getBasemapList : 3000,
		}
		 */
	});
    var options = {
		// 이미지 사용여부
		useImage: true,

		// toolbox 표현 방향
		toolboxPosition: 'left',//'left', 'right'

		//베이스맵 위젯 테마 설정
		//1)'menu' 메뉴 형 2)'gallary' 갤러리형  4)'mobile' 모바일형
		thema: 'gallary',

		// 사용할 배경지도만 필터링
		// filter: (bcrnMapId/*배경지도id*/) => {
		// let tailNumber = Number(bcrnMapId.substring(10));
		// if (tailNumber >= 2 && tailNumber <= 18) {
		// return true;
		// }
		// return false;
		// },

		//배경지도의 표출 순서를 변경하는 함수
		// sort : (list /*배경지도 정보가 담긴 object의 배열*/)=>{
		// 	return list
		// },

		//지도 선택 시 그룹 창 닫기 여부
		directClose: false,

		//사용자 정의 alert 사용
		alertList: {
			customAlert: (message) => {
				//callAlertMessage(message);
			},
					customErrorAlert: (message) => {
				//callAlert('error', message);
			}
		},

		//프록시 정보 셋팅
		proxyObject: {
			proxyURL: "::DeveloperUrl::/proxyUrl.jsp",
					proxyParam: "url"
		},

		//하이브리드 사용여부 기본값 true
		useHybrid : true,
			//빈 지도 사용여부. 기본값 true
		useNoDisplay : false,
		//OSM 사용여부, 기본값 false
		useOSM: false,
		//외부 라이브러리 지도 사용여부 기본값 false
		//활성화 할 경우 옵션에 해당하는 라이브러리를 추가한 후 사용해야 합니다.
		//지도를 활성화하면 mapContainer > 'odf-map' class를 가진 요소의 Position 이 absolute로 변경됩니다.
		mapProviders: {
			useNaverMap: true, //네이버지도 사용여부
			useKakaoMap: true, //카카오맵 사용여부
			useGoogleMap: false, //구글맵 사용여부
		},
		//외부 라이브러리 지도 썸네일 경로
		//해당 경로에 아래와같이 파일이 존재해야 정상적으로 썸네일이 출력됩니다.
		//네이버 - 일반지도: naver.png, 지형도: naverTerrain.png, 위성지도: naverSatellite.png, 하이브리드: naverHybrid.png
		//카카오 - 일반지도: kakao.png, 스카이뷰: kakaoSky.png
		//구글 - 일반지도: google.png, 지형도: googleTerrain.png, 위성지도: googleSatellite.png, 하이브리드: googleHybrid.png
		thumbnailPath: 'images/widget/basemap', // default
	}

	/* 베이스맵 위젯 생성 */
  	var basemapControlWidget = new oui.BasemapWidget({
		odf: odf, //odf 모듈
		target: document.querySelector('#basemapWidget'),
		api: {
			// 배경지도 조회 api
			getBasemapList: basemapApi.getBasemapList,
		},
		options
	});
  	basemapControlWidget.addTo(map);

	// basemapControlWidget.setActiveType('BM0000000002')

	// 테마 변경
	function setTheme(theme) {
		options.thema = theme
		//지우기 함수
		basemapControlWidget.remove();

		basemapControlWidget = new oui.BasemapWidget({
			odf: odf, //odf 모듈
			target: document.querySelector('#basemapWidget'),
			api: {
				// 배경지도 조회 api
				getBasemapList: basemapApi.getBasemapList,
			},
			options
		});
		basemapControlWidget.addTo(map);
	}

</script>
</html>
