<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/drawControl.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/measureControl.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/clearControl.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
<div id ="map" style="height:550px;"></div>
	<ul class="toolbar">
	    <li class="drawControlWidget" id="drawControlWidget"></li>
		<li class="measureControlWidget" id="measureControlWidget"></li>
		<li class="resetWidget" id="clearControlWidget"></li>
	</ul>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 그리기 도구 위젯 생성  */
	var drawControlWidget = new oui.DrawControlWidget({
		options : {
			rightClickDelete: true, //그리기도구로 그린 객체 우클릭 시 삭제버튼 활성화 여부 (true 활성화 / false 비활성화),
			callback: function () {
				measureControlWidget ? measureControlWidget.removeHelpTooltip() : null; //그룹 element 클릭 시 실행 callback 함수
			},
  			//style : {} //그리기 피쳐 odf 스타일 옵션,
  			//bufferStyle : {} //버퍼 도형 odf 스타일 옵션,
  			//createNewLayer : true //drawControl 생성 시 새로운 레이어 생성 여부
  			//message : { //그리기 도구 시작시 툴팁 메세지
  				//DRAWSATRT_POINT : '점 그리기 측정입니다.',
  				//DRAWSTART_LINESTRING : '',
  				//DRAWSTART_POLYGON : '',
  				//DRAWSTART_CURVE : '' ,
  				//DRAWSTART_TEXT : '',
  				//DRAWSTART_BUFFER : '',
  				//DRAWSTART_CIRCLE : '',
  				//DRAWSTART_BOX : '',
  				//DRAWEND_DRAG : '',
  				//DRAWEND_DBCLICK : '',
  			//},
  			//directClose : true, //그리기 도구 선택 시 그리기 도구 영역 자동으로 닫기 여부 (기본값 : true),
  			//measure : false, //측정 옵션 활성화 여부, (선, 원형만 제공)
  			//continuity : false, //연속 측정 여부  (기본값 false),
  			//rightClickDelete : false, //우클릭 삭제 기능 활성화 여부,
  			//tools : ['text', 'polygon','lineString','point','circle','curve','box','buffer'], //사용할 그리기 도구 목록
  		},
    		target: document.getElementById('drawControlWidget'),
	});
  	drawControlWidget.addTo(map);
  	//지우기함수
  	//drawControlWidget.remove();
	//지점 측정을 위한 api

	/* 측정도구 위젯 생성  */
	var addressClient = oui.HttpClient({
		baseURL: '::geocodingAPI::', //api 주소
	});
	var addressApi = oui.AddressApi(addressClient, {
		projection : '::srid::', //검색 api 대상 좌표계
		crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getAddressFromPoi : 3000,
		}
		 */
	});
	var measureControlWidget = new oui.MeasureControlWidget({
		options : {
  			callback: function () {
				drawControlWidget ? drawControlWidget.removeToolTip() : null; //그룹 element 클릭 시 실행 callback 함수
			},
  			//style : {} //odf 스타일 옵션,
  			//message : { //그리기 도구 시작시 툴팁 메세지
      			//DRAWSTART: '클릭하여 측정을 시작하세요',
      			//DRAWEND_POLYGON: '클릭하여 폴리곤을 그리거나, 더블클릭하여 그리기를 종료하세요',
      			//DRAWEND_LINE: '클릭하여 라인을 그리거나, 더블클릭하여 그리기를 종료하세요',
      			//DRAWEND_ROUND : '클릭하여 원을 그리거나, 원 그리기를 종료하세요.',
  			//},
  			//continuity : false, 연속 측정 여부  (기본값 false),
  			rightClickDelete: true, //측정도구로 그린 객체 우클릭 시 삭제버튼 활성화 여부 (true 활성화 / false 비활성화),
  			//사용할 툴 배열
  			//tools : ['distance', 'area','round', 'spot'], //사용할 측정 도구 목록
  			// 단위 표현 옵션
   			//displayOption : {
            //	area : {
            //      decimalPoint : 8,
            //      transformUnit : 10000,
            //    },
            //  	distance : {
            //      decimalPoint : 8,
            //      transformUnit : 10000,
            //    },
            //  	round : {
            //      decimalPoint : 8,
            //     transformUnit : 10000,
            //  	},
  		},
  		api : {
  			getAddressFromPoi: addressApi.getAddressFromPoi, //지점 측정을 위한 api 함수
  		},
    		target: document.getElementById('measureControlWidget'),
	});
  	measureControlWidget.addTo(map);
  	//지우기함수
  	//measureControlWidget.remove();

  	/* 초기화 위젯 생성  */
  	var clearControlWidget = new oui.ClearControlWidget({
  		target : document.getElementById('clearControlWidget'),
  	})
  	clearControlWidget.addTo(map);
  	//지우기함수
  	//clearControlWidget.remove();

</script>
</html>
