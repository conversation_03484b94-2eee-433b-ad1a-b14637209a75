<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <link href="::OdfUrl::/odf.css" rel="stylesheet">
    <link href="::OuiUrl::/oui.css" rel="stylesheet">
    <link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
    <link href="::SmtUrl::/css/widgets/swiperControl.css" rel="stylesheet">

    <script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
    <script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
<div id ="map" style="height:550px;"></div>
<ul class="toolbar">
    <li>
        <div>
            <button class="swipter_btn" id="swiperControlWidget">
                <span>스와이퍼</span>
            </button>
        </div>
    </li>
</ul>
</body>

<script>

    /* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
    var mapContainer = document.getElementById('map');
    var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
    var map = new odf.Map(mapContainer, mapOption);


    /* oui api 연결 객체 생성  */
    var basemapApiClient = oui.HttpClient({
        baseURL: '::smtAPI::', //api 주소
    });
    var basemapApi = oui.BasemapApi(basemapApiClient, {
        crtfckey: '::crtfckey::',
        /*
        //timeout 설정
        //timeout : 3000,
        timeout  :{
            getBasemapList : 3000,
        }
         */
    });

    /* 스와이퍼 위젯 생성  */
    var swiperControlWidget = new oui.SwiperControlWidget({
        options: {
            layerList: [],// [{layerNm : '테스트레이어1' , layer : testLayer} , {layerNm : '테스트레이어2', layer : testLayer2}], 스와이퍼 생성 시 표출할 레이어 목록
            createCallback: function () {
            },
            removeCallback: function () {

            },
        },
        api: {
            getBasemapList: basemapApi.getBasemapList,
        },
        target: document.querySelector("#swiperControlWidget")
    });

    swiperControlWidget.addTo(map);
    //지우기함수
    //swiperControlWidget.remove();

</script>
</html>
