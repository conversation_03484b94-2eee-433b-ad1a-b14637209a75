<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/divideMap.css" rel="stylesheet">
	<!-- 분할지도위젯에서 배경지도를 옵션을 사용할 경우에 basemap.css 추가  -->
	<link href="::SmtUrl::/css/widgets/basemap.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>

	<!-- mapProviders 활성화 시키는 경우 옵션에 맞는 라이브러리를 설정해야합니다. -->
	<script type="text/javascript" src="//dapi.kakao.com/v2/maps/sdk.js?appkey=::kakaoAppKey::"></script>
	<script type="text/javascript" src="https://oapi.map.naver.com/openapi/v3/maps.js?ncpClientId=::naverAppKey::"></script>
	<script>
		(g=>{var h,a,k,p="The Google Maps JavaScript API",c="google",l="importLibrary",q="__ib__",m=document,b=window;b=b[c]||(b[c]={});var d=b.maps||(b.maps={}),r=new Set,e=new URLSearchParams,u=()=>h||(h=new Promise(async(f,n)=>{await (a=m.createElement("script"));e.set("libraries",[...r]+"");for(k in g)e.set(k.replace(/[A-Z]/g,t=>"_"+t[0].toLowerCase()),g[k]);e.set("callback",c+".maps."+q);a.src=`https://maps.googleapis.com/maps/api/js?`+e;d[q]=f;a.onerror=()=>h=n(Error(p+" could not load."));a.nonce=m.querySelector("script[nonce]")?.nonce||"";m.head.append(a)}));d[l]?console.warn(p+" only loads once. Ignoring:",g):d[l]=(f,...n)=>r.add(f)&&u().then(()=>d[l](f,...n))})({
			key: "::googleAppKey::",
			v: "weekly",
		});
	</script>
</head>
<body>
	<!-- [★중요★]  분할지도가 제대로 동작하기 위해서는 map 태그의 height를 px로 지정해주어야 합니다.  -->
	<div id ="map" style="height:550px;"></div>
	<ul class="toolbar">
    		<li class="divideWidget" id="divideWidget"></li>
	</ul>
</body>
<style>
	/* 네이버, 카카오, 구글지도를 활성화하면 odf-map class를 가진 element의 position이 'absolute'로 변경됩니다. */
	.odf-map {
		position: absolute;
	}
</style>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
	var basemapApiClient = oui.HttpClient({
		baseURL: '::smtAPI::', //api 주소
	});
	var basemapApi = oui.BasemapApi(basemapApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getBasemapList : 3000,
		}
		 */
	});

	/* 분할지도 위젯 생성 */
  	var divideMapWidget = new oui.DivideMapWidget({
		odf: odf,
		target: document.querySelector('#divideWidget'),
		api: {

		},
		options: {
			alertList: {
				customAlert: (message) => {
					alert(message); // 메세지 표출 커스텀 함수
				},
				customErrorAlert: (message) => {
					alert('error', message); // 메세지 표출 커스텀 함수
				}
			},
			toolboxPosition: 'left', // 툴바위치 옵션
			// 홈 위젯 생성 옵션
/* 			home: { //
				center: [::coordx::, ::coordy::],
				zoom: 9
			},
			scale: { //축척 위젯 생성 옵션
				options: {
					size: 100,
					scaleInput: true,
				},
			}, */
			// toc 위젯 생성 옵션
			toc: {
				groupHeight: 38,
				layerHeight: 38,
				//getContentList: () => {
					//기존 toc에서 컨텐츠 목록 받는 함수
				//}
			},
			// 배경지도 위젯 생성 옵션
			basemap: {
				api: {
					// 배경지도 조회 api
					getBasemapList: basemapApi.getBasemapList,
				},
				options: {
					//								// 사용할 배경지도만 필터링
					//								filter: (bcrnMapId/* 배경지도id */) => {
					//									let tailNumber = Number(bcrnMapId.substring(10));
					//									if (tailNumber >= 2 && tailNumber <= 15) {
					//										return true;
					//									}
					//									return false;
					//								}
					useImage: true,// 이미지 사용여부
					toolboxPosition: 'left',// toolbox 표현 방향
					thema: 'gallary', //menu, gallary 타입 선택
					mapProviders: {
						useNaverMap: true, //네이버지도 사용여부
						useKakaoMap: true, //카카오맵 사용여부
						useGoogleMap: true, //구글맵 사용여부
					},
					//외부 라이브러리 지도 썸네일 경로
					//default path : 'images/widget/basemap'
					//Thumbnail file name
					//네이버 - 일반지도: naver.png, 지형도: naverTerrain.png, 위성지도: naverSatellite.png, 하이브리드: naverHybrid.png
					//카카오 - 일반지도: kakao.png, 스카이뷰: kakaoSky.png
					//구글 - 일반지도: google.png, 지형도: googleTerrain.png, 위성지도: googleSatellite.png, 하이브리드: googleHybrid.png
					thumbnailPath: 'images/widget/basemap',
				},
			},
			// 분할지도 생성옵션
			divideMap: {
				// config : {
				// dualMap : {
				// divType : 'horizonal'//'vertical'=>수직분할 /
				// 'horizonal'=>수평분할
				// },
				// threepleMap : {
				// divType : 'complex-04'//'vertical'=>수직분할 /
				// 'horizonal'=>수평분할 //
				// 'complex-01'|'complex-02'|'complex-03'|'complex-04'
				// => 복합형
				// },
				// quadMap : {
				// divType : 'horizonal'//'vertical'=>수직분할 /
				// 'horizonal'=>수평분할 // 'complex' => 복합형
				// },
				// }
			},
			// 분할지도 on/off 상태 변화시 호출되는 콜백
			beforeChangeStatus: (dMapKey, dMapFlag) => {
				// dMapKey =>dualMap(2분할), quadMap(3분할)
				// dMapFlag =>true(on), false(off)
				let displayTarget = document.querySelector('.toolbar');
				if (dMapFlag) {
					displayTarget.style.display = 'none';
				}else{
					displayTarget.style.display = 'block';
				}
			},
		}
	});
	divideMapWidget.addTo(map);
  	//지우기함수
  	//divideMapWidget.remove();

</script>
</html>
