<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
</head>
<link href="::OdfUrl::/odf.css" rel="stylesheet">
<link href="::OuiUrl::/oui.css" rel="stylesheet">
<link href="::SmtUrl::/css/widget.css" rel="stylesheet">
<link href="::SmtUrl::/css/common.css" rel="stylesheet">
<link href="::SmtUrl::/css/widget_custom.css" rel="stylesheet">
<link href="::SmtUrl::/css/common_custom.css" rel="stylesheet">

<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
<style>
 .contents_widgetBox{
	position: absolute;
    top: 0px;
    background-color: white;
    height: 100%;
    width : 300px;
    }
 #contents_widgetBox_widget02{
 	position: absolute;
    left: 295px;
    width: 100%;
    top: 0px;
    background-color: white;
    overflow-y:auto;
    max-height:650px;
 }
   #contents_widgetBox_widget03{
 position: absolute;
    bottom: 0px;
    background-color:white;
    width: 1100px;
 }
/*   .odf-map{
  	height: 700px;
  }
  .ol-viewport{
  	height : 700px;
  }
  #sample-container{
  	height: 700px;
  } */
</style>
<body>
<div class="mapContainer1">
<div id ="map" style="height:550px;">
</div>
      <div class="cntnts contents_widgetBox">
        <div id="contents_widgetBox_widget01">
        </div>
        <div id="contents_widgetBox_widget02">
        </div>
        <div id="contents_widgetBox_widget03">
        </div>
      </div>
</div>

</body>

<script>

	/* 맵 타겟 */
	var mapContainer = document.getElementById('map');

	/* 맵 중심점 */
	var coord = new odf.Coordinate(209576.17356159375, 355040.40034509933);

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
    var mapOption = "::mapOpt::";
	/*
		* 배경지도 종류
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도

		* 프록시 사용
		proxyURL: 'proxy.jsp' 프록시 설정
	 */

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, mapOption);

	 var crtfckey = '::crtfckey::';
	  var userId = 'geontest8';

	  var smtClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
	    baseURL: '::smtAPI::',
	  });
	  var publishClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
	    baseURL: '::publishAPI::',
	  });
	  var addrgeoClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
	    baseURL: '::geocodingAPI::',
	  });
	  var mapClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
	    baseURL: '::mapAPI::',
	  });
	  var analysisClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
	    baseURL: '::analysisAPI::',
	  });
	  var cqlInfoApi = oui.CqlInfoApi(smtClient, { userMapId: 'UM0000000061', userId: 'geontest8', crtfckey: '::crtfckey::' });
	  var layerDownloadApi = oui.LayerDownloadApi(analysisClient, { crtfckey: '::crtfckey::' });
	  var layerApi = oui.LayerApi(smtClient, {
	    userId: userId,
	    userMapId: 'UM0000000061',
	    crtfckey: '::crtfckey::',
	    groupCode: 'MPD003'
	  });
	  var mapApi = oui.MapApi(mapClient);
	  var columnInfoApi = oui.ColumnInfoApi(smtClient, { lyrId: '', userId: 'geontest8', crtfckey: '::crtfckey::' });
	  var commonCodeApi = oui.CommonCodeApi(smtClient, { crtfckey: '::crtfckey::' });
	  var noticeApi = oui.NoticeApi(analysisClient , {crtfckey: '::crtfckey::'});
	  var uploadApi = oui.UploadApi(publishClient, {
	    sendOpertNtcnInfo: (opertNtcnInfo) => {
	      var opertProcessSeCode = '1'
	      var opertNtcnSn = opertNtcnInfo.opertNtcnSn;
	      var intervalFunc = setInterval(()=> {
	    	  noticeApi.selectNotice({opertNtcnSn  : opertNtcnInfo.opertNtcnSn }, (result)=>{
	    		  if(result.opertProcessSeCode == '5'){
	    			  clearInterval(intervalFunc);
	    		      var resultLayer = odf.LayerFactory.produce('geoserver', {
	    		    	  method : 'get',
	    		    	  server : '::wfsAPI::',
	    		    	  layer : `${result.layers[0].typeName}`,
	    		    	  service : 'wfs',
	    		      });
	    		      resultLayer.setMap(map);
	    		      spatialAnalysisWidget.removeGrid();
	    		  }
		      })
	      }, 3000)
	    },
	    userId: userId,
	    crtfckey: '::crtfckey::'
	  });
	  var geocodingApi = oui.GeocodingApi(addrgeoClient, {
	    sendOpertNtcnInfo: (opertNtcnInfo) => {
	      console.dir(opertNtcnInfo)
	    },
	    crtfckey: '::crtfckey::',
	  });
	  var layerUploadCodeApi = oui.CommonCodeApi(smtClient, {
	    paramList: [
	      { params: { groupCode: 'MPD003' }, type: 'detail' },
	      { params: { groupCode: 'MPD007' }, type: 'detail' },
	      { params: { groupCode: 'MPD025' }, type: 'detail' },
	    ],
	    categoryId: 'layerUpload',
	    crtfckey: '::crtfckey::'
	  });
 //
 ////레이어 추가 함수
 //
 var spatialLayerList =  [
	    {
	        "mapUrl": null,
	        "lyrGroupSeCode": "02",
	        "title": "제주보전산지",
	        "contentId": "odf-layer-vector1664502905353qecg3sqijb",
	        "odfLayerId": "odf-layer-vector1664502905353qecg3sqijb",
	        "cntntsId": "L100000428",
	        "upperGroupId": null,
	        "linkedLayer": odf.LayerFactory.produce('geoserver', {
	        	method : 'get',
	        	layer : 'Wgeontest8:L100000428',
	        	service : 'wfs',
	        	server : '::WfsAPI::',
	        }),
	        "layerId": "LR0000000267",
	        "onOffAt": null,
	        "style": {
	            "isTypeStyleFieldLimit": false,
	            "labelFlag": false,
	            "targetLayer": {
	                "Layer": true
	            },
	            "targetLayerService": "vector",
	            "originStyleObject": {
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    19,
	                                    134,
	                                    44,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": "#000000",
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            19,
	                                            134,
	                                            44,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "nowStyleObject": {
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    19,
	                                    134,
	                                    44,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": "#000000",
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            19,
	                                            134,
	                                            44,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "previewStyleObject": {
	                "option": {
	                    "useManualEditing": true
	                },
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    19,
	                                    134,
	                                    44,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": "#000000",
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            19,
	                                            134,
	                                            44,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "geometryType": "polygon",
	            "targetLayerId": "LR0000000267",
	            "useMultiStyle": true
	        },
	        "filter": null,
	        "attributes": [],
	        "jobClCode": "01",
	        "lyrClCode": "MPD013",
	        "lyrClSeCode": "01",
	        "svcTySeCode": "F",
	        "popup": {
	            "originPopupObject": {
	                "layerPopupEstbs": null,
	                "detailList": [
	                    {
	                        "columnNm": "ALIAS",
	                        "columnOrdr": 0,
	                        "indictAt": "Y",
	                        "columnNcm": "a2"
	                    },
	                    {
	                        "columnNm": "MNUM",
	                        "columnOrdr": 1,
	                        "indictAt": "Y",
	                        "columnNcm": "a1"
	                    },
	                    {
	                        "columnNm": "REMARK",
	                        "columnOrdr": 2,
	                        "indictAt": "Y",
	                        "columnNcm": "a3"
	                    },
	                    {
	                        "columnNm": "NTFDATE",
	                        "columnOrdr": 3,
	                        "indictAt": "Y",
	                        "columnNcm": "NTFDATE"
	                    },
	                    {
	                        "columnNm": "SGG_OID",
	                        "columnOrdr": 4,
	                        "indictAt": "Y",
	                        "columnNcm": "SGG_OID"
	                    },
	                    {
	                        "columnNm": "COL_ADM_SE",
	                        "columnOrdr": 5,
	                        "indictAt": "Y",
	                        "columnNcm": "COL_ADM_SE"
	                    }
	                ]
	            },
	            "nowPopupObject": {
	                "layerPopupEstbs": null,
	                "detailList": [
	                    {
	                        "columnNm": "ALIAS",
	                        "columnOrdr": 0,
	                        "indictAt": "Y",
	                        "columnNcm": "a2"
	                    },
	                    {
	                        "columnNm": "MNUM",
	                        "columnOrdr": 1,
	                        "indictAt": "Y",
	                        "columnNcm": "a1"
	                    },
	                    {
	                        "columnNm": "REMARK",
	                        "columnOrdr": 2,
	                        "indictAt": "Y",
	                        "columnNcm": "a3"
	                    },
	                    {
	                        "columnNm": "NTFDATE",
	                        "columnOrdr": 3,
	                        "indictAt": "Y",
	                        "columnNcm": "NTFDATE"
	                    },
	                    {
	                        "columnNm": "SGG_OID",
	                        "columnOrdr": 4,
	                        "indictAt": "Y",
	                        "columnNcm": "SGG_OID"
	                    },
	                    {
	                        "columnNm": "COL_ADM_SE",
	                        "columnOrdr": 5,
	                        "indictAt": "Y",
	                        "columnNcm": "COL_ADM_SE"
	                    }
	                ]
	            },
	            "layerInfo": {
	                "layerObject": {
	                    "Layer": true
	                },
	                "layerId": "LR0000000267",
	                "lyrGroupSn": 1,
	                "layerNm": "제주보전산지",
	                "layerCallType": "1"
	            }
	        },
	        "lyrGroupSn": 1,
	        "registerId": "geontest8",
	        "lyrTySeCode": "3",
	        "expanded": true,
	        "toc": {
	            "popupSet": true,
	            "setGroupName": true,
	            "setLayerNcm": true,
	            "delete": true,
	            "icon": true,
	            "setViisible": true,
	            "setLabel": true,
	            "attributeGrid": true,
	            "layerDetail": true,
	            "styleSet": true
	        }
	    },
	    {
	        "mapUrl": null,
	        "lyrGroupSeCode": "02",
	        "title": "(결과) 제주 119안전센터 현황_전체추출",
	        "contentId": "odf-layer-vector1664502905671tmd1tr6uqp",
	        "odfLayerId": "odf-layer-vector1664502905671tmd1tr6uqp",
	        "cntntsId": "L100000263",
	        "upperGroupId": null,
	        "linkedLayer":odf.LayerFactory.produce('geoserver', {
	        	method : 'get',
	        	layer : 'Wgeontest8:L100000263',
	        	service : 'wfs',
	        	server : '::WfsAPI::',
	        }),
	        "layerId": "LR0000000178",
	        "onOffAt": null,
	        "style": {
	            "isTypeStyleFieldLimit": false,
	            "labelFlag": false,
	            "targetLayer": {
	                "Layer": true
	            },
	            "targetLayerService": "vector",
	            "originStyleObject": {
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    144,
	                                    169,
	                                    220,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": "#000000",
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            144,
	                                            169,
	                                            220,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "nowStyleObject": {
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    144,
	                                    169,
	                                    220,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": "#000000",
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            144,
	                                            169,
	                                            220,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "previewStyleObject": {
	                "option": {
	                    "useManualEditing": true
	                },
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    144,
	                                    169,
	                                    220,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": "#000000",
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            144,
	                                            169,
	                                            220,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "geometryType": "point",
	            "targetLayerId": "LR0000000178",
	            "useMultiStyle": true
	        },
	        "filter": null,
	        "attributes": [],
	        "jobClCode": "01",
	        "lyrClCode": "MPD013",
	        "lyrClSeCode": "03",
	        "svcTySeCode": "F",
	        "popup": {
	            "originPopupObject": {
	                "layerPopupEstbs": null,
	                "detailList": [
	                    {
	                        "columnNm": "fid",
	                        "columnOrdr": 1,
	                        "indictAt": "Y",
	                        "columnNcm": "fid"
	                    },
	                    {
	                        "columnNm": "경위도X좌표",
	                        "columnOrdr": 1,
	                        "indictAt": "Y",
	                        "columnNcm": "경위도X좌표"
	                    },
	                    {
	                        "columnNm": "경위도Y좌표",
	                        "columnOrdr": 2,
	                        "indictAt": "Y",
	                        "columnNcm": "경위도Y좌표"
	                    },
	                    {
	                        "columnNm": "정제도로명주소",
	                        "columnOrdr": 3,
	                        "indictAt": "Y",
	                        "columnNcm": "정제도로명주소"
	                    },
	                    {
	                        "columnNm": "정제지번주소",
	                        "columnOrdr": 4,
	                        "indictAt": "Y",
	                        "columnNcm": "정제지번주소"
	                    },
	                    {
	                        "columnNm": "정제지번PNU",
	                        "columnOrdr": 5,
	                        "indictAt": "Y",
	                        "columnNcm": "정제지번PNU"
	                    },
	                    {
	                        "columnNm": "순번",
	                        "columnOrdr": 6,
	                        "indictAt": "Y",
	                        "columnNcm": "순번"
	                    },
	                    {
	                        "columnNm": "시도본부",
	                        "columnOrdr": 7,
	                        "indictAt": "Y",
	                        "columnNcm": "시도본부"
	                    },
	                    {
	                        "columnNm": "소방서",
	                        "columnOrdr": 8,
	                        "indictAt": "Y",
	                        "columnNcm": "소방서"
	                    },
	                    {
	                        "columnNm": "안전센터명",
	                        "columnOrdr": 9,
	                        "indictAt": "Y",
	                        "columnNcm": "안전센터명"
	                    },
	                    {
	                        "columnNm": "주소",
	                        "columnOrdr": 10,
	                        "indictAt": "Y",
	                        "columnNcm": "주소"
	                    },
	                    {
	                        "columnNm": "전화번호",
	                        "columnOrdr": 11,
	                        "indictAt": "Y",
	                        "columnNcm": "전화번호"
	                    }
	                ]
	            },
	            "nowPopupObject": {
	                "layerPopupEstbs": null,
	                "detailList": [
	                    {
	                        "columnNm": "fid",
	                        "columnOrdr": 1,
	                        "indictAt": "Y",
	                        "columnNcm": "fid"
	                    },
	                    {
	                        "columnNm": "경위도X좌표",
	                        "columnOrdr": 1,
	                        "indictAt": "Y",
	                        "columnNcm": "경위도X좌표"
	                    },
	                    {
	                        "columnNm": "경위도Y좌표",
	                        "columnOrdr": 2,
	                        "indictAt": "Y",
	                        "columnNcm": "경위도Y좌표"
	                    },
	                    {
	                        "columnNm": "정제도로명주소",
	                        "columnOrdr": 3,
	                        "indictAt": "Y",
	                        "columnNcm": "정제도로명주소"
	                    },
	                    {
	                        "columnNm": "정제지번주소",
	                        "columnOrdr": 4,
	                        "indictAt": "Y",
	                        "columnNcm": "정제지번주소"
	                    },
	                    {
	                        "columnNm": "정제지번PNU",
	                        "columnOrdr": 5,
	                        "indictAt": "Y",
	                        "columnNcm": "정제지번PNU"
	                    },
	                    {
	                        "columnNm": "순번",
	                        "columnOrdr": 6,
	                        "indictAt": "Y",
	                        "columnNcm": "순번"
	                    },
	                    {
	                        "columnNm": "시도본부",
	                        "columnOrdr": 7,
	                        "indictAt": "Y",
	                        "columnNcm": "시도본부"
	                    },
	                    {
	                        "columnNm": "소방서",
	                        "columnOrdr": 8,
	                        "indictAt": "Y",
	                        "columnNcm": "소방서"
	                    },
	                    {
	                        "columnNm": "안전센터명",
	                        "columnOrdr": 9,
	                        "indictAt": "Y",
	                        "columnNcm": "안전센터명"
	                    },
	                    {
	                        "columnNm": "주소",
	                        "columnOrdr": 10,
	                        "indictAt": "Y",
	                        "columnNcm": "주소"
	                    },
	                    {
	                        "columnNm": "전화번호",
	                        "columnOrdr": 11,
	                        "indictAt": "Y",
	                        "columnNcm": "전화번호"
	                    }
	                ]
	            },
	            "layerInfo": {
	                "layerObject": {
	                    "Layer": true
	                },
	                "layerId": "LR0000000178",
	                "lyrGroupSn": 2,
	                "layerNm": "(결과) 제주 119안전센터 현황_전체추출",
	                "layerCallType": "1"
	            }
	        },
	        "lyrGroupSn": 2,
	        "registerId": "geontest8",
	        "lyrTySeCode": "1",
	        "expanded": true,
	        "toc": {
	            "popupSet": true,
	            "setGroupName": true,
	            "setLayerNcm": true,
	            "delete": true,
	            "icon": true,
	            "setViisible": true,
	            "setLabel": true,
	            "attributeGrid": true,
	            "layerDetail": true,
	            "styleSet": true
	        }
	    },
	    {
	        "mapUrl": null,
	        "lyrGroupSeCode": "02",
	        "title": "[선]서울_자전거길_4326_EUC-KR",
	        "contentId": "odf-layer-vector16645029057410428fgi9ejv",
	        "odfLayerId": "odf-layer-vector16645029057410428fgi9ejv",
	        "cntntsId": "L100000325",
	        "upperGroupId": null,
	        "linkedLayer": odf.LayerFactory.produce('geoserver', {
	        	method : 'get',
	        	layer : 'Wgeontest8:L100000325',
	        	service : 'wfs',
	        	server : '::WfsAPI::',
	        }),
	        "layerId": "LR0000000224",
	        "onOffAt": null,
	        "style": {
	            "isTypeStyleFieldLimit": false,
	            "labelFlag": false,
	            "targetLayer": {
	                "Layer": true
	            },
	            "targetLayerService": "vector",
	            "originStyleObject": {
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    56,
	                                    193,
	                                    139,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": [
	                                    56,
	                                    193,
	                                    139,
	                                    1
	                                ],
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            56,
	                                            193,
	                                            139,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "nowStyleObject": {
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    56,
	                                    193,
	                                    139,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": [
	                                    56,
	                                    193,
	                                    139,
	                                    1
	                                ],
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            56,
	                                            193,
	                                            139,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "previewStyleObject": {
	                "option": {
	                    "useManualEditing": true
	                },
	                "styleObject": [
	                    {
	                        "seperatorFunc": "default",
	                        "style": {
	                            "geometryType": "free",
	                            "name": "기본 스타일",
	                            "fill": {
	                                "color": [
	                                    56,
	                                    193,
	                                    139,
	                                    1
	                                ]
	                            },
	                            "stroke": {
	                                "color": [
	                                    56,
	                                    193,
	                                    139,
	                                    1
	                                ],
	                                "width": 1
	                            },
	                            "image": {
	                                "circle": {
	                                    "radius": 5,
	                                    "snapToPixel": true,
	                                    "fill": {
	                                        "color": [
	                                            56,
	                                            193,
	                                            139,
	                                            1
	                                        ]
	                                    },
	                                    "stroke": {
	                                        "color": "#000000",
	                                        "width": 1
	                                    }
	                                }
	                            }
	                        }
	                    }
	                ],
	                "opacity": 1
	            },
	            "geometryType": "line",
	            "targetLayerId": "LR0000000224",
	            "useMultiStyle": true
	        },
	        "filter": null,
	        "attributes": [],
	        "jobClCode": "01",
	        "lyrClCode": "MPD013",
	        "lyrClSeCode": "01",
	        "svcTySeCode": "F",
	        "popup": {
	            "originPopupObject": {
	                "layerPopupEstbs": null,
	                "detailList": [
	                    {
	                        "columnNm": "OBJECTID",
	                        "columnOrdr": 1,
	                        "indictAt": "Y",
	                        "columnNcm": "OBJECTID"
	                    },
	                    {
	                        "columnNm": "LINK_ID",
	                        "columnOrdr": 2,
	                        "indictAt": "Y",
	                        "columnNcm": "LINK_ID"
	                    },
	                    {
	                        "columnNm": "ST_ND_ID",
	                        "columnOrdr": 3,
	                        "indictAt": "Y",
	                        "columnNcm": "ST_ND_ID"
	                    },
	                    {
	                        "columnNm": "ED_ND_ID",
	                        "columnOrdr": 4,
	                        "indictAt": "Y",
	                        "columnNcm": "ED_ND_ID"
	                    },
	                    {
	                        "columnNm": "LENGTH",
	                        "columnOrdr": 5,
	                        "indictAt": "Y",
	                        "columnNcm": "LENGTH"
	                    },
	                    {
	                        "columnNm": "ST_DIR",
	                        "columnOrdr": 6,
	                        "indictAt": "Y",
	                        "columnNcm": "ST_DIR"
	                    },
	                    {
	                        "columnNm": "ED_DIR",
	                        "columnOrdr": 7,
	                        "indictAt": "Y",
	                        "columnNcm": "ED_DIR"
	                    },
	                    {
	                        "columnNm": "LINK_CATE",
	                        "columnOrdr": 8,
	                        "indictAt": "Y",
	                        "columnNcm": "LINK_CATE"
	                    },
	                    {
	                        "columnNm": "ROAD_CATE",
	                        "columnOrdr": 9,
	                        "indictAt": "Y",
	                        "columnNcm": "ROAD_CATE"
	                    },
	                    {
	                        "columnNm": "LINK_CATE2",
	                        "columnOrdr": 10,
	                        "indictAt": "Y",
	                        "columnNcm": "LINK_CATE2"
	                    },
	                    {
	                        "columnNm": "ROAD_NO",
	                        "columnOrdr": 11,
	                        "indictAt": "Y",
	                        "columnNcm": "ROAD_NO"
	                    },
	                    {
	                        "columnNm": "ONEWAY",
	                        "columnOrdr": 12,
	                        "indictAt": "Y",
	                        "columnNcm": "ONEWAY"
	                    },
	                    {
	                        "columnNm": "LANE",
	                        "columnOrdr": 13,
	                        "indictAt": "Y",
	                        "columnNcm": "LANE"
	                    },
	                    {
	                        "columnNm": "ROAD_NAME",
	                        "columnOrdr": 14,
	                        "indictAt": "Y",
	                        "columnNcm": "ROAD_NAME"
	                    },
	                    {
	                        "columnNm": "ALTITUDE",
	                        "columnOrdr": 15,
	                        "indictAt": "Y",
	                        "columnNcm": "ALTITUDE"
	                    },
	                    {
	                        "columnNm": "TRACK",
	                        "columnOrdr": 16,
	                        "indictAt": "Y",
	                        "columnNcm": "TRACK"
	                    },
	                    {
	                        "columnNm": "SHAPE_LEN",
	                        "columnOrdr": 17,
	                        "indictAt": "Y",
	                        "columnNcm": "SHAPE_LEN"
	                    }
	                ]
	            },
	            "nowPopupObject": {
	                "layerPopupEstbs": null,
	                "detailList": [
	                    {
	                        "columnNm": "OBJECTID",
	                        "columnOrdr": 1,
	                        "indictAt": "Y",
	                        "columnNcm": "OBJECTID"
	                    },
	                    {
	                        "columnNm": "LINK_ID",
	                        "columnOrdr": 2,
	                        "indictAt": "Y",
	                        "columnNcm": "LINK_ID"
	                    },
	                    {
	                        "columnNm": "ST_ND_ID",
	                        "columnOrdr": 3,
	                        "indictAt": "Y",
	                        "columnNcm": "ST_ND_ID"
	                    },
	                    {
	                        "columnNm": "ED_ND_ID",
	                        "columnOrdr": 4,
	                        "indictAt": "Y",
	                        "columnNcm": "ED_ND_ID"
	                    },
	                    {
	                        "columnNm": "LENGTH",
	                        "columnOrdr": 5,
	                        "indictAt": "Y",
	                        "columnNcm": "LENGTH"
	                    },
	                    {
	                        "columnNm": "ST_DIR",
	                        "columnOrdr": 6,
	                        "indictAt": "Y",
	                        "columnNcm": "ST_DIR"
	                    },
	                    {
	                        "columnNm": "ED_DIR",
	                        "columnOrdr": 7,
	                        "indictAt": "Y",
	                        "columnNcm": "ED_DIR"
	                    },
	                    {
	                        "columnNm": "LINK_CATE",
	                        "columnOrdr": 8,
	                        "indictAt": "Y",
	                        "columnNcm": "LINK_CATE"
	                    },
	                    {
	                        "columnNm": "ROAD_CATE",
	                        "columnOrdr": 9,
	                        "indictAt": "Y",
	                        "columnNcm": "ROAD_CATE"
	                    },
	                    {
	                        "columnNm": "LINK_CATE2",
	                        "columnOrdr": 10,
	                        "indictAt": "Y",
	                        "columnNcm": "LINK_CATE2"
	                    },
	                    {
	                        "columnNm": "ROAD_NO",
	                        "columnOrdr": 11,
	                        "indictAt": "Y",
	                        "columnNcm": "ROAD_NO"
	                    },
	                    {
	                        "columnNm": "ONEWAY",
	                        "columnOrdr": 12,
	                        "indictAt": "Y",
	                        "columnNcm": "ONEWAY"
	                    },
	                    {
	                        "columnNm": "LANE",
	                        "columnOrdr": 13,
	                        "indictAt": "Y",
	                        "columnNcm": "LANE"
	                    },
	                    {
	                        "columnNm": "ROAD_NAME",
	                        "columnOrdr": 14,
	                        "indictAt": "Y",
	                        "columnNcm": "ROAD_NAME"
	                    },
	                    {
	                        "columnNm": "ALTITUDE",
	                        "columnOrdr": 15,
	                        "indictAt": "Y",
	                        "columnNcm": "ALTITUDE"
	                    },
	                    {
	                        "columnNm": "TRACK",
	                        "columnOrdr": 16,
	                        "indictAt": "Y",
	                        "columnNcm": "TRACK"
	                    },
	                    {
	                        "columnNm": "SHAPE_LEN",
	                        "columnOrdr": 17,
	                        "indictAt": "Y",
	                        "columnNcm": "SHAPE_LEN"
	                    }
	                ]
	            },
	            "layerInfo": {
	                "layerObject": {
	                    "Layer": true
	                },
	                "layerId": "LR0000000224",
	                "lyrGroupSn": 3,
	                "layerNm": "[선]서울_자전거길_4326_EUC-KR",
	                "layerCallType": "1"
	            }
	        },
	        "lyrGroupSn": 3,
	        "registerId": "geontest8",
	        "lyrTySeCode": "2",
	        "expanded": true,
	        "toc": {
	            "popupSet": true,
	            "setGroupName": true,
	            "setLayerNcm": true,
	            "delete": true,
	            "icon": true,
	            "setViisible": true,
	            "setLabel": true,
	            "attributeGrid": true,
	            "layerDetail": true,
	            "styleSet": true
	        }
	    }
	];

 ////////////////////////TOC 및 레이어 검색(추가)에 필요한 함수 구간////////////////////////


 var addressApi = oui.AddressApi(oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
   baseURL: '::geocodingAPI::',
 }), {
   projection: '5186',
   crtfckey : '::crtfckey::'
 });

 //행정구역 검색  api
 var administApi = oui.AdministApi(oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
   baseURL: '::geocodingAPI::',
 }), {
   projection: '5186',//사용 좌표계
   crtfckey : '::crtfckey::'
 });

 var analysisApi = oui.AnalysisApi(oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
   baseURL: '::analysisAPI::',
 }), {
   projection: '5186',
   crtfckey : '::crtfckey::',
   //작업일련번호를 리턴해줘야할 함수
   sendOpertNtcnInfo: ({ opertNtcnSn/*작업알림번호*/, lyrNm/*레이어명*/, lyrDc/*레이어 설명*/ }) => {
     console.log(opertNtcnSn, layerName, layerDescription);
   }
 });


 var coordApi = oui.CoordApi(oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
   baseURL: '::coordAPI::',
 }));

 var spatialAnalysisWidget = new oui.SpatialAnalysisWidget({
   odf,
   target: document.querySelector('#contents_widgetBox_widget01'),
   options: {
     //바로발행 디폴트값
     publishDirect: false,
     //분석 상세 정보를 표현할 element
     detailTarget: document.querySelector('#contents_widgetBox_widget02'),
     //분석 결과 grid를 표출할 element
     gridTarget: document.querySelector('#contents_widgetBox_widget03'),
     //분석 선택시 호출 callback
     selectAnalysis: function(){console.log('분석 위젯')},
     //프로그래스 알림
     alertList: {
       startLoadingBar: (message) => {
         console.log(message);
         //callLoadingBar({message : "레이어를 업로드중입니다.",status : true});
       },
       endLoadingBar: (message) => {
         console.log(message);
         //callLoadingBar({status : false});
       },
       customAlert: (message) => {
         alert(message);
       }
     },
     validationCheck: {
       //바로 발행 권한이 있는지 check function, true 또는 false 반환
       publishDirect: () => {
         let reVal = {
           validationState: true,
           message: '',
         };

         if (1 === 2) {
           reVal.validationState = false;
           reVal.message = '바로 발행을 하기 위해서는 웹맵을 저장해야합니다.';
         }

         return reVal;
       }
     },
     isUse : {
    	 publishDirectElement : {
    		  ag: true,//데이터 요약분석 - 포인트 집계
    	       join: true,//데이터 요약분석 - 조인 피처
    	       nrby: true,//데이터 요약분석 - 주변 요약
    	       range: true,//데이터 요약분석 - 범위 내 요약
    	       center: true,//데이터 요약분석 - 중심 및 분산 요약
    	       searchLegacy: true,//위치 찾기 분석 - 기존 위치 찾기
    	       searchNew: true,//위치 찾기 분석 - 새 위치 파생 분석
    	       searchCenter: true,//위치 찾기 분석 - 중심 찾기
    	       searchSimilar: true,//위치 찾기 분석 - 유사한 위치 찾기
    	       density: true,//공간 패턴 분석 - 밀도맵
    	       hotspot: true,//공간 패턴 분석 - 핫스팟 찾기
    	       gatherPoints: true,//공간 패턴 분석 - 군집 찾기
    	       interpolatePoints: true,//공간 패턴 분석 - 내삽 기능
    	       searchOutliers: true,//공간 패턴 분석 - 이상치 찾기 분석
    	       connectDestination: true,//근접도 분석 - 출발지와 목적지 연결
    	       buffer: true,//근접도 분석 - 버퍼 생성
    	       drivingArea: true,//근접도 분석 - 운전시간 영역 생성
    	       findNearestPoint: true,//근접도 분석 - 최근접 위치 찾기
    	       findPath: true,//근접도 분석 - 경로 계획
    	       dsslve: true,//데이터 관리 분석 - 경계 디졸브 실행
    	       extrc: true,//데이터 관리 분석 - 데이터 추출 기능
    	       dvsion: true,//데이터 관리 분석 - 공간 분할
    	       merge: true,//데이터 관리 분석 - 레이어 병합
    	       'ovrlay/erase': true,//데이터 관리 분석 - 레이어 중첩(지우기)
    	       'ovrlay/intsct': true,//데이터 관리 분석 - 레이어 중첩(교차)
    	       'ovrlay/union': true,//데이터 관리 분석 - 레이어 중첩(유니온)
    	       clustering: true,//데이터 관리 분석 - 클러스터링
    	       ar: true,//데이터 관리 분석 - 면적 계산
    	       lt: true,//데이터 관리 분석 - 길이 계산
    	       file: true,//좌표변환 - 파일 좌표 변환
    	       single: true//좌표변환 - 단일 좌표 및 도분초 변환
    	 },
     },
     //분석 사용 여부 정의
     spatialAnalysis: {
       ag: true,//데이터 요약분석 - 포인트 집계
       join: true,//데이터 요약분석 - 조인 피처
       nrby: true,//데이터 요약분석 - 주변 요약
       range: true,//데이터 요약분석 - 범위 내 요약
       center: true,//데이터 요약분석 - 중심 및 분산 요약
       searchLegacy: true,//위치 찾기 분석 - 기존 위치 찾기
       searchNew: true,//위치 찾기 분석 - 새 위치 파생 분석
       searchCenter: true,//위치 찾기 분석 - 중심 찾기
       searchSimilar: true,//위치 찾기 분석 - 유사한 위치 찾기
       density: true,//공간 패턴 분석 - 밀도맵
       hotspot: true,//공간 패턴 분석 - 핫스팟 찾기
       gatherPoints: true,//공간 패턴 분석 - 군집 찾기
       interpolatePoints: true,//공간 패턴 분석 - 내삽 기능
       searchOutliers: true,//공간 패턴 분석 - 이상치 찾기 분석
       connectDestination: true,//근접도 분석 - 출발지와 목적지 연결
       buffer: true,//근접도 분석 - 버퍼 생성
       drivingArea: true,//근접도 분석 - 운전시간 영역 생성
       findNearestPoint: true,//근접도 분석 - 최근접 위치 찾기
       findPath: true,//근접도 분석 - 경로 계획
       dsslve: true,//데이터 관리 분석 - 경계 디졸브 실행
       extrc: true,//데이터 관리 분석 - 데이터 추출 기능
       dvsion: true,//데이터 관리 분석 - 공간 분할
       merge: true,//데이터 관리 분석 - 레이어 병합
       'ovrlay/erase': true,//데이터 관리 분석 - 레이어 중첩(지우기)
       'ovrlay/intsct': true,//데이터 관리 분석 - 레이어 중첩(교차)
       'ovrlay/union': true,//데이터 관리 분석 - 레이어 중첩(유니온)
       clustering: true,//데이터 관리 분석 - 클러스터링
       ar: true,//데이터 관리 분석 - 면적 계산
       lt: true,//데이터 관리 분석 - 길이 계산
       file: true,//좌표변환 - 파일 좌표 변환
       single: true//좌표변환 - 단일 좌표 및 도분초 변환
     }
   },
   api:
   {
     //레이어 업로드 api
     publishGeojsonLayer: uploadApi.publishGeojsonLayer,
     //주소 검색 api
     addressSearch: addressApi.intSearch,
     //시도 목록 조회 function
     ctpvAdministrativeDistrictSearch: administApi.ctpvAdministrativeDistrictSearch,
     //시군구 목록 조회 function
     sggAdministrativeDistrictSearch: administApi.sggAdministrativeDistrictSearch,
     //읍면동 목록 조회 function
     emdAdministrativeDistrictSearch: administApi.emdAdministrativeDistrictSearch,
     //리 목록 조회 function
     //liAdministrativeDistrictSearch: administApi.liAdministrativeDistrictSearch,

     //단건 행정구역 정보 조회  function
     geometrySearch: administApi.geometrySearch,
     //별칭 및 컬럼 정보 조회
     selectColumn: columnInfoApi.selectColumn,
     //filter 정보 조회
     selectFilter: (params/* {} */, callback) => {

			var layerList = spatialLayerList;

			if (layerList.length <= 0) {
				callback({ flterCndCn: undefined });
				return;
			}

			var filteredLayerList = layerList.filter(layer => layer.layerId === params.lyrId);
			if (filteredLayerList.length > 0) {
				callback({
					flterCndCn: filteredLayerList[0].filter
				});
			}
		},
     //공간분석 관련 api 사용
     runAnalysis: analysisApi.runAnalysis,
     //좌표변환 관련 api 사용
     convertCoord: coordApi.convertCoord,
     //특정 레이어 목록 조회(toc 에서 레이어 목록 조회)
     getLayerList: (params/*{} */, callback) => {
       var layerList = spatialLayerList;
       //toc에서 레이어 리스트 뽑아서 vector레이어이면서 cluster레이어가 아닌 경우 추출
       let vectorLayerList = layerList.filter(layer => {
         if (layer.odfLayerId.includes('vector')) {
           if (layer.linkedLayer.getInitialOption().params.service === 'cluster') {
             return false;
           }
           return true;
         }
       });
       let filteredLayerList;
       //필터링 함수가 정의되어있다면 필터링하여 레이어 반환
       if (params.geometryType) {
         filteredLayerList = vectorLayerList.filter(layer => {
           let geometryType = layer.linkedLayer.getAttributes(['geometry'])[0].geometryType.toLowerCase().replace('multi', '');
           if (params.geometryType.includes(geometryType)) {
             return true;
           } else {
             return false;
           }
         });
       } else {
         filteredLayerList = vectorLayerList
       }
       let reVal = filteredLayerList.map(layer => {
         return {
           layerId: layer.layerId,
           lyrGroupSn: layer.lyrGroupSn,
           lyrNm: layer.title,
           typeName: layer.linkedLayer.getInitialOption().params.layer,
           geometryType: layer.lyrTySeCode === '1' ? 'point' : layer.lyrTySeCode === '2' ? 'line' : 'polygon',
         };
       })

       callback(reVal);
     },
   }
 });
 _spatialAnalysisWidget = spatialAnalysisWidget;
 spatialAnalysisWidget.addTo(map);

</script>
</html>
