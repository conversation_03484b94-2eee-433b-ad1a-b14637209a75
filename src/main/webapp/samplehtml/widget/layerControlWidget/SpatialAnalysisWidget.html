<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8" />
	<link href="::OdfUrl::/odf.css" rel="stylesheet" />
	<link href="::OuiUrl::/oui.css" rel="stylesheet" />
	<link href="::SmtUrl::/css/widgets/spatialAnalysis.css" rel="stylesheet" />

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<style>
	.contents_widgetBox {
		position: absolute;
		top: 0px;
		background-color: white;
		height: 95%;
		width: 300px;
    }
    #contents_widgetBox_widget02 {
		position: absolute;
		left: 295px;
		width: 345px;
		top: 0px;
		background-color: white;
		overflow-y: auto;
		max-height: 650px;
    }
    #contents_widgetBox_widget03 {
		position: absolute;
		bottom: 0px;
		background-color: white;
		width: 1100px;
    }
</style>
<body>
	<div id="map" style="height:550px;"></div>
	<p>분석에 사용되는 레이어는 지도상에 점레이어(주황색), 선레이어(초록색),
      면레이어(빨간색)으로 추가되어 있습니다.</p>
    <div class="cntnts contents_widgetBox">
		<div id="contents_widgetBox_widget01"></div>
		<div id="contents_widgetBox_widget02"></div>
		<div id="contents_widgetBox_widget03"></div>
    </div>
  </body>
  <script>

    /* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
    var mapContainer = document.getElementById('map');
    var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
    var map = new odf.Map(mapContainer, mapOption);

    /* 테스트 레이어 생성  */
    // 아래 샘플 레이어들의 공간 좌표 체계가 'EPSG:5186' 으로 정의되어 있기 때문에 5179 좌표 체계에선 정상동작하지 않습니다.
    // 분석에 사용되는 레이어는 분석 결과 레이어와 구분되기 위해 스타일을 정의했습니다. (점레이어 : 주황색, 선레이어 : 초록색, 면레이어: 빨간색)
    var wfsPolygonLayer =  odf.LayerFactory.produce('geoserver', {
    	method : 'get',
    	layer : '::testPolygonLayer1::',
   		service : 'wfs',
    	server : '::WfsAPI::',
    });
    var polygonStyle = odf.StyleFactory.produce({
    	fill : {
			color : [255, 0, 0, 0.2]
		},
    	stroke : {
			color : [0, 0, 0, 0.2],
			width : 1
		}
    })
    wfsPolygonLayer.setStyle(polygonStyle)
    wfsPolygonLayer.setMap(map);
    wfsPolygonLayer.fit();
    var wfsPointLayer = odf.LayerFactory.produce('geoserver', {
    	method : 'get',
    	layer : '::testPointLayer1::',
     	service : 'wfs',
    	server : '::WfsAPI::',
    });
    var wfsLineLayer =  odf.LayerFactory.produce('geoserver', {
    	method : 'get',
    	layer : '::testLineLayer1::',
   		service : 'wfs',
    	server : '::WfsAPI::',
    })
    var lineStyle = odf.StyleFactory.produce({
    	stroke : {
			color : [0, 255, 0, 0.5],
			width : 5
       	}
    })
    wfsLineLayer.setStyle(lineStyle);
    wfsLineLayer.setMap(map);
    var pointStyle = odf.StyleFactory.produce({
    	image : {
    		circle : {
    			radius : 5,//크기
    			fill : {
    				color : [ 255, 165, 0, 0.4 ]
    			//채우기 색
    			},//채우기
    			stroke : {//윤곽선
    				color : [ 0, 0, 0, 0.4 ],//테두리 색
    				width : 1,//굵기
    			}
   			}
    	}
    })
    wfsPointLayer.setStyle(pointStyle);
    wfsPointLayer.setMap(map);

    /* 분석을 테스트 하기 위해 사용되는 레이어 정보 리스트 */
    var spatialLayerList =  [
        {
            "lyrNm": "::testPointLayer1Nm::",
            "typeName" : '::testPointLayer1::',
            "linkedLayer": wfsPointLayer,
           	"layerId": '::testPointLayer1Id::'
        },
        {
            "lyrNm": "::testLineLayer1Nm::",
            "typeName" : '::testLineLayer1::',
            "linkedLayer": wfsLineLayer,
            "layerId": '::testLineLayer1Id::'
        },
        {
            "lyrNm": "::testPolygonLayer1Nm::",
            "typeName" : '::testPolygonLayer1::',
            "linkedLayer": wfsPolygonLayer,
            "layerId": '::testPolygonLayer1Id::'
		}
    ];

	/* oui api 연결 객체 생성  */
   	var smtClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::',
   	});
    var publishClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::publishAPI::',
   	});
    var addrgeoClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
    	baseURL: '::geocodingAPI::',
    });
    var mapClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
    	baseURL: '::mapAPI::',
    });
    var analysisClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
    	baseURL: '::analysisAPI::',
    });
    var columnInfoApi = oui.ColumnInfoApi(smtClient, {
    	userId: '::userId::',
    	crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			selectColumn : 3000,
		}
		 */
   	});
    var noticeApi = oui.NoticeApi(analysisClient , {
    	crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			uploadWebLayer : 3000,
		}
		 */
    });
    var uploadApi = oui.UploadApi(publishClient, {
		sendOpertNtcnInfo: (opertNtcnInfo) => {
			var opertProcessSeCode = '1'
			var opertNtcnSn = opertNtcnInfo.opertNtcnSn;
			var intervalFunc = setInterval(()=> {
	   			noticeApi.selectNotice({opertNtcnSn  : opertNtcnInfo.opertNtcnSn }, (result)=>{
					//작업번호가 5인 경우 레이어 발행이 성공 됐다는 것.
					if(result.opertProcessSeCode == '5'){
						clearInterval(intervalFunc);
						//발행된 레이어르 지도에 추가
						var resultLayer = odf.LayerFactory.produce('geoserver', {
							method : 'get',
							server : '::wfsAPI::',
							layer : `${result.layers[0].typeName}`,
							service : 'wfs',
						});
		   		      	resultLayer.setMap(map);
		   		      	spatialAnalysisWidget.removeGrid();
					}
				})
			}, 3000)
   		},
  		userId: '::userId::',
  		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			publishGeojsonLayer : 3000,
		}
		 */
    });
    var addressApi = oui.AddressApi(addrgeoClient, {
		projection: '::srid::',
		crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			intSearch : 3000,
		}
		 */
    });
    //행정구역 검색  api
    var administApi = oui.AdministApi(addrgeoClient, {
    	projection: '::srid::',//사용 좌표계
    	crtfckey : '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			ctpvAdministrativeDistrictSearch : 3000,
			sggAdministrativeDistrictSearch : 3000,
			emdAdministrativeDistrictSearch : 3000,
			geometrySearch : 3000,
		}
		 */
    });
    var analysisApi = oui.AnalysisApi(analysisClient, {
		projection: '::srid::',
		crtfckey : '::crtfckey::',
		//작업일련번호를 리턴해줘야할 함수
		sendOpertNtcnInfo: ({ opertNtcnSn/*작업알림번호*/, lyrNm/*레이어명*/, lyrDc/*레이어 설명*/ }) => {
			console.log(opertNtcnSn, layerName, layerDescription);
		},
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			runAnalysis : 3000,
			// 분석 api 별 timeout 설정
			//runAnalysis : {
			//	ag:3000,// 포인트집계 분석(ag) 요청 타임아웃 설정
			//	join:3000,// 공간조인 분석(join) 요청 타임아웃 설정
			//	nrby:3000,// 주변 집계 분석(nrby) 요청 타임아웃 설정
			//	range:3000,// 영역 내 집계 분석(range) 요청 타임아웃 설정
			//	center:3000,// 공간분포 패턴 분석(center) 요청 타임아웃 설정
			//	searchLegacy:3000,// 공간 조건 검색 분석(searchLegacy) 요청 타임아웃 설정
			//	searchNew:3000,// 공간 조건 추출 분석(searchNew) 요청 타임아웃 설정
			//	searchCenter:3000,// 중심찾기 분석(searchCenter) 요청 타임아웃 설정
			//	searchSimilar:3000,// 유사한 위치 찾기 분석(searchSimilar) 요청 타임아웃 설정
			//	density:3000,// 밀도 분석(density) 요청 타임아웃 설정
			//	hotspot:3000,// 핫 스팟 분석(Getis-Ord Gi*)(hotspot) 요청 타임아웃 설정
			//	gatherPoints:3000,// 포인트 군집 분석(gatherPoints) 요청 타임아웃 설정
			//	interpolatePoints:3000,// 포인트 내삽 찾기 분석(interpolatePoints) 요청 타임아웃 설정
			//	searchOutliers:3000,// 이상치 찾기 분석(searchOutliers) 요청 타임아웃 설정
			//	connectDestination:3000,// 출발지와 목적지 연결 분석(connectDestination) 요청 타임아웃 설정
			//	buffer:3000,// 버퍼 분석(buffer) 요청 타임아웃 설정
			//	drivingArea:3000,// 운전시간 영역 생성 분석(drivingArea) 요청 타임아웃 설정
			//	findNearestPoint:3000,// 최근접 위치찾기 분석(findNearestPoint) 요청 타임아웃 설정
			//	findPath:3000,// 경로계획 분석(findPath) 요청 타임아웃 설정
			//	dsslve:3000,// 경계 디졸브 분석(dsslve) 요청 타임아웃 설정
			//	extrc:3000,// 데이터 추출 분석(extrc) 요청 타임아웃 설정
			//	dvsion:3000,// 공간 분할 생성 분석(dvsion) 요청 타임아웃 설정
			//	merge:3000,// 레이어 병합 분석(merge) 요청 타임아웃 설정
			//	'ovrlay/erase':3000,// 레이어 중첩(지우기) 분석(ovrlay/erase) 요청 타임아웃 설정
			//	'ovrlay/intsct':3000,// 레이어 중첩(교차) 분석(ovrlay/intsct) 요청 타임아웃 설정
			//	'ovrlay/union':3000,// 레이어 중첩(유니온) 분석(ovrlay/union) 요청 타임아웃 설정
			//	clustering:3000,// 클러스터링 분석(clustering) 요청 타임아웃 설정
			//	ar:3000,// 면적 계산 분석(ar) 요청 타임아웃 설정
			//	ag:3000,// 길이 계산 분석(ag) 요청 타임아웃 설정
			//	file:3000,// 파일 좌표 변환 분석(file) 요청 타임아웃 설정
			//	single:3000,// 단일 좌표 변환 분석(single) 요청 타임아웃 설정
			//},
		}
		 */
    });
    var coordApi = oui.CoordApi(oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::coordAPI::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			convertCoord : 3000,
		}
		 */
	}));

    /* 분석 위젯 생성 */
    var spatialAnalysisWidget = new oui.SpatialAnalysisWidget({
		odf,
		target: document.querySelector('#contents_widgetBox_widget01'),
		options: {
			//바로발행 디폴트값
			publishDirect: false,
			//분석 상세 정보를 표현할 element
			detailTarget: document.querySelector('#contents_widgetBox_widget02'),
			//분석 결과 grid를 표출할 element
			gridTarget: document.querySelector('#contents_widgetBox_widget03'),
			//분석 선택시 호출 callback
			selectAnalysis: function(){console.log('분석 위젯')},
			//프로그래스 알림
			alertList: {
				startLoadingBar: (message) => {
					console.log(message);
					//callLoadingBar({message : "레이어를 업로드중입니다.",status : true});
				},
				endLoadingBar: (message) => {
					console.log(message);
					//callLoadingBar({status : false});
				},
				customAlert: (message) => {
					alert(message);
				}
			},
			validationCheck: {
				//바로 발행 권한이 있는지 check function, true 또는 false 반환
				publishDirect: () => {
					let reVal = {
						validationState: true,
						message: '',
					};

					if (1 === 2) {
						reVal.validationState = false;
						reVal.message = '바로 발행을 하기 위해서는 웹맵을 저장해야합니다.';
					}

					return reVal;
				}
			},
			isUse : {
				publishDirectElement : {
					ag: true,//데이터 요약분석 - 포인트 집계
					join: true,//데이터 요약분석 - 조인 피처
					nrby: true,//데이터 요약분석 - 주변 요약
					range: true,//데이터 요약분석 - 범위 내 요약
					center: true,//데이터 요약분석 - 중심 및 분산 요약
					searchLegacy: true,//위치 찾기 분석 - 기존 위치 찾기
					searchNew: true,//위치 찾기 분석 - 새 위치 파생 분석
					searchCenter: true,//위치 찾기 분석 - 중심 찾기
					searchSimilar: true,//위치 찾기 분석 - 유사한 위치 찾기
					density: true,//공간 패턴 분석 - 밀도맵
					hotspot: true,//공간 패턴 분석 - 핫스팟 찾기
					gatherPoints: true,//공간 패턴 분석 - 군집 찾기
					interpolatePoints: true,//공간 패턴 분석 - 내삽 기능
					searchOutliers: true,//공간 패턴 분석 - 이상치 찾기 분석
					connectDestination: true,//근접도 분석 - 출발지와 목적지 연결
					buffer: true,//근접도 분석 - 버퍼 생성
					drivingArea: true,//근접도 분석 - 운전시간 영역 생성
					findNearestPoint: true,//근접도 분석 - 최근접 위치 찾기
					findPath: true,//근접도 분석 - 경로 계획
					dsslve: true,//데이터 관리 분석 - 경계 디졸브 실행
					extrc: true,//데이터 관리 분석 - 데이터 추출 기능
					dvsion: true,//데이터 관리 분석 - 공간 분할
					merge: true,//데이터 관리 분석 - 레이어 병합
					'ovrlay/erase': true,//데이터 관리 분석 - 레이어 중첩(지우기)
					'ovrlay/intsct': true,//데이터 관리 분석 - 레이어 중첩(교차)
					'ovrlay/union': true,//데이터 관리 분석 - 레이어 중첩(유니온)
					clustering: true,//데이터 관리 분석 - 클러스터링
					ar: true,//데이터 관리 분석 - 면적 계산
					lt: true,//데이터 관리 분석 - 길이 계산
					file: true,//좌표변환 - 파일 좌표 변환
					single: true//좌표변환 - 단일 좌표 및 도분초 변환
				},
			},
			//분석 사용 여부 정의
			spatialAnalysis: {
				ag: true,//데이터 요약분석 - 포인트 집계
				join: true,//데이터 요약분석 - 조인 피처
				nrby: true,//데이터 요약분석 - 주변 요약
				range: true,//데이터 요약분석 - 범위 내 요약
				center: true,//데이터 요약분석 - 중심 및 분산 요약
				searchLegacy: true,//위치 찾기 분석 - 기존 위치 찾기
				searchNew: true,//위치 찾기 분석 - 새 위치 파생 분석
				searchCenter: true,//위치 찾기 분석 - 중심 찾기
				searchSimilar: true,//위치 찾기 분석 - 유사한 위치 찾기
				density: true,//공간 패턴 분석 - 밀도맵
				hotspot: true,//공간 패턴 분석 - 핫스팟 찾기
				gatherPoints: true,//공간 패턴 분석 - 군집 찾기
				interpolatePoints: true,//공간 패턴 분석 - 내삽 기능
				searchOutliers: true,//공간 패턴 분석 - 이상치 찾기 분석
				connectDestination: true,//근접도 분석 - 출발지와 목적지 연결
				buffer: true,//근접도 분석 - 버퍼 생성
				drivingArea: true,//근접도 분석 - 운전시간 영역 생성
				findNearestPoint: true,//근접도 분석 - 최근접 위치 찾기
				findPath: true,//근접도 분석 - 경로 계획
				dsslve: true,//데이터 관리 분석 - 경계 디졸브 실행
				extrc: true,//데이터 관리 분석 - 데이터 추출 기능
				dvsion: true,//데이터 관리 분석 - 공간 분할
				merge: true,//데이터 관리 분석 - 레이어 병합
				'ovrlay/erase': true,//데이터 관리 분석 - 레이어 중첩(지우기)
				'ovrlay/intsct': true,//데이터 관리 분석 - 레이어 중첩(교차)
				'ovrlay/union': true,//데이터 관리 분석 - 레이어 중첩(유니온)
				clustering: true,//데이터 관리 분석 - 클러스터링
				ar: true,//데이터 관리 분석 - 면적 계산
				lt: true,//데이터 관리 분석 - 길이 계산
				file: true,//좌표변환 - 파일 좌표 변환
				single: true//좌표변환 - 단일 좌표 및 도분초 변환
			}
		},
		api: {
			//레이어 업로드 api
			publishGeojsonLayer: uploadApi.publishGeojsonLayer,
			//주소 검색 api
			addressSearch: addressApi.intSearch,
			//시도 목록 조회 function
			ctpvAdministrativeDistrictSearch: administApi.ctpvAdministrativeDistrictSearch,
			//시군구 목록 조회 function
			sggAdministrativeDistrictSearch: administApi.sggAdministrativeDistrictSearch,
			//읍면동 목록 조회 function
			emdAdministrativeDistrictSearch: administApi.emdAdministrativeDistrictSearch,
			//단건 행정구역 정보 조회  function
			geometrySearch: administApi.geometrySearch,
			//별칭 및 컬럼 정보 조회
			selectColumn: columnInfoApi.selectColumn,
			//filter 정보 조회
			selectFilter: (params/* {} */, callback) => {
	    		var layerList = spatialLayerList;
	    		if (layerList.length <= 0) {
	    			callback({ flterCndCn: undefined });
	    			return;
	    		}
	    		var filteredLayerList = layerList.filter(layer => layer.layerId === params.lyrId);
	    		if (filteredLayerList.length > 0) {
	    			callback({
	    				flterCndCn: filteredLayerList[0].filter
	    			});
	    		}
    		},
			//공간분석 관련 api 사용
			runAnalysis: analysisApi.runAnalysis,
			//좌표변환 관련 api 사용
			convertCoord: coordApi.convertCoord,
			//특정 레이어 목록 조회(toc 에서 레이어 목록 조회)
			getLayerList: (params/*{} */, callback) => {
				//레이어 리스트 뽑아서 vector레이어이면서 cluster레이어가 아닌 경우 추출
				let vectorLayerList = spatialLayerList.filter(layerInfo => {
					if (layerInfo.linkedLayer.getODFId().includes('vector')) {
						if (layerInfo.linkedLayer.getInitialOption().params.service === 'cluster') {
							return false;
						}
						return true;
					}
				});
          		let filteredLayerList;
          		//필터링 함수가 정의되어있다면 필터링하여 레이어 반환
				if (params.geometryType) {
					filteredLayerList = vectorLayerList.filter(layerInfo => {
						let geometryType = layerInfo.linkedLayer.getAttributes(['geometry'])[0].geometryType.toLowerCase().replace('multi', '');
						if (params.geometryType.includes(geometryType)) {
							return true;
						} else {
							return false;
						}
					});
				} else {
					filteredLayerList = vectorLayerList
				}
				let reVal = filteredLayerList.map(layerInfo => {
					return {
						layerId: layerInfo.layerId, //레이어 정보 테이블에서 사용하는 layerId
						lyrNm: layerInfo.lyrNm,     //레이어 명칭
						typeName: layerInfo.typeName, //레이어 typeName
						geometryType: layerInfo.linkedLayer.getAttributes(['geometry'])[0].geometryType.toLowerCase().replace('multi', '') //레이어 geometry정보 (point/line/polygon)
					};
				})
				callback(reVal);
			},
		}
    });
    _spatialAnalysisWidget = spatialAnalysisWidget;
    spatialAnalysisWidget.addTo(map);
  </script>
</html>
