<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/widgets/toc.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<style>
 .contents_widgetBox{
	position: absolute;
    top: 0px;
    background-color: white;
    height: 650px;
    width : 300px;
 }
 #contents_widgetBox_widget02{
 	position: absolute;
    left: 295px;
    width: 100%;
    top: 0px;
 }
  #contents_widgetBox_widget03{
 	position: absolute;
    bottom: 0px;
    background-color:white;
 	border-radius: 4px;
 }
 .grid_headerGrp{
 	width: 1000px;
 }
 .legend_frame{
    position: absolute;
    bottom: 20px;
    right: 20px;
    left: unset !important;
 }
</style>
<body>
	<div class="mapContainer1">
		<div id ="map" style="height:550px;"></div>
		<div class="cntnts contents_widgetBox">
			<div id="tocWidget" class="toc"></div>
			<div id="contents_widgetBox_widget02"></div>
			<div id="contents_widgetBox_widget03"></div>
		</div>
	</div>
	<button id="closeGridBtn" class="onoffOnlyBtn" onClick="tocWidget.removeGrid()">속성테이블 닫기</button>
</body>

<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 위젯에서 사용되는 공통 변수 생성*/
	var crtfckey = '::crtfckey::';
	var userId = '::userId::';

	/* oui api 연결 객체 생성  */
	var smtClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::',
	});
	var publishClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::publishAPI::',
	});
	var addrgeoClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::geocodingAPI::',
	});
	var mapClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::mapAPI::',
	});
	var analysisClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::analysisAPI::',
	});
	var tocApi = oui.TOCApi(smtClient, {
		userId: userId, crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getGroupId : 3000,
		}
		 */
	});
	var cqlInfoApi = oui.CqlInfoApi(smtClient, {
		userId: userId,
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			cqlInfoFunction : 3000,
			getOption : 3000,
		}
		 */
	});
	var layerDownloadApi = oui.LayerDownloadApi(analysisClient, {
		crtfckey: '::crtfckey::' ,
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			downloadLayer : 3000,
		}
		 */
	});
	var layerApi = oui.LayerApi(smtClient, {
		userId: userId,
		crtfckey: '::crtfckey::',
		groupCode: 'MPD003',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			selectPopupInfo : 3000,
			selectSymbol : 3000,
			insertSymbol : 3000,
			deleteSymbol : 3000,
			getLayerList : 3000,
			removeLayer : 3000,
			getLayerSearchTypeList : 3000,
			uploadWebLayer : 3000,
		}
		 */
	});
	var mapApi = oui.MapApi(mapClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getData : 3000,
			updateData : 3000,
		}
		 */

	});
	var columnInfoApi = oui.ColumnInfoApi(smtClient, {
		lyrId: '',
		userId: userId,
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			columnInfoFunction : 3000,
			selectColumn : 3000,
		}
		 */
	});
	var commonCodeApi = oui.CommonCodeApi(smtClient, {
		crtfckey: '::crtfckey::' ,
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			commonCodeFunction : 3000,
			getAllDetailCode : 3000,
		}
		 */

	});
	var uploadApi = oui.UploadApi(publishClient, {
		sendOpertNtcnInfo: (opertNtcnInfo) => {
			console.dir(opertNtcnInfo)
		},
		userId: userId,
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			publishFileLayer : 3000,
			publishDXFFile : 3000,
		}
		 */

	});
	var geocodingApi = oui.GeocodingApi(addrgeoClient, {
		sendOpertNtcnInfo: (opertNtcnInfo) => {
			console.dir(opertNtcnInfo)
		},
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			geocodingLayer : 3000,
		}
		 */
	});
	var layerUploadCodeApi = oui.CommonCodeApi(smtClient, {
		paramList: [
			{ params: { groupCode: 'MPD003' }, type: 'detail' },
			{ params: { groupCode: 'MPD007' }, type: 'detail' },
			{ params: { groupCode: 'MPD025' }, type: 'detail' },
		],
		categoryId: 'layerUpload',
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getAllDetailCode : 3000,
		}
		 */
	});

	/* toc 위젯 보조 함수 생성  */
	//레이어 정보를 파라미터로 받아 스타일을 적용후, odf 레이어 객체를 리턴
	var _getLayerObject = async (layerInfo) => {

		var { svcTySeCode = null, lyrGroupId, cntntsId, lyrClCode, lyrId, lyrTySeCode, lyrClSeCode, typeName, upperGroupId = null, lyrGroupSeCode, flterCndCn = null, title, symbolCndCn = null, popup = null, attributes = [], onOffAt = 'Y', holdDataSeCode, style, cntmSeCode } = layerInfo;
		//contetnId 넘어올 값 그룹의경우 'GR000' , 레이어의경우 'L10000'
		var refineContentInfo = {};
		//레이어의 경우
		var serviceType = (svcTySeCode != null ? (svcTySeCode == 'M' ? 'wms' : (svcTySeCode == 'T' ? 'wmts' : 'wfs')) : 'wfs');

		if (lyrClCode == 'MPD011' && lyrClSeCode == '04') {
			serviceType = "heatmap";
		} else if (lyrClCode == 'MPD011' && lyrClSeCode == '06') {
			serviceType = "cluster";
		} else if (lyrClCode == "MPD013" && lyrClSeCode == '06' && lyrTySeCode == '5') {
			serviceType = "group";
		}

		//해당 정보로 레이어 만들기['heatmap', 'cluster'
		var layer = odf.LayerFactory.produce('geoserver', { // 레이어 호출 방법 (ex. geoserver, geojson)
			method: 'post',
			server: `::mapAPI::/api/map/${['heatmap', 'cluster'].includes(serviceType) ? 'wfs' : (serviceType == "group" ? "wms" : serviceType)}`, // 레이어가 발행된 서버 주소
			layer: typeName, // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
			service: serviceType, // 호출하고자 하는 레이여 형태(wms, wfs, wmts)
			bbox: false,
			matrixSet: serviceType === 'wmts' ? `EPSG:${cntmSeCode}` : undefined,
			crtfckey: '::crtfckey::'
		});

		//스타일 값이 있을경우, 스타일 셋팅
		if (symbolCndCn) {
			var style = JSON.parse(symbolCndCn);

			if (['vector', 'cluster'].includes(style.serviceType)) {
				layer.setStyle(odf.StyleFactory.produceFunction(style.styleObject));
			} else if ('image' === style.serviceType) {
				layer.setSLD(odf.StyleFactory.produceSLD(style.styleObject));
			} else if ('heatmap' === style.serviceType) {
				layer.setBlur(style.styleObject.blur);
				layer.setGradient(style.styleObject.gradient);
				layer.setRadius(style.styleObject.radius);
				if (style.styleObject.maxResolution) {
					layer.setMaxResolution(style.styleObject.maxResolution);
				}
				if (style.styleObject.minResolution) {
					layer.setMinResolution(style.styleObject.minResolution);
				}
			}

			if (style.opacity) {
				layer.setOpacity(style.opacity);
			}
		} else if (serviceType === 'group') {
			//group레이어 스타일 적용
		} else if (style) {
			layer.setStyle(style);
		} else if (['MPD011', 'MPD012', 'MPD016', 'MPD017'].includes(lyrClCode)/*분석레이어 발행일 경우*/
			&& ['1', '2', '3'].includes(lyrTySeCode)/*점/선/면 타입(타일, GeoTIFF 타입 제외)*/) {

		} else if (symbolCndCn == null && serviceType == 'wfs') {
			//스타일이 없을 경우, 랜덤 스타일 지정
			var randomColor = odf.ColorFactory.produceRandomColor(1, 1)[0];
			//dxf로 발행된 레이어일 경우
			if (lyrClCode === 'MPD013' && lyrClSeCode === '06') {
				if (lyrTySeCode === '01'/*점*/) {
					layer.setStyle(odf.StyleFactory.produceFunction(`[{"seperatorFunc":"default","style":{"stroke":{},"image":{"circle":{"stroke":{"color":"#00000000","width":1},"radius":5,"fill":{"color":"#2BF92E00"}}},"text":{"fill":{"color":"#000000FF"},"stroke":{"color":"#FFFFFF00","width":1},"font":"normal normal 10px 굴림","placement":"point","offsetX":0,"offsetY":0,"overflow":true},"name":"기본 스타일"},"callbackFunc":"function anonymous(style, feature, resolution, produce) {/*oui style callbackFunc*//*oui wfs setText start*/style.getText().setText(String(feature.getProperties().Text));/*oui wfs setText end*/}","priority":99}]`));
				}
				else if (lyrTySeCode === '2'/*선*/) {
					layer.setStyle(odf.StyleFactory.produceFunction(`[{"seperatorFunc":"default","style":{"geometryType":"free","name":"기본 스타일","fill":{"color":[${randomColor.toString()}]},"stroke":{"color":[${randomColor.toString()}],"width":1},"image":{"circle":{"radius":5,"snapToPixel":true,"fill":{"color":[243,75,199,1]},"stroke":{"color":"#000000","width":1}}}},"priority":-1,"callbackFunc":"(t,e,i)=>{}"}]`));
				}
				else if (lyrTySeCode === '3'/*면*/) {
					layer.setStyle(odf.StyleFactory.produceFunction(`[{"seperatorFunc":"default","style":{"stroke":{"color":"#000000","width":1},"image":{},"text":{"fill":{"color":"#000000FF"},"stroke":{"color":"#FFFFFF00","width":2},"font":"normal normal 20px 굴림","placement":"point","offsetX":0,"offsetY":0,"overflow":true},"fill":{"color":"#6FB57C00"},"name":"기본 스타일"},"priority":99,"callbackFunc":"(style, feature, resolution) => {}"}]`));
				}
			} else {
				var geometryType = layer.getProperties().geometryType.toLowerCase();

				var styleOption = odf.StyleFactory.produce({
					geometryType: 'free',
					image: {
						circle: {
							radius: 5,
							fill: { color: randomColor },
							stroke: { color: '#000000', width: 1 },
						},
					},
					fill: { color: randomColor },
					stroke: { color: (geometryType.includes('linestring') ? randomColor : '#000000'), width: 1 },
				});
				layer.setStyle(styleOption);
			}
		}
//		layer.setMap(_map);
//		layer.fit();
		return layer;
	}

	// toc 스타일에서 사용하기위해, odf 레이어 객체를 파라미터로 받아 toc의 스타일 옵션객체 리턴
	var _getLayerStyleOption = async (layer) => {
		var _labelFlag = false;
		var _sourceOption = layer.getInitialOption();
		var _serviceType = '';
		var _style;
		var _opacity = layer.getOpacity();

		switch (_sourceOption.type) {
			case 'geoserver': _serviceType = _sourceOption.params.service; break;
			case 'kml': _serviceType = 'kml'; break;
			case 'csv': _serviceType = 'csv'; break;
			case 'api': _serviceType = _sourceOption.params.service; break;
			case 'empty': _serviceType = 'geojson'; break;
		}

		//api 타입은 wfs일때만 스타일 허용
		if (_sourceOption.type === 'api' && _serviceType !== 'wfs') {
			return undefined;
		}

		if (['wfs', 'kml', 'csv', 'geojson'].includes(_serviceType) || 'cluster' === _serviceType) {
			var sObj = layer.getStyle().getObject();
			if (sObj instanceof Array) {
				_style = sObj;
			} else {
				_style = [{
					seperatorFunc: 'default',
					style: layer.getStyle().getObject()
				}];
			}

		if ((_style[0].style.text && _style[0].style.text.text) ||
			(_style[0].callbackFunc && _style[0].callbackFunc.toString().includes('setText'))) {
			_labelFlag = true;
		}
		} else if (_serviceType === 'wms') {
			var _sld = layer.getSLD();
			if (!_sld) {
				_sld = await layer.getDefaultSLD();
			}
			_style = _sld.getObject();
			_style.rules.forEach(rule => {
				rule.symbolizers.forEach(symbolizer => {
					if (symbolizer.kind == 'Text' && symbolizer.label && symbolizer.label !== '') {
						_labelFlag = true;
					}
				})
			})
		} else if (_serviceType === 'heatmap') {
			_style = {
				blur: layer.getBlur(),
				gradient: layer.getGradient(),
				opacity: layer.getOpacity(),
				radius: layer.getRadius(),
			}
		} else if (_serviceType === 'group') {
			_style = {}
		}

		var _styleService = '';
		if (['heatmap', 'cluster'].includes(_serviceType)) {
			_styleService = _serviceType;
		}
		else if (['wfs', 'kml', 'csv', 'geojson'].includes(_serviceType)) {
			_styleService = 'vector';
		} else if ('wms' === _serviceType) {
			_styleService = 'image';
		} else if ('group' === _serviceType) {
			_styleService = 'group';
		}

		//임의  ==> 추후 레이어별 서버에서 어떤 타입인지 조회
		var _geometryType;


		if (_styleService === 'group') {
			_geometryType = 'group';
		} else if (!['heatmap', 'cluster'].includes(_styleService)) {
			if (layer.getAttributes && _sourceOption.type !== 'api') {
				_geometryType = layer.getAttributes(['geometry'])[0].geometryType.toLowerCase();
			} else if (_styleService === 'vector' && layer.getFeatures().length > 0) {
				_geometryType = layer.getFeatures()[0].getGeometry().getType().toLowerCase();
			} else if (_sourceOption.type === 'api' && _styleService === 'vector') {
				// web 레이어의 경우 점선면 정보를 사용자가 입력하는 것이기 때문에 잘못 입력할 가능성이 존재한다.
				// 때문에 getFeature로 실제 데이터를 가져와 geometryType을 확인하는데,
				// arcGIS에서 피쳐를 조회하는 방식이 아래와 다르다면, layer정보에 있는 점선면 정보를 조회하여 사용하는것도 하나의 방법이다.
				var { type, params } = _sourceOption;
				var param = {
					service: 'WFS',
					request: 'GetFeature',
					outputFormat: 'application/json',
					...params,
					maxfeatures: 1,
				};
				delete param.server;
				delete param.originalOption;
				var url = typeof params.server === 'string' ? params.server : params.server.url;

				var xhr = new XMLHttpRequest();
				xhr.onload = function (e) {
					if (this.status === 200) {
						var reData = JSON.parse(this.responseText);
						_geometryType = reData.features[0].geometry.type.toLowerCase();
					}
				};
				var paramString = '';
				Object.entries(param).forEach(([key, value]) => {
					paramString += `${key}=${value}&`;
				});

				xhr.open('get', `${url}?${paramString}`, false);
				xhr.send(undefined);
			}

			_geometryType = _geometryType.includes('point') ? 'point' : _geometryType;
			_geometryType = _geometryType.includes('line') ? 'line' : _geometryType;
			_geometryType = _geometryType.includes('polygon') ? 'polygon' : _geometryType;
		} else {
			_geometryType = _styleService;
		}

		return {
			labelFlag: _labelFlag,
			targetLayer: layer,
			targetLayerService: _styleService,//타겟 레이어 유형 'vector', 'image', 'cluster', 'hotspot'
			//초기화를 위해 DB에 저장된 스타일값
			originStyleObject: {
				styleObject: _style,
				opacity: _opacity
			},
			//수정중 스타일 값
			nowStyleObject: {
				styleObject: _style,
				opacity: _opacity
			},
			//미리보기 스타일 값
			previewStyleObject: {
				option: {
					type: undefined,
					useManualEditing: true,
					styleOption: undefined,
				},
				styleObject: _style,
				opacity: _opacity,
			},
			geometryType: _geometryType
		}
	}

	//파라미터로 받은 data 를 가지고 toc에 추가할수 있는 컨텐츠로 정제하여 리턴
	var _refineTocContentList = async (data) => {
		var result = [];
		for (var index = 0; index < data.length; index++) {
			var item = data[index];
			//그룹의 경우
			if (item.lyrGroupSeCode == "01") {
				result.push({
					lyrGroupSeCode: item.lyrGroupSeCode,
					contentId: item.lyrGroupId,
					title: item.lyrGroupNm,
					lyrGroupId: item.lyrGroupId, //lyrGroupId
					onOffAt: item.onOffAt,
					upperGroupId: item.upperGroupId,
					lyrGroupSn: item.lyrGroupSn,
					registerId: item.registerId,
				});
			} else if (item.lyrGroupSeCode == "02") {
				//레이어의 경우
				var _layer;
				var _layerCallType = '1';
				//웹레이어 추가 의 경우
				if (item.lyrClCode && item.lyrClCode == 'MPD013' && item.lyrClSeCode && item.lyrClSeCode == '11') {
					_layerCallType = '2';
					var param = JSON.parse(item.mapUrlParamtr);
					_layer = odf.LayerFactory.produce('api', {
						...param
					});

					if (item.svcTySeCode === 'F') {
						//저장된 스타일값 지정
						if (item.symbolCndCn) {
							var style = JSON.parse(item.symbolCndCn);
							_layer.setStyle(odf.StyleFactory.produceFunction(style.styleObject));
							if (style.opacity) {
								_layer.setOpacity(style.opacity);
							}
						} else {
							//스타일이 없을경우 랜덤 스타일 지정
							var geometryType = item.lyrTySeCode === '1' ? 'point' : item.lyrTySeCode === '2' ? 'linestring' : 'polygon';
							var randomColor = odf.ColorFactory.produceRandomColor(1, 1)[0];

							var styleOption = odf.StyleFactory.produce({
								geometryType: 'free',
								image: {
									circle: {
										radius: 5,
										fill: { color: randomColor },
										stroke: { color: '#000000', width: 1 },
									},
								},
								fill: { color: randomColor },
								stroke: { color: (geometryType.includes('linestring') ? randomColor : '#000000'), width: 1 },
							});
							_layer.setStyle(styleOption);
						}
					}
				} else {
					var _apiContent = await getContentDetail(item.cntntsId);
					item.typeName = _apiContent.result.lyrOpertSpcNm + ':' + _apiContent.result.cntntsId;
					_layer = await _getLayerObject(item);
				}
				var _style = undefined;

				if (!(item.lyrClCode === "MPD011" && item.lyrClSeCode === "15")//이상치 찾기의 경우 스타일 옵션 x
				  //&& !(item.lyrClCode === "MPD013" && item.lyrClSeCode === "11")//웹레이어의 경우 스타일 옵션 x
				  && ((item.svcTySeCode == 'F'
				    || (item.svcTySeCode == 'M' && !(item.lyrClCode === 'MPD013' && item.lyrClSeCode == '11'))
				  ))
				) {
					_style = await _getLayerStyleOption(_layer);
					_style.targetLayerId = item.lyrId;
					_style.useMultiStyle = true;
				}

				result.push({
					mapUrl: item.mapUrl,
					lyrGroupSeCode: item.lyrGroupSeCode,
					title: item.lyrNcm != null ? item.lyrNcm : item.lyrNm,
					contentId: _layer.getODFId(),
					odfLayerId: _layer.getODFId(),
					cntntsId: item.cntntsId,
					upperGroupId: item.upperGroupId,
					linkedLayer: _layer,
					layerId: item.lyrId,
					onOffAt: item.onOffAt,
					style: _style,
					filter: item.flterCndCn, //필터정보
					attributes: [],
					jobClCode: item.jobClCode,
					lyrClCode: item.lyrClCode,
					lyrClSeCode: item.lyrClSeCode,
					svcTySeCode: item.svcTySeCode,
					popup: {
						originPopupObject: item.popupEstbs,
						nowPopupObject: item.popupEstbs,
						layerInfo: { layerObject: _layer, layerId: item.lyrId, lyrGroupSn: item.lyrGroupSn, layerNm: item.lyrNm, layerCallType: _layerCallType }
					},
					lyrGroupSn: item.lyrGroupSn,
					registerId: item.registerId,
					lyrTySeCode: item.lyrTySeCode,
				});

			}
		}
		return result;
	};

	var getFeature = (param) => {
		const response = fetch(`::mapAPI::/api/map/wfs`, {
			method: 'post',
			headers: {
				'Content-Type': 'application/json',
			}
		});
		return response.then(res => res.json());
	}

	var getContentDetail = (cntntsId) => {
		const response = fetch(`::mapAPI::/layer/cn/select?cntntsId=${cntntsId}&crtfckey=::crtfckey::`);
		return response.then(res => res.json());
	}

	//레이어 검색 기능에서 추가[+] 클릭시 호출되는 함수
	var _getAddLayerInfo = function (layerInfo) {
		//레이어 추가
		layerInfo.lyrGroupSeCode = "02";
		_refineTocContentList([layerInfo]).then((data) => {
			data[0].linkedLayer.setMap(map);
			if (layerInfo.lyrClCode && layerInfo.lyrClSeCode && !(layerInfo.lyrClCode == 'MPD013' && layerInfo.lyrClSeCode == '11')) {
				data[0].linkedLayer.fit();
			}
			tocWidget.setContent(data[0]);
		});
	}

	/* toc 위젯 생성 */
	var tocWidget = new oui.TOCWidget({
		odf: odf,
		map: map,
		target: document.querySelector('#tocWidget'),
		api: {
			getGroupId: tocApi.getGroupId,
			//좌표계 리스트 조회
			getCommonCode: layerUploadCodeApi.getAllDetailCode,
		},
		options: {
			maxDepth: 3,
			toc : {
				layerUpload: false,
				addGroup: true,
				popupSet: false,
				deleteAll: true,
				setGroupName: true,
				setLayerNcm: true,
				delete: true,
				attributeGrid: true,
				legend:true,
			},
			//특정레이어 스타일 , popup 등 ,, toc 기능 활성화 및 비활성화
			// getIsActivateOption: () => {
			// 	//사용여부
			// 	return {
			// 		setLabel: { isActivateOption: false, condition: [{ layerId: "LR0000035026" }] }, //6800
			// 		styleSet: { isActivateOption: false, condition: [{ layerId: "LR0000035026" }] },
			// 		popupSet: { isActivateOption: false, condition: [{ layerId: "LR0000035251", title: "[면]제주도_서귀포시_읍면동" }, { jobClCode: "00" }] }  //서귀포읍면동
			// 		//popupSet : {isActivateOption : false, condition : [{jo: "LR0000034953"}]}  //서귀포읍면동
			// 	}
			// },
			// setContentsElement: (contentInfo) => {
			// 	return contentInfo.lyrClSeCode == '03' ? '<button>허허</button>' : null;
			// },
			callbackUpdateTocInfo: (updateInfo) => {
				console.dir(updateInfo);
			},
			validationCheck: {
				layerUpload: () => {
					// let _userId = userId;
					// let _mapId = userMapId;
					// let _message = "";

					// let _validationState = true;
					// if (_userId == "") {
					//   _validationState = false;
					//   _message = "로그인이 필요합니다.";
					// } else if (_mapId == "") {
					//   _validationState = false;
					//   _message = "웹맵을 저장해주세요.";
					// }
					// // 맵이 저장되어있고 유저 아이디가 있어야 업로드가능
					return { validationState: true, message: '' }
				}
			},
			alertList: {
				customAlert: (message) => {
					// (1)그룹추가시 사용
				},
				customErrorAlert: (message) => {}
			},
			groupHeight: 55,
			layerHeight: 75,
			// toc 상세 창 영역 지정
			layerDetailTargetElemnet: '#contents_widgetBox_widget02',
			// 상세 버튼 클릭시 호출
			setLayerDetail: (
					flag,
					/*
					 * true=>toc 상세 on,
					 * fasle=>toc 상세 off
					*/
					layerInfo
			) => {
				if (flag) {
					document.getElementById('contents_widgetBox_widget02').classList.add('active');
					if (document.getElementById('contents_widgetBox_widget02').classList.contains('hide')) {
						document.getElementById('contents_widgetBox_widget02').classList.remove('hide');
					}
				} else {
					document.getElementById('contents_widgetBox_widget02').classList.remove('active');
				}
				map.updateSize();
			},
			layerDeleteCallback: () => {
				document.querySelector('#contents_widgetBox_widget03').style.display = 'none';
				window.setTimeout(function () { map.updateSize() }, '100');
			},
			popup: {
				api: {
					// 팝업정보조회
					selectPopupInfo: layerApi.selectPopupInfo,
					// 별칭 및 컬럼 정보 조회
					columnInfoFunction: columnInfoApi.columnInfoFunction,
					// 공통코드조회
					getCommonCode: commonCodeApi.commonCodeFunction,
					// 상세공통코드 조회 aixos.all
					getAllDetailCode: commonCodeApi.getAllDetailCode
				},
				options: {
					alertList: {
						customAlert: (message) => {
						},
						customErrorAlert: (message) => {
						}
					}
				}
			},
			style: {
				options: {
					imageUrl: 'js/oui/images/widget',
					alertList: {
						customAlert: (message) => {
						  console.dir(message)
						},
						startLoadingBar: (message) => {
						  console.dir(message)
						},
						endLoadingBar: (message) => {
						  console.dir(message)
						},
						customErrorAlert: (message) => {
						  console.dir(message)
						}
					},
				},
				// api: userImageApi,
				api: {
					// 사용자정의 이미지 조회 function
					selectSymbol: layerApi.selectSymbol,
					// 사용자정의 이미지 추가 function
					insertSymbol: layerApi.insertSymbol,
					// 사용자정의 이미지 삭제 function
					deleteSymbol: layerApi.deleteSymbol,
					// 별칭 및 컬럼 정보 조회
					selectColumn: columnInfoApi.selectColumn,
				}
			},
			layerSearch: {
				options: {
					pageSize: 20,
					pageIndex: 1,
					getAddLayerInfo: _getAddLayerInfo, // 추가버튼
					// 클릭한
					// 레이어의
					// 정보,
					removeLayerCallback: ({ layerId }) => {
						// 레이어가 삭제된후에 실행되는 콜백함수
						// 다른작업이 필요없으면 사용하지 않아도되지만
						// toc갱신 및 지도 재작업이 필요한 경우 여기서 해주어야한다.
						// 0.콘텐츠제거
						tocWidget.removeContent([{ type: 'layerId', id: layerId }]);
						// 1.지도에서 제거
						tocWidget.getLayerList().forEach(item => {
							if (item.layerId == layerId) {
								map.removeLayer(item.odfLayerId);
							}
						})
					},
					alertList: {
						customAlert: (message) => {
						},
						customErrorAlert: (message) => {
						},
						// 사용자 정의 알림 메세지 정의
						customConfirm: (message, callback) => {
							// 확인창 띄우기
							callConfirm('해당 레이어를 삭제하시겠습니까?', '', function () {
								callback();
							});
						}
					}
				},
				api: {
					getLayerList: layerApi.getLayerList,
					removeLayer: layerApi.removeLayer,
					getNavInfo: function (callback) {
						var navList = [
							{
								title: '국가공간정보',
								options: { holdDataSeCode: '9' },
							},
							{
								title: '사용자데이터',
								options: { holdDataSeCode: '1' }
							}
						];
						callback({ navList: navList, initNavValue: '9' });
					},
					getTypeNavList: layerApi.getLayerSearchTypeList
				}
			},
			layerUpload: {
				options: {
					uploadTypeList: ["shp", "geocoding", "web", "dxf"],
					// uploadTypeList 아래 참고
					// 1 : 웹 레이어 업로드
					// 2 : 파일레이어 업로드
					// 3 : 엑셀 업로드
					// 4 : dxf 업로드
					// defaultUploadType : "shp" //처음 표출할 업로드 화면
					errorCallback: function (message) {
						callLoadingBar({ status: false });
					},
					webLayerOptions: {
						proxyURL: "::DeveloperUrl::/proxyUrl.jsp",
						proxyParam: "url"
					},
					alertList: {
						startLoadingBar: (message) => {

						},
						endLoadingBar: (message) => {

						},
						customAlert: (message) => {

						},
						customErrorAlert: (message) => {

						}
					},
					geocodingOptions: {
						//size : {xlsx : 1, xls : 1 , txt : 0.1, csv :0.2},
						async: true,
						targetSrid: ::srid::
					},
					shpUploadOptions: {
						//size : {zip : 1},
						//동기 발행 클릭 시 타는 함수
						onClickPublish: function () {
						}
					},
					dxfFileUploadOptions: {
						//size : {dxf : 1},
						//동기 발행 클릭 시 타는 함수
						onClickPublish: function () {
						}
					}
				},
				api: {
					publishFileLayer: uploadApi.publishFileLayer,
					geocodingLayer: geocodingApi.geocodingLayer,
					uploadWebLayer: layerApi.uploadWebLayer,
					publishDXFFile: uploadApi.publishDXFFile,
					getCommonCode: layerUploadCodeApi.getAllDetailCode
				}
			},
			grid: {
				options: {
					gridCallback: function (e) {
						// smt 프로젝트 내에서 처리할 dom event들을 callback으로
						// 받아서 oui 에서 처리
						document.querySelector('#contents_widgetBox_widget03').style.display = 'block';
						window.setTimeout(function () { map.updateSize() }, '100');
			        },
					alertList: {
						customAlert: (message) => {
						},
						customErrorAlert: (type, message) => {
						},
						startLoadingBar: (message) => {
						},
						// 사용자 정의 로딩바 종료 function
						endLoadingBar: (message) => {
						},
					},
					pagination: true,
					pageSize: 100,
					rowSelection: 'multiple',
					sortable: true,
					filter: true,
					mode: 'layer',
					gridHeight: '300px',
					gridWidth: '1050px',
					cellWidth: '',
					createOption: {
						chart: true, // 차트 위젯 생성여부
						geomSearch: true, // 공간검색 사용여부
						attributeEditor: true, // 속성설정
						// 사용여부
						editMode: true, // 편집모드
						// 사용여부
						modify: true, // 피쳐편집 사용여부
						filter: true, // 조건식편집기 사용여부
						export: true, // 추출 사용여부
						delete: true, // 피쳐삭제 사용여부
						insert: true, // 피쳐 추가 사용여부
						clear: true, // 초기화 버튼 사용여부
						close : true, //속성테이블닫기버튼
					},
					conditionFilterOption: {
						thema: 'table', // 조건식편집기 thema
					},
					attributeEditorOption: {

					},
					chartOption: {
						// chartCreateMode : 'show',
						// createObject : {
						// title : '차트타이틀',
						// chartType : 'piechart',
						// targetCol : '읍면동',
						// value : 'base_year',
						// targetElement : '',
						// width : '1000px',
						// height : '600px',
						// },
						label: false,
						legend: true,
						legendPosition: 'right',
						// customColor : {
						//     colorList : [[255,219,219],[255,191,191],[255,159,159],[255,128,128],[255,96,96],[255,64,64],[255,32,32],[255,0,0]],
						//     colorLegend :  [3,24.714285714285715,46.42857142857143,68.14285714285714,89.85714285714286,111.57142857142858,133.2857142857143,155],
						// },
						applyCallback: (object) => {
							console.log(object)
						},
						getData: (obj) => {
							console.log(obj)
						}
					},
				},
				api: {
					// 데이터 조회 (mode에 따라 layer(feature 정보),
					// object(일반 json 정보))
					// 지오서버 데이터 조회
					getData: mapApi.getData,
					// 지오서버 업로드
					updateData: mapApi.updateData,
					// 공통코드조회
					getCommonCode: commonCodeApi.commonCodeFunction,
					// 상세공통코드 조회 aixos.all
					getAllDetailCode: commonCodeApi.getAllDetailCode,
					// 별칭 및 컬럼 정보 조회
					columnInfoFunction: columnInfoApi.columnInfoFunction,
					// 컬럼정보조회 옵션값 변경
					columnInfoOptionChange: columnInfoApi.changeOption,
					// 레이어 다운로드 api
					downloadLayer: layerDownloadApi.downloadLayer,
					// //cql 정보 조회
					cqlInfoFunction: cqlInfoApi.cqlInfoFunction,
					// //cql 옵션 변경
					cqlInfoOptionChange: cqlInfoApi.changeOption,
					// //cql 옵션 조회
					getCqlInfoOption: cqlInfoApi.getOption,
				},
				target: document.querySelector('#contents_widgetBox_widget03'),
			},
	  	}
	});
	tocWidget.addTo(map);

	/*toc에 팝업 활성화 옵션을 사용할 경우 추가해야하는 팝업 위젯 생성*/
	var popupWidget = new oui.PopupWidget({
		odf: odf,
		options: {
			draggable: true,
			getIsActivateOption: () => {
				//팝업 비활성화할 조건 입력
				return {
					popupDisplay: { isActivateOption: false, condition: [{ isPopupVisible: false}] }
				}
			},
			alertList: {
				customErrorAlert: (message) => {
					document.getElementsByClassName('textBox_textArea')[0].innerHTML = message;
				}
			}
		},
		api: {
			// 별칭 및 컬럼 정보 조회
			columnInfoFunction: columnInfoApi.columnInfoFunction,
			// 컬럼정보조회 옵션값 변경
			columnInfoOptionChange: columnInfoApi.changeOption,
			// 상세공통코드 조회 aixos.all
			getAllDetailCode: commonCodeApi.getAllDetailCode,
			getLayerList: (callback) => {
				callback(tocWidget.getLayerList());
			}
		}
	});
	popupWidget.addTo(map);

</script>
</html>
