<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<p style="margin-top:10px;">피쳐를 클릭하여 피쳐정보를 확인한 뒤 조건식 편집기를 사용해보기</p>
	<div class="conditionFilterWidget" id="conditionFilterWidget">
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* 테스트 레이어 추가  */
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::testPointLayer1::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();


	/* oui api 연결 객체 생성  */
	var smtApiClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::', //api 주소
	});
	var mapApiClient =  oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::mapAPI::', //api 주소
	});
	var commonCodeApi = oui.CommonCodeApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			commonCodeFunction : 3000,
			getAllDetailCode : 3000,
		}
		 */
	});
	var columnInfoApi = oui.ColumnInfoApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		//lyrId : '::testPointLayer1Id::'
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			columnInfoFunction : 3000,
			changeOption : 3000,
		}
		 */
	});
	var cqlInfoApi = oui.CqlInfoApi(smtApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			cqlInfoFunction : 3000,
			changeOption : 3000,
		}
		 */
	});
	var mapApi = oui.MapApi(mapApiClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getData : 3000,
			updateData : 3000,
		}
		 */
	});

	/* 조건식 편집기 위젯의 data 파라미터 생성  */
    // data는 배열형태의 ['컬럼명1' : {columnNm : '원본컬럼명' , columnNcm : '컬럼별칭명', type : '컬럼데이터타입'}] 형태로 입력
    var layerAttribute = wfsLayer.getAttributes().filter((col) => col.type != 'geometry');
    var typeofCol = [];
    Object.keys(layerAttribute).forEach((e, i) => { // odf에서 조회한 컬럼정보
        typeofCol[layerAttribute[e].name] = { columnNm: layerAttribute[e].name, columnNcm: layerAttribute[e].name, type: layerAttribute[e].type }
    });

    /* 조건식 편집기 위젯 생성  */
	//[★중요★]  조건식 편집기의 입력값을 직접입력하는 것이 아닌 selectbox로 존재하는 데이터들중 선택하도록 하려면
	//	oui.ColumnInfoApi에 파라미터로 lyrId에 해당 layer의 id를 입력한뒤, 위젯 옵션중 mode를 'codeFlterSet'으로 변경한다.
    var conditionFilterWidget = new oui.ConditionFilterWidget({
		layer: wfsLayer,
		data : typeofCol,
		loadData : '',
		options: {
   			mode : 'default',	//mode 기본으로 사용하는 'default' 와 'codeFlterSet'
			thema : 'table',
	        applyCallback : (cql) => {
	            console.log(cql);
	        },
	        header : false,
	        width : 815,
		},
		api: {//데이터 조회 (mode에 따라 layer(feature 정보), geocoding(지오코딩발행조건식편집기) ,object(일반 json 정보))
			//지오서버 데이터 조회
			getData: mapApi.getData,
			//지오서버 업로드
			updateData: mapApi.updateData,
			//공통코드조회
			getCommonCode: commonCodeApi.commonCodeFunction,
			//상세공통코드 조회 aixos.all
			getAllDetailCode: commonCodeApi.getAllDetailCode,
			//별칭 및 컬럼 정보 조회
			columnInfoFunction: columnInfoApi.columnInfoFunction,
			//컬럼정보조회 옵션값 변경
			columnInfoOptionChange: columnInfoApi.changeOption,
			// cql 정보 조회
			cqlInfoFunction: cqlInfoApi.cqlInfoFunction,
			// cql 옵션 변경
			cqlInfoOptionChange: cqlInfoApi.changeOption,
		},
		target: document.getElementById('conditionFilterWidget'),
	});
	conditionFilterWidget.addTo(map);

	//조건식 편집기를 사용하기 위해서 피쳐속성을 알아야하는데, 피쳐속성을 알아보기 위해 팝업위젯 추가
	//레이어의 피쳐를 클릭하면 피쳐정보를 확인하여 조건식 편집기를 사용해보기.
	var popupWidget = new oui.PopupWidget({
		odf: odf
	});
	popupWidget.addTo(map);

</script>
</html>
