<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id ="map" style="height:550px;"></div>
	<p>점 레이어는 팝업이 활성화되지 않도록 레이어 필터링 적용(geonserver에 발행된 레이어만 필터링 가능)</p>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* 테스트 레이어 생성 */
	var wfsPolygonLayer =  odf.LayerFactory.produce('geoserver', {
		method : 'get',
		layer : '::testPolygonLayer1::',
 		service : 'wfs',
		server : '::WfsAPI::',
	});
	var polygonStyle = odf.StyleFactory.produce({
		fill : {
			color : [255, 0, 0, 0.2]
			},
		stroke : {
            color : [0, 0, 0, 0.2],
            width : 1
      	}
	})
	wfsPolygonLayer.setStyle(polygonStyle)
	wfsPolygonLayer.setMap(map);
	wfsPolygonLayer.fit();
	var wfsPointLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		layer : '::testPointLayer1::',
	 	service : 'wfs',
		server : '::WfsAPI::',
	});
	var pointStyle = odf.StyleFactory.produce({
		image : {
			circle : {
				radius : 5,//크기
				fill : {
					color : [ 255, 165, 0, 0.4 ]
				//채우기 색
				},//채우기
				stroke : {//윤곽선
					color : [ 0, 0, 0, 0.4 ],//테두리 색
					width : 1,//굵기
				}
				}
		}
	})
	wfsPointLayer.setStyle(pointStyle);
	wfsPointLayer.setMap(map);


	/* 팝업 위젯(레이어 필터링) 생성 */
	//팝업을 표출할 레이어를 필터링하여 속성팝업을 표출할 경우 (geoserver에 발행된 레이어만 팝업 표출 가능)
	var popupWidget = new oui.PopupWidget({
		odf: odf
		, options: {
			draggable: true, //팝업 위젯 드래그 사용 여부
			//팝업에 커스텀 html 추가
			getIsActivateOption: () => {
				//팝업 비활성화할 조건 입력
				//[★레이어 필터 방법2★] 레이어 활성화여부 필터 걸기
				/*
					popupDisplay : 팝업 표출 활성/비활성화
             		isActivateOption(활성화여부) : true(활성화)/false(비활성화)
               		condition 필터조건을 넣을 배열객체. 조건으로 사용할수 있는 값은 (getLayerList에서 넘겨줬던 레이어 목록의 레이어 객체의 키값들)
               		예시1) condition: [{ cntntsId: 'L100002857' }, { cntntsId: 'L100000275' }] -> cntntsId가 'L100002857' 이거나(or) 'L100000275' 인 경우 해당 레이어 속성팝업 비활성화
               		예시2) condition: [{ cntntsId: 'L100002857', title: '타이틀명' }] -> cntntsId가 'L100002857' 이면서(and) title이 '타이틀명'인 경우 해당 레이어 속성팝업 비활성화
           		*/
				return {
					popupDisplay: { isActivateOption: false, condition: [{ cntntsId: '::testPointLayer1::'.split(':')[1] }] }
				}
			},
			/*
			//팝업이 열릴때 호출되는 콜백함수
			callbackOpen :(popupInfo)=>{

			},
			*/
			/*
			// 팝업의 활성/비활성화 여부
			getIsActivateOption: () => {
				//popupDisplay : 팝업 표출 활성/비활성화
				//isActivateOption(활성화여부) : true(활성화)/false(비활성화)
				//condition 필터조건을 넣을 배열객체. 조건으로 사용할수 있는 값은 (getLayerList에서 넘겨줬던 레이어 목록의 레이어 객체의 키값들)
				//예시1) condition: [{ cntntsId: '[Geoserver 발행 id(a)]' }, { cntntsId: '[Geoserver 발행 id(b)]' }] -> cntntsId가 (a) 이거나(or) (b)인 경우 해당 레이어 속성팝업 비활성화
				//예시2) condition: [{ cntntsId: '[Geoserver 발행 id(a)]', title: '타이틀명' }] -> cntntsId가 (a) 이면서(and) title이 '타이틀명'인 경우 해당 레이어 속성팝업 비활성화

				return {
					popupDisplay: { isActivateOption: false, condition: [{ cntntsId: '[Geoserver 발행 id(a)]' }] }
				}
			},*/
			//하이라이트 기능 사용 여부(기본값 true)
			//useHilight :true,
			//하이라이트 레이어 스타일(미정의시 내부에서 정의한 기본 스타일 적용)
			styleObject: {
				image: {
					circle: {
						radius: 10,
						fill: { color: [255, 255, 255, 0] },
						stroke: { color: [237, 116, 116, 0.82], width: 2 },
					},
				},
				fill: { color: [255, 255, 255, 0] },
				stroke: { color: [237, 116, 116, 0.9], width: 4 },
			},
			//클릭 한 위치에 버퍼를 지정하여 팝업 조회 (단위:픽셀) (기본값 20) (getLayerList를 사용하고, geometryType이 점,선인 레이어만 적용됨)
			//pointBuffer : 20

		},
	    api: {
	      	//[★필수★] 지도에 표출되고 있는 레이어 목록을 넘기는 곳 (geonserver에 발행된 레이어면서, 조건을 걸어야할 경우 해당 함수에 레이어 리스트를 파라미터로 넘겨야함)
	      	//[중요] 지도에 맨위에 표출되고 있는 레이어 순서대로 layerList를 만들어야함
			getLayerList: (callback) => {
	        //[★레이어 필터 방법1★] 레이어 목록을 콜백함수에 넘길때, 비활성화할 레이어는 제외해서 넘기면 됨.
				let layerList = [
	          		//(필수)linkedLayer [odfLayerObject] : 레이어 객체
	          		//(필수)odfLayerId [String] : 레이어 마다 고유 id값 (새로고침때마다 ODFId는 계속바뀜) wfsLayer.getODFId()로 id 값 가져오기 가능
	          		//(필수)cntntsId [String] : geoserver에 발행된 cntntsId 값
	         		//(옵션)title [String] : 팝업제목 위치에 표출될 타이틀 값 (명시하지 않을 경우 레이어-1, 레이어-2..의 명칭으로 표출, 팝업 제목을 아예 표출하고 싶지않을 경우 빈값(title : '') 넘기면 됨 )
					{
						linkedLayer: wfsPointLayer, odfLayerId: wfsPointLayer.getODFId(), cntntsId: '::testPointLayer1::'.split(':')[1], title: '타이틀명1'
					},
					{
						linkedLayer: wfsPolygonLayer, odfLayerId: wfsPolygonLayer.getODFId(), cntntsId: '::testPolygonLayer1::'.split(':')[1], title: '타이틀명2'
					},
				];
	        		callback(layerList);
			}
	    }
	});
  	popupWidget.addTo(map);
</script>
</html>
