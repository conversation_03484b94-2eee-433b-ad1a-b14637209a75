<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id="map" style="height:550px;"></div>
	<div class="layerUploadWidget" id="layerUploadWidget"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 테스트용 사용자 아이디 생성  */
	var userId = '::userId::';

	/* oui api 연결 객체 생성  */
	var smtClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::',
	});
	var publishClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::publishAPI::',
	});
	var addrgeoClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::geocodingAPI::',
	});
	var layerApi = oui.LayerApi(smtClient, {
		userId: userId,
		crtfckey: '::crtfckey::',
		groupCode: 'MPD003',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			uploadWebLayer : 3000,
		}
		 */
	});
	var uploadApi = oui.UploadApi(publishClient, {
		sendOpertNtcnInfo: (opertNtcnInfo) => {
			console.dir(opertNtcnInfo)
		},
		userId: userId,
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			publishFileLayer : 3000,
			publishDXFFile : 3000,
		}
		 */
	});
	var geocodingApi = oui.GeocodingApi(addrgeoClient, {
		sendOpertNtcnInfo: (opertNtcnInfo) => {
			console.dir(opertNtcnInfo)
		},
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			geocodingLayer : 3000,
		}
		 */
	});
	var layerUploadCodeApi = oui.CommonCodeApi(smtClient, {
		paramList: [
			{ params: { groupCode: 'MPD003' }, type: 'detail' },
			{ params: { groupCode: 'MPD007' }, type: 'detail' },
			{ params: { groupCode: 'MPD025' }, type: 'detail' },
		],
		categoryId: 'layerUpload',
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getAllDetailCode : 3000,
		}
		 */
	});

	/* 레이어 업로드 위젯 생성 */
	var layerUploadWidget = new oui.LayerUploadWidget({
		target: document.querySelector('#layerUploadWidget'),
		options: {
			/*
				[위젯 주요 옵션 설명]
				uploadTypeList : 다양한 레이어 업로드 중, 사용하고자하는 업로드를 배열로 받습니다. 또한 배열의 순서대로 탭이 생성됩니다.
				- shp : shp zip 파일 업로드
				- geocoding : .xls/.xlsx/.csv/.txt 파일 업로드
				- dxf : .dxf 파일 업로드
				- web : url로 외부 레이어 업로드
				- custom : 사용자가 직접 생성하는 레이어
				- image : 사용자 이미지 업로드
				- geoTiff : geoTiff 업로드
				- netCdf : netCdf 업로드
			*/
			uploadTypeList: ["shp", "geocoding", "dxf"],
			/* uploadTypeName : 탭에 표출될 업로드 명칭*/
			uploadTypeName: {
				shp: 'SHP',
				geocoding: 'xlsx/xls/csv/txt',
				dxf: 'CAD(.DXF)',
				web: 'URL',
				custom: '직접 생성'
			},
		    errorCallback: function (message) {

		    },
	    	webLayerOptions: {
				proxyURL: '/builder/proxyUrl.jsp',
				proxyParam: "url"
			},
	    	alertList: {
				startLoadingBar: (message) => {
					console.dir('로딩시작');
				},
				endLoadingBar: (message) => {
					console.dir('로딩종료');
				},
				customAlert: (message) => {
					console.dir(message);
				},
				customErrorAlert: (message) => {
					console.dir(message);
				}
			},
	    	/* [uploadType]Options 객체는 업로드 유형별로 따로 제공하는 옵션을 설정할 수있다. */
	    	geocodingOptions: {
		    	async: true,
		    	targetSrid: ::srid::
	    	},
	    	shpUploadOptions: { //shp 파일 업로드 옵션
    			// serviceTpye의 종류
	    		// 'wms' : 레이어 발행 후 지도상에 wms레이어로 추가
	    		// 'wfs' : 레이어 발행 후 지도상에 wms레이어로 추가
	    		// 'all'[기본값] : 업로드 위젯에서 wms/wfs 선택할 수있는 체크 박스 제공
	    		serviceType: 'wms',
	        	//동기 발행 클릭 시 타는 함수
	        	onClickPublish: function () {
	        		console.dir('shp업로드 발행버튼을 클릭하였습니다.')
	        	}
	    	},
	    	dxfFileUploadOptions: { //dxf 파일 업로드 옵션
	        	//size : {dxf : 1},
	        	//동기 발행 클릭 시 타는 함수
	        	onClickPublish: function () {
        			console.dir('dxf업로드 발행버튼을 클릭하였습니다.')
	        	}
	    	},
			dxfFileUploadOptions: { //dxf 파일 업로드 옵션
				//size : 1,
				//동기 발행 클릭 시 타는 함수
				onClickPublish: function () {
					callLoadingBar({ status: true, message: "레이어를 업로드중입니다" });
				},
				serviceType: 'wms' // all/wfs/wms 값 사용 가능, default는 all(wms, wfs 둘다 사용)
			},
			geoTiffUploadOptions: { //geoTiff 업로드 옵션
				onClickPublish: function () {
					callLoadingBar({ status: true, message: "레이어를 업로드중입니다" });
				}
			},
			customUploadOptions: { //사용자 레이어 업로드 옵션
				serviceType: 'wms', // all/wfs/wms 값 사용 가능, default는 all(wms, wfs 둘다 사용)
				targetSrid: '::srid::', //5186, 5174 등 targetSrid 선언 안하면 셀렉트박스 생김
				mapApiClient: '::mapAPI::' + '/api/map/wfs',
				crtfckey: '::crtfckey::',
				onClickPublish: function () {
					callLoadingBar({ status: true, message: "레이어를 업로드중입니다" });
				}
			},
			imgUploadOptions: { //이미지 업로드 옵션
				size: 3,
				onClickPublish: function () {
					//callLoadingBar({ status: true, message: "레이어를 업로드중입니다" });
				},
				//formatList : [] // 정의하지 않으면 'jpg', 'jpeg', 'png' 사용 가능
			},
			netCdfUploadOptions: { //netCdf 업로드 옵션
				onClickPublish: function () {
					callLoadingBar({ status: true, message: "레이어를 업로드중입니다" });
				}
			}

		},
		api: {
			publishFileLayer: uploadApi.publishFileLayer,
			publishDXFFile: uploadApi.publishDXFFile,
			geocodingLayer: geocodingApi.geocodingLayer,
			uploadWebLayer: layerApi.uploadWebLayer,
			getCommonCode: layerUploadCodeApi.getAllDetailCode
		}
	});
	layerUploadWidget.addTo(true);
</script>
</html>
