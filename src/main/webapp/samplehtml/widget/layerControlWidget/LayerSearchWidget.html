<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
	<div id="map" style="height:550px;"></div>
	<div class="layerSearchWidget" id="layerSearchWidget"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
    var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* oui api 연결 객체 생성  */
	var smtClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::',
	});
	var layerApi = oui.LayerApi(smtClient, {
		crtfckey: '::crtfckey::',
		groupCode: 'MPD003',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getLayerList : 3000,
			removeLayer : 3000,
			getLayerSearchTypeList : 3000,
		}
		 */
	});

	/* 레이어 검색 위젯 생성  */
	var layerSearchWidget = new oui.LayerSearchWidget({
		target: document.querySelector('#layerSearchWidget'),
		options: {
			pageSize: 20,
			pageIndex: 1,
			//레이어 추가 버튼 클릭시, 해당 레이어 정보를 리턴 (보통 이곳에서 레이어를 지도에 추가)
			getAddLayerInfo: function (layerInfo) {
	        	var _data = null;
		        var _layer;
		        var _layerCallType = '1';
		        //웹레이어 추가 의 경우
		        if (layerInfo.lyrClCode && layerInfo.lyrClCode == 'MPD013' && layerInfo.lyrClSeCode && layerInfo.lyrClSeCode == '11') {
					_layerCallType = '2';
					var param = JSON.parse(layerInfo.mapUrlParamtr);
					_layer = odf.LayerFactory.produce('api', {
						...param
					});
					_layer.setMap(map);
					_layer.fit();
		        } else {
					fetch(`::mapAPI::/layer/cn/select?cntntsId=${layerInfo.cntntsId}`, {}).then((response) => response.json()).then((data) => {
						var contentDetail = data.result;
						layerInfo.typeName = contentDetail.lyrOpertSpcNm + ':' + contentDetail.cntntsId;
						//layerInfo.typeName = 'dklx' + ':' + layerInfo.cntntsId;
						//contetnId 넘어올 값 그룹의경우 'GR000' , 레이어의경우 'L10000'
						var refineContentInfo = {};
						//레이어의 경우
						var _serviceType = (layerInfo.svcTySeCode != null ? (layerInfo.svcTySeCode == 'M' ? 'wms' : (layerInfo.svcTySeCode == 'T' ? 'wmts' : 'wfs')) : 'wfs');

						if (layerInfo.lyrClCode == 'MPD011' && layerInfo.lyrClSeCode == '04') {
					 		_serviceType = "heatmap";
						} else if (layerInfo.lyrClCode == 'MPD011' && layerInfo.lyrClSeCode == '06') {
					 		_serviceType = "cluster";
						} else if (layerInfo.lyrClCode == "MPD013" && layerInfo.lyrClSeCode == '06' && layerInfo.lyrTySeCode == '5') {
					 		_serviceType = "group";
						}

						//해당 정보로 레이어 만들기['heatmap', 'cluster'
						_layer = odf.LayerFactory.produce('geoserver', { // 레이어 호출 방법 (ex. geoserver, geojson)
							method: 'post',
							server: `::mapAPI::/api/map/${['heatmap', 'cluster'].includes(_serviceType) ? 'wfs' : (_serviceType == "group" ? "wms" : _serviceType)}`, // 레이어가 발행된 서버 주소
							layer: layerInfo.typeName, // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
							service: _serviceType, // 호출하고자 하는 레이여 형태(wms, wfs, wmts)
							bbox: false,
							matrixSet: _serviceType === 'wmts' ? `EPSG:${cntmSeCode}` : undefined,
							crtfckey: '::crtfckey::'
						});
			            _layer.setMap(map);
			            _layer.fit();
					}).catch(error => {
			            console.dir(error);
					});
				}
			},
			removeLayerCallback: (layerInfo) => {
				console.dir(layerInfo);
			},
			alertList: {
				customAlert: (message) => {
					alert(message);
				},
	        	//사용자 정의 알림 메세지 정의
	        	customConfirm: (message, callback) => {
					//확인창 띄우기
					var test = confirm(message);
					if (test) {
						callback();
					}
				},
				customErrorAlert: (message) => {
					alert(message);
				}
			}
		},
	    api: {
			getLayerList: layerApi.getLayerList,
			removeLayer: layerApi.removeLayer,
			getNavInfo: function (callback) {
				var navList = [
					{
		            		title: '국가공간정보',
		            		options: { holdDataSeCode: '9' },
					},
					{
		            		title: '사용자데이터',
		            		options: { holdDataSeCode: '1' }
					}
		        ];
		        callback({ navList: navList, initNavValue: '9' });
			},
			getTypeNavList: layerApi.getLayerSearchTypeList
		}
	});
	layerSearchWidget.addTo(true);

</script>
</html>
