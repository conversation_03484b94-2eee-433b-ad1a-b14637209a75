<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<body>
<div id ="map" style="height:550px;"></div>
<ul class="toolbar">
	<li class="limsControlWidget" id="limsControlWidget"></li>
</ul>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* LX맵 위젯 생성  */
	//1) geon map api를 사용해서 브이월드 통신하여 위젯 생성
	var limsControlWidget = new oui.LimsControlWidget({
		options: {
			mapApiUrl: '::mapAPI::',
			alertList : {
				customAlert: (message) => {
					alert(message);
				},
				customErrorAlert: (message) => {
					alert(message);
				}
			}
		},
		target: document.querySelector('#limsControlWidget')
	});
	limsControlWidget.addTo(map);

	//2) geon map api를 사용하지 않고 vworld key를 사용해서 브이월드 통신하여 위젯 생성
	//   ※ 해당 방법을 사용할 때 proxy가 필요한 경우 proxyObject를 셋팅해서 사용하세요.
	//   ※ 브이월드에서 키를 발급받을 때, 위젯이 실행되는 도메인으로 등록해야 합니다.
	/**
	 *	var limsControlWidget = new oui.LimsControlWidget({
	 *		options: {
	 *			vworldKey: '[직접 발급받은 브이월드 키]',
	 *         	alertList : {
	 *       		customAlert: (message) => {
	 *     		 		alert(message);
	 *            	},
	 *          	customErrorAlert: (message) => {
	 *          		alert(message);
	 *           	}
	 *      	},
	 *  		proxyObject:{
 *         		proxyURL: `https://geon-gateway.geon.kr/map/proxy`,
	 *         		proxyParam: "url"
	 *       	}
	 *		},
	 *     	target: document.querySelector('#limsControlWidget')
	 *    });
	 *    limsControlWidget.addTo(map);
	 * */
	//지우기함수
	//limsControlWidget.remove();

</script>
</html>
