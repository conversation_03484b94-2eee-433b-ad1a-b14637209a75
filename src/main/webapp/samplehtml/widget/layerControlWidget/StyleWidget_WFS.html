<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<link href="::OuiUrl::/oui.css" rel="stylesheet">
	<link href="::SmtUrl::/css/common_toolbar.css" rel="stylesheet">

	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="::OuiUrl::/oui.min.js"></script>
</head>
<style>
 #style{
 	position: absolute;
    top: 0px;
    right: 0px;
    padding: 0 25px;
    background-color: white;
    height: 550px;
    overflow-y: scroll;
 }
 .style_inner{
 	height:unset !important;
 }
</style>
<body>
	<div id ="map" style="height:550px;"></div>
	<div id="style" class="cScroll"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);



	/* 테스트 레이어 생성 (wfs) */
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::testPointLayer1::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();



	//00.01. 레이어 api 생성 (아래부분을 주석처리하면 api를 사용하지 않고 스타일 위젯을 사용할 수 있습니다.)
	var userId = '::userId::';
	var smtClient = oui.HttpClient({
		//api 요청 실패시(500,503) 최대 재시도 횟수
		//maxRetries : 3,
		baseURL: '::smtAPI::',
	});
	var layerApi = oui.LayerApi(smtClient, {
		userId: userId,
		crtfckey: '::crtfckey::',
		groupCode: 'MPD003',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			selectSymbol : 3000,
			insertSymbol : 3000,
			deleteSymbol : 3000,
		}
		 */
	});

	//00.02. 컬럼 정보 api 객체 생성 (아래부분을 주석처리하면 api를 사용하지 않고 스타일 위젯을 사용할 수 있습니다.)
	var columnInfoApi = oui.ColumnInfoApi(smtClient, {
		lyrId: '::testPointLayer1Id::',
		userId: userId,
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			selectColumn : 3000,
			selectUniqueColumn : 3000,
			selectRangeColumn : 3000,
		}
		 */
	});
	var attributeApi = oui.AttributeApi(smtClient, {
		crtfckey: '::crtfckey::',
		/*
		//timeout 설정
		//timeout : 3000,
		timeout  :{
			getCountAttributes : 3000,
		}
		 */
	});



	var alertList = {
		customAlert: (message) => {
			alert(message);
		},
		customErrorAlert: (message) => {
			alert(message);
		}
	};

	//01. 스타일 위젯 생성
	var styleWidget = new oui.StyleWidget({
		odf,
	    target: document.querySelector('#style'),
	    options: {
	    		imageUrl: 'js/oui/images/widget',
	    		alertList,
	    },
	    // 아래부분을 주석처리하면 api를 사용하지 않고 스타일 위젯을 사용할 수 있습니다.
	    api: {
    		// 사용자정의 이미지 조회 function
			selectSymbol: layerApi.selectSymbol,
			// 사용자정의 이미지 추가 function
			insertSymbol: layerApi.insertSymbol,
			// 사용자정의 이미지 삭제 function
			deleteSymbol: layerApi.deleteSymbol,
			// 별칭 및 컬럼 정보 조회
			selectColumn: columnInfoApi.selectColumn,
			// 컬럼정보조회 옵션값 변경
			columnInfoOptionChange: columnInfoApi.changeOption,
			// 레이어 컬럼 유일값 조회
			selectUniqueColumn: columnInfoApi.selectUniqueColumn,
			// 레이어 컬럼 범위 조회
			selectRangeColumn: columnInfoApi.selectRangeColumn,
			// 레이어 속성 정보 목록 갯수 조회
			getCountAttributes : attributeApi.getCountAttributes,
	    }
	});
	styleWidget.addTo(wfsLayer,'::testPointLayer1Id::',map);
</script>
</html>
