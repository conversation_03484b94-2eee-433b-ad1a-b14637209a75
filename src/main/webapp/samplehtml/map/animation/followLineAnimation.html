<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
</head>
<link href="::OdfUrl::/odf.css" rel="stylesheet">
<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
<style>

</style>

<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div>
		※ 버스 노선 정류장 정보는 서울 열린데이터 광장의 "서울시 버스정류소 위치정보"를 이용하였습니다.
	</div>
	<div class="control">
		<label for="speed">
			speed:&nbsp;
			<input id="speed" type="range" min="10" max="999" step="10" value="60">
		</label>
		<button id="start" onClick="start()">시작</button>
	</div>
</body>
<script>

	// 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.)
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	//서울특별시 5626 버스 노선
	var lineLayer = odf.LayerFactory.produce('empty'/*레이어를 생성하기위 한 테이터 호출 방법*/, {}/*레이어 생성을 위한 옵션*/);
	lineLayer.setMap(map);
	var busRoute = [[126.9511819, 37.4113392], [126.9511297, 37.4113634], [126.9510695, 37.4113641], [126.9510151, 37.4113502], [126.9509781, 37.4113262], [126.9509533, 37.4113046], [126.9509373, 37.4112869], [126.9507468, 37.4108034], [126.9505818, 37.4103784], [126.950472, 37.4101971], [126.9504156, 37.4098741], [126.950408, 37.4096697], [126.9504455, 37.4095006], [126.950473, 37.409212], [126.950473, 37.409212], [126.9504761, 37.4089956], [126.9504125, 37.4087513], [126.9499687, 37.4068699], [126.9499687, 37.4068699], [126.9499245, 37.4063964], [126.9496718, 37.4054049], [126.9494011, 37.4042315], [126.9492652, 37.4039306], [126.949173, 37.4037244], [126.9491744, 37.403714], [126.9491744, 37.403714], [126.9491973, 37.403527], [126.949216, 37.4034557], [126.9493973, 37.4028985], [126.9495974, 37.4022701], [126.9496926, 37.4019918], [126.9495594, 37.4018929], [126.9491231, 37.4017629], [126.9488762, 37.4016747], [126.9487209, 37.40161], [126.9483466, 37.4014209], [126.9477647, 37.4010364], [126.9474274, 37.4008021], [126.9474274, 37.4008021], [126.9459535, 37.3997653], [126.9453525, 37.3994086], [126.9450771, 37.3992882], [126.9446127, 37.3991401], [126.944267, 37.399074], [126.944267, 37.399074], [126.9439956, 37.3990458], [126.9436416, 37.3990439], [126.9432174, 37.3990423], [126.9427643, 37.3990424], [126.942597, 37.3990369], [126.942597, 37.3990369], [126.9410148, 37.3990267], [126.9402009, 37.3990734], [126.938439, 37.399058], [126.938439, 37.399058], [126.9367998, 37.3990506], [126.9362464, 37.3990153], [126.935707, 37.399006], [126.935707, 37.399006], [126.9351641, 37.398995], [126.9345946, 37.3989429], [126.9343075, 37.3989031], [126.9339811, 37.3988374], [126.9336999, 37.3987538], [126.9332623, 37.3985516], [126.9328418, 37.3983093], [126.9318675, 37.3977986], [126.9309474, 37.3973069], [126.9307301, 37.3972039], [126.9305341, 37.3971013], [126.9305341, 37.3971013], [126.9305141, 37.3970927], [126.9304467, 37.3970358], [126.9302393, 37.3969295], [126.9301286, 37.3968211], [126.928303, 37.3958703], [126.9280653, 37.3957488], [126.9278885, 37.3956426], [126.9273105, 37.3953331], [126.9268205, 37.3951373], [126.9263207, 37.394887], [126.9262389, 37.3948663], [126.9261227, 37.394805], [126.9261227, 37.394805], [126.9259829, 37.3947304], [126.925978, 37.394735], [126.9257518, 37.3946104], [126.9253972, 37.3944304], [126.9253052, 37.3944454], [126.9241442, 37.3958383], [126.923866, 37.396169], [126.923866, 37.396169], [126.9228762, 37.3973487], [126.9225497, 37.3977359], [126.921384, 37.399138], [126.921384, 37.399138], [126.919511, 37.401382], [126.919511, 37.401382], [126.9189127, 37.4020929], [126.9184193, 37.402695], [126.9181603, 37.4029723], [126.9173983, 37.403929], [126.9173983, 37.403929], [126.9159036, 37.4056958], [126.915062, 37.406739], [126.915062, 37.406739], [126.9133841, 37.40876], [126.9127298, 37.4095426], [126.9127483, 37.4096375], [126.91386, 37.410233], [126.91386, 37.410233], [126.9142858, 37.4104623], [126.914723, 37.4107069], [126.9152412, 37.4109805], [126.9154917, 37.4111122], [126.9157091, 37.4112503], [126.9158128, 37.4113124], [126.9158662, 37.4113485], [126.9159181, 37.4113934], [126.9159647, 37.4114437], [126.9160523, 37.4115623], [126.9161183, 37.4116904], [126.9162001, 37.4118736], [126.9168581, 37.4130449], [126.9169325, 37.4133699], [126.916996, 37.413659], [126.916996, 37.413659], [126.9170905, 37.4140404], [126.9171707, 37.4144432], [126.9174007, 37.4151678], [126.9174223, 37.4153281], [126.9174106, 37.4154738], [126.9173749, 37.4155828], [126.9173059, 37.4156801], [126.9171934, 37.4157792], [126.917055, 37.415849], [126.9163283, 37.4161036], [126.9151707, 37.4164715], [126.9147133, 37.4166453], [126.9147133, 37.4166453], [126.9142139, 37.4168729], [126.9133872, 37.4172517], [126.9128842, 37.4175678], [126.9124807, 37.4178663], [126.9113614, 37.4188013], [126.910323, 37.419689], [126.910323, 37.419689], [126.9094055, 37.4204662], [126.9088121, 37.4208573], [126.9085875, 37.4210684], [126.9082293, 37.4214986], [126.9078969, 37.4218289], [126.9075976, 37.4222136], [126.9074031, 37.4225523], [126.9074031, 37.4225523], [126.9069748, 37.4234079], [126.9065797, 37.4241569], [126.90597, 37.4253653], [126.9057351, 37.425937], [126.9055671, 37.4264681], [126.9051672, 37.4277459], [126.9044271, 37.4294607], [126.9043773, 37.4296378], [126.9043773, 37.4296378], [126.904334, 37.429792], [126.9042842, 37.4299664], [126.9038759, 37.4315973], [126.9034844, 37.4331557], [126.9033744, 37.4335569], [126.9033744, 37.4335569], [126.9032883, 37.4338752], [126.9031747, 37.4342989], [126.9031162, 37.4345691], [126.9031572, 37.436429], [126.9031163, 37.4381999], [126.9033121, 37.4396562], [126.9033786, 37.4401999], [126.9033786, 37.4401999], [126.9034216, 37.4405579], [126.9035424, 37.4416401], [126.9037348, 37.4431467], [126.9038002, 37.4436914], [126.9038659, 37.444515], [126.9038333, 37.4455317], [126.9036973, 37.4465201], [126.9034116, 37.4474422], [126.9031663, 37.4481087], [126.9031663, 37.4481087], [126.9030742, 37.4483573], [126.9025057, 37.4498348], [126.9016854, 37.4519407], [126.9014864, 37.4524641], [126.9014864, 37.4524641], [126.9013566, 37.4528046], [126.9007662, 37.4542939], [126.9000461, 37.4561237], [126.8995589, 37.4574555], [126.8990516, 37.4587731], [126.8988505, 37.4592786], [126.8988505, 37.4592786], [126.8987109, 37.4596299], [126.8977533, 37.4622557], [126.8975491, 37.4628267], [126.8975174, 37.4631848], [126.8975069, 37.4633765], [126.8975061, 37.463557], [126.8975155, 37.4637238], [126.8975155, 37.4637238], [126.8975251, 37.4638315], [126.8975439, 37.4640371], [126.8975655, 37.4643343], [126.8975751, 37.4644428], [126.8976465, 37.4653221], [126.8979696, 37.4684482], [126.8980394, 37.4691911], [126.8980394, 37.4691911], [126.8980619, 37.4694364], [126.8983531, 37.4717317], [126.898466, 37.4723448], [126.898636, 37.4729304], [126.8986371, 37.4729307], [126.8986371, 37.4729307], [126.898964, 37.476413], [126.898964, 37.476413], [126.8990576, 37.4777295], [126.8991974, 37.4787216], [126.8992768, 37.4792224], [126.8994701, 37.4797635], [126.8995238, 37.47983], [126.8996372, 37.4798433], [126.8997997, 37.4798302], [126.899906, 37.47977], [126.9000197, 37.4796956], [126.900129, 37.4795825], [126.9001559, 37.4795037], [126.900133, 37.4794095], [126.9000637, 37.4793267], [126.8999578, 37.4792799], [126.8994496, 37.4791569], [126.8986948, 37.4790292], [126.8963879, 37.4786468], [126.8958897, 37.4786461], [126.8956975, 37.4786443], [126.8949459, 37.4787009], [126.894792, 37.478708], [126.894792, 37.478708], [126.893526, 37.4789288], [126.8933408, 37.4789766], [126.8921526, 37.4791903], [126.8920011, 37.4792181], [126.8917498, 37.4792543], [126.8917498, 37.4792543], [126.8910445, 37.4793679], [126.8909222, 37.4793967], [126.8908197, 37.4794139], [126.8907642, 37.4794211], [126.8904723, 37.4794622], [126.8904214, 37.4794766], [126.8903078, 37.4795034], [126.8898146, 37.4795952], [126.8893402, 37.4797629], [126.8889987, 37.4799179], [126.88886, 37.479995], [126.88886, 37.479995], [126.8887544, 37.4800416], [126.8885637, 37.4801453], [126.8879353, 37.4805899], [126.8872224, 37.4813941], [126.886993, 37.4817321], [126.8869767, 37.4818714], [126.8872407, 37.4824485], [126.8872834, 37.4826201], [126.8872691, 37.4828182], [126.887153, 37.482997], [126.887153, 37.482997], [126.8868758, 37.4834182], [126.8868178, 37.4835168], [126.8863146, 37.4846654], [126.8860456, 37.4852666], [126.885671, 37.48611], [126.885671, 37.48611], [126.88508, 37.4875324], [126.884945, 37.487842], [126.884945, 37.487842], [126.8847589, 37.4883217], [126.88472, 37.4884202], [126.884542, 37.4890645], [126.884253, 37.490268], [126.884253, 37.490268], [126.8841377, 37.490817], [126.8836484, 37.4928139], [126.8836369, 37.4929482], [126.8836682, 37.4929896], [126.8837252, 37.4930387], [126.8845412, 37.4932188], [126.8845412, 37.4932188], [126.88536, 37.4934172], [126.8856074, 37.4934966], [126.8861139, 37.4937025], [126.8867998, 37.4940219], [126.8872491, 37.4942519], [126.8873609, 37.4942906], [126.8882288, 37.4947651], [126.8885751, 37.4949456], [126.8887594, 37.4951946], [126.8888246, 37.4953754], [126.8887532, 37.495618], [126.888488, 37.49594], [126.888488, 37.49594], [126.8878661, 37.496718], [126.8876622, 37.4969368], [126.8874954, 37.4971075], [126.8871307, 37.4975313], [126.8863819, 37.4984641], [126.8862316, 37.4985703], [126.8860433, 37.4986653], [126.8859252, 37.4987154], [126.8857018, 37.4988612], [126.8854193, 37.4989564], [126.8841279, 37.4995339], [126.8839659, 37.4996672], [126.8836956, 37.4998823], [126.8833022, 37.500189], [126.882739, 37.50076], [126.882739, 37.50076], [126.8825919, 37.5008899], [126.8822443, 37.5010577], [126.8820011, 37.5012724], [126.8816481, 37.5015811], [126.8811666, 37.5019855], [126.8803788, 37.5027006], [126.8802433, 37.5028348], [126.880094, 37.5029401], [126.8800259, 37.5029873], [126.8798279, 37.5030032], [126.8795541, 37.5029714], [126.8793306, 37.5028755], [126.8783562, 37.502169], [126.8778337, 37.5017892], [126.8773919, 37.5013946], [126.8766751, 37.5009282], [126.8751785, 37.5000701], [126.8747484, 37.4998586], [126.8747484, 37.4998586], [126.8745406, 37.4997566], [126.8738239, 37.4994322], [126.8734444, 37.4992761], [126.8728587, 37.4990573], [126.8725469, 37.4989878], [126.8720855, 37.4989461], [126.8717514, 37.4989766], [126.871209, 37.499062], [126.8694418, 37.4994263], [126.8684314, 37.4996213], [126.868288, 37.4996373], [126.868164, 37.4996492], [126.8680614, 37.4996506], [126.8680614, 37.4996506], [126.8678654, 37.4996517], [126.8676869, 37.4996501], [126.8672839, 37.4996137], [126.8670063, 37.4995328], [126.8667014, 37.4993865], [126.8663964, 37.4992222], [126.8660714, 37.4989965], [126.8656636, 37.4986731], [126.8647162, 37.4978846], [126.8640582, 37.4973324], [126.8636007, 37.4969691], [126.8631454, 37.4967892], [126.8625278, 37.4966387], [126.8620809, 37.4966225], [126.8617264, 37.4966457], [126.8612895, 37.4967139], [126.8605737, 37.49682], [126.8601167, 37.4968965], [126.8601167, 37.4968965], [126.8598962, 37.4969333], [126.8585006, 37.4972131], [126.8572802, 37.4974304], [126.8570207, 37.4974354], [126.8567612, 37.4974211], [126.8562371, 37.4973882], [126.855504, 37.4973614], [126.855504, 37.4973614], [126.8552358, 37.4973507], [126.8549928, 37.4973414], [126.8546423, 37.497338], [126.8539735, 37.4974396], [126.8536099, 37.4975532], [126.8528009, 37.4979509], [126.8517627, 37.4982775], [126.8514376, 37.4983521], [126.850871, 37.4984277], [126.8504698, 37.4983955], [126.8504032, 37.4983911], [126.8501179, 37.4984023], [126.8494294, 37.4983357], [126.8488219, 37.4982534], [126.8482281, 37.4982038], [126.8479554, 37.4981599], [126.8476793, 37.4980805], [126.8472199, 37.497945], [126.8467651, 37.4976748], [126.8464223, 37.4974496], [126.846108, 37.4972625], [126.8455937, 37.4969841], [126.845481, 37.496924], [126.8448242, 37.4966407], [126.8447646, 37.4966126], [126.844567, 37.496546], [126.844567, 37.496546], [126.8435113, 37.4960678], [126.84259, 37.495671], [126.84259, 37.495671], [126.8420601, 37.4954354], [126.8414787, 37.49518], [126.8412307, 37.4950732], [126.8408725, 37.4949418], [126.839456, 37.494307], [126.839456, 37.494307], [126.838659, 37.4939203], [126.8377272, 37.4934787], [126.8374232, 37.4933946], [126.8371409, 37.4933648], [126.8365756, 37.4933592], [126.8363302, 37.4933762], [126.8359408, 37.4933797], [126.8359408, 37.4933797], [126.8348341, 37.4933601], [126.832822, 37.493322], [126.832822, 37.493322], [126.830819, 37.4933084], [126.830819, 37.4933084], [126.8262409, 37.4932842], [126.8258004, 37.4932528], [126.8254056, 37.4931858], [126.8246728, 37.4930433], [126.8241772, 37.4929213], [126.823704, 37.492781], [126.823704, 37.492781], [126.8235355, 37.4927347], [126.8233559, 37.4926518], [126.8222767, 37.4922446], [126.8218722, 37.4920589], [126.8215932, 37.4918413], [126.8211885, 37.4915646], [126.8209925, 37.4914093], [126.8209925, 37.4914093], [126.8209671, 37.4913846], [126.8206876, 37.4911937], [126.8206545, 37.4911292], [126.8206667, 37.4910752], [126.8207351, 37.4910399], [126.8208476, 37.4910517], [126.8213059, 37.491415], [126.8219099, 37.4918355], [126.8223919, 37.4921106], [126.8228191, 37.492268], [126.8236284, 37.4925824], [126.8239208, 37.4926844], [126.8244298, 37.492782], [126.8244298, 37.492782], [126.8252659, 37.4929674], [126.8258935, 37.4930825], [126.8270178, 37.4931107], [126.8289404, 37.4931186], [126.8304436, 37.4931376], [126.8304436, 37.4931376], [126.83348, 37.493139], [126.83348, 37.493139], [126.8346016, 37.493153], [126.835391, 37.493176], [126.835391, 37.493176], [126.8370079, 37.4931923], [126.8374376, 37.4931965], [126.837821, 37.4932634], [126.8381472, 37.4933747], [126.8390124, 37.4937346], [126.8403486, 37.4943333], [126.8407079, 37.494491], [126.8407079, 37.494491], [126.8414352, 37.4948004], [126.8422474, 37.4951539], [126.842846, 37.4954338], [126.8433935, 37.4956775], [126.8433935, 37.4956775], [126.8438987, 37.495878], [126.844496, 37.4961345], [126.8456569, 37.4966463], [126.8459481, 37.4967842], [126.8462697, 37.4969583], [126.8462697, 37.4969583], [126.8471241, 37.4975054], [126.8477174, 37.4978605], [126.8481693, 37.4979722], [126.8487941, 37.4980201], [126.8501862, 37.4980967], [126.8503223, 37.498071], [126.8504477, 37.4980091], [126.8505162, 37.4979647], [126.850608, 37.4978755], [126.8507646, 37.4976401], [126.850789, 37.49756], [126.8508087, 37.4974432], [126.8507815, 37.4972876], [126.8507362, 37.4971868], [126.8506619, 37.4970946], [126.850579, 37.4970077], [126.8504415, 37.4969397], [126.8503175, 37.4969086], [126.8502272, 37.4968987], [126.8501141, 37.4968976], [126.8500074, 37.4969136], [126.8499424, 37.4969535], [126.8498515, 37.4970265], [126.8494434, 37.4974794], [126.8490352, 37.4979323], [126.8486088, 37.4984508], [126.848298, 37.4988081], [126.8478045, 37.4994558], [126.8474969, 37.4999273], [126.8469831, 37.5011533], [126.8469831, 37.5011533], [126.8468796, 37.5014164], [126.8469455, 37.5015931], [126.8479534, 37.5020243], [126.8479534, 37.5020243], [126.8491363, 37.5024736], [126.8496845, 37.5026698], [126.849821, 37.502733], [126.849821, 37.502733], [126.8499911, 37.5027795], [126.8500031, 37.5027759], [126.8501438, 37.502499], [126.8502552, 37.5022946], [126.8503234, 37.502158], [126.8506094, 37.5016115], [126.850783, 37.501247], [126.850783, 37.501247], [126.8510863, 37.5006193], [126.8517132, 37.4992853], [126.851823, 37.49905], [126.851823, 37.49905], [126.8523015, 37.4980632], [126.8524626, 37.4978845], [126.8526343, 37.497751], [126.8535549, 37.4974536], [126.854032, 37.4973141], [126.8545532, 37.497247], [126.855121, 37.4972558], [126.855121, 37.4972558], [126.8553807, 37.4972601], [126.8562598, 37.4973176], [126.8568118, 37.4973587], [126.857069, 37.4973675], [126.8572642, 37.4973643], [126.8584788, 37.4971588], [126.8600102, 37.4968492], [126.8605431, 37.4967552], [126.8610182, 37.4966902], [126.8618139, 37.4965772], [126.8619781, 37.496569], [126.862182, 37.4965627], [126.8623708, 37.4965666], [126.8623708, 37.4965666], [126.8624626, 37.4965704], [126.8625701, 37.4965788], [126.8631479, 37.496711], [126.8636537, 37.496888], [126.864064, 37.4971769], [126.8644676, 37.4975335], [126.864876, 37.4978521], [126.8663136, 37.4990413], [126.866701, 37.4992917], [126.8668287, 37.4993492], [126.8668751, 37.499369], [126.8669333, 37.4993911], [126.8669333, 37.4993911], [126.8671428, 37.49947], [126.8672431, 37.4995057], [126.8673283, 37.4995336], [126.867709, 37.4995793], [126.8681916, 37.4995767], [126.869161, 37.4993859], [126.8713857, 37.4989131], [126.8720996, 37.4988271], [126.8726019, 37.4988798], [126.8729551, 37.4989638], [126.873602, 37.4992186], [126.8740669, 37.4994424], [126.874529, 37.4996592], [126.874529, 37.4996592], [126.8746965, 37.4997365], [126.8768676, 37.500912], [126.8774956, 37.5012694], [126.877967, 37.5015081], [126.8787475, 37.5020335], [126.8796195, 37.5026769], [126.8799816, 37.5026601], [126.8801523, 37.5025532], [126.8805349, 37.5021741], [126.8807901, 37.5019389], [126.8812789, 37.5014964], [126.8814856, 37.5013458], [126.8817384, 37.5011564], [126.8818731, 37.5010561], [126.8820445, 37.500905], [126.8823439, 37.5006955], [126.8824436, 37.5006029], [126.8825949, 37.5004608], [126.88272, 37.5003424], [126.8828632, 37.5000949], [126.882954, 37.500023], [126.882954, 37.500023], [126.8832526, 37.49972], [126.8835276, 37.4994794], [126.8836875, 37.4993727], [126.8842118, 37.4990984], [126.8855083, 37.498615], [126.8856939, 37.4985822], [126.8858471, 37.4985229], [126.8859414, 37.4983848], [126.8865045, 37.4977864], [126.8866077, 37.4976883], [126.8878285, 37.496204], [126.8884618, 37.4954351], [126.8884851, 37.4953902], [126.8884863, 37.4953092], [126.8884533, 37.4952458], [126.8884086, 37.4952093], [126.88741, 37.494695], [126.88741, 37.494695], [126.8869276, 37.4944205], [126.8866018, 37.4942733], [126.8854674, 37.493749], [126.8851186, 37.4936286], [126.8846903, 37.4935344], [126.8837549, 37.4933183], [126.8835416, 37.4932172], [126.8834193, 37.4930719], [126.8833876, 37.4929274], [126.883401, 37.4927834], [126.8837873, 37.4911381], [126.8839547, 37.490527], [126.884123, 37.489844], [126.884123, 37.489844], [126.8843284, 37.4889717], [126.8844893, 37.4883947], [126.8845603, 37.4882444], [126.8846241, 37.4880414], [126.8846241, 37.4880414], [126.8858272, 37.4852492], [126.8859952, 37.4848458], [126.88623, 37.484277], [126.88623, 37.484277], [126.8866243, 37.4834057], [126.886811, 37.4831769], [126.8869615, 37.4829429], [126.8870865, 37.4827258], [126.8870904, 37.4826867], [126.8870641, 37.4825433], [126.887014, 37.482482], [126.887014, 37.482482], [126.8868501, 37.4820984], [126.8866399, 37.4817851], [126.8865982, 37.4815505], [126.8866352, 37.4813436], [126.8869117, 37.4809948], [126.8873854, 37.480514], [126.8876223, 37.4803437], [126.8880799, 37.4799876], [126.8883786, 37.4798094], [126.8883786, 37.4798094], [126.888841, 37.4795619], [126.8891453, 37.4794306], [126.8895015, 37.4793159], [126.889955, 37.4791756], [126.8904108, 37.4790994], [126.8925983, 37.4787324], [126.893369, 37.478586], [126.893369, 37.478586], [126.8949991, 37.4783383], [126.895646, 37.478301], [126.895646, 37.478301], [126.895983, 37.4783083], [126.8969825, 37.4784139], [126.8972352, 37.4783685], [126.8974393, 37.4782732], [126.8981704, 37.4778182], [126.8983576, 37.4776704], [126.8984351, 37.4775716], [126.8985127, 37.4773718], [126.8984726, 37.4769351], [126.8985532, 37.4758866], [126.8984051, 37.4739219], [126.8984051, 37.4739219], [126.8983809, 37.4736654], [126.8980675, 37.470631], [126.8980212, 37.4700684], [126.8980212, 37.4700684], [126.8979929, 37.4696952], [126.8977259, 37.4673748], [126.8976421, 37.4661988], [126.8975576, 37.4652729], [126.8975012, 37.4647015], [126.8975012, 37.4647015], [126.8974672, 37.4643509], [126.8974161, 37.4635667], [126.8974215, 37.4632583], [126.8974742, 37.4628084], [126.8986284, 37.4596304], [126.8991485, 37.4582923], [126.8993205, 37.4578324], [126.8993205, 37.4578324], [126.8999181, 37.4562098], [126.900956, 37.453554], [126.9011398, 37.4530771], [126.9011398, 37.4530771], [126.9012864, 37.4527005], [126.9018743, 37.4511971], [126.9024516, 37.4497073], [126.9027234, 37.4489992], [126.9027234, 37.4489992], [126.902871, 37.4486146], [126.9036268, 37.4465075], [126.9037631, 37.4455048], [126.9038097, 37.4445038], [126.9034216, 37.4414054], [126.9033542, 37.4408155], [126.9033542, 37.4408155], [126.9033184, 37.4404968], [126.9029939, 37.437947], [126.9029984, 37.4367963], [126.9030101, 37.4351271], [126.9030296, 37.4347138], [126.9030296, 37.4347138], [126.9030396, 37.434557], [126.9032365, 37.4338498], [126.9033547, 37.4333402], [126.9035278, 37.4327133], [126.9038018, 37.4314981], [126.9038596, 37.4312619], [126.9038596, 37.4312619], [126.9039274, 37.4309811], [126.9041971, 37.4299673], [126.9043329, 37.4294231], [126.9046721, 37.4276906], [126.9050878, 37.4262892], [126.9051089, 37.425955], [126.9052195, 37.4255587], [126.9052878, 37.4254294], [126.9054104, 37.4250144], [126.9056549, 37.4246576], [126.9058844, 37.4242145], [126.9061951, 37.4236746], [126.9062953, 37.4234379], [126.9065878, 37.422886], [126.9068228, 37.4225001], [126.9069558, 37.4222816], [126.9070798, 37.4220835], [126.9072801, 37.4217571], [126.9074425, 37.4215703], [126.907958, 37.4210672], [126.9084765, 37.4205978], [126.9089081, 37.4202212], [126.909258, 37.419895], [126.909258, 37.419895], [126.9106637, 37.4187434], [126.9119914, 37.4175956], [126.9125084, 37.4172303], [126.9130575, 37.4169066], [126.913882, 37.416514], [126.913882, 37.416514], [126.9150935, 37.41611], [126.9161648, 37.4156876], [126.9171753, 37.4153257], [126.9172372, 37.415234], [126.9171091, 37.4148373], [126.9169784, 37.41441], [126.916911, 37.414109], [126.916911, 37.414109], [126.9168792, 37.4139005], [126.9167772, 37.4134462], [126.9166742, 37.4131646], [126.91611, 37.4118908], [126.9160146, 37.4116723], [126.9159538, 37.4115706], [126.9158801, 37.4114738], [126.9158389, 37.4114286], [126.9157921, 37.4113882], [126.9157265, 37.4113531], [126.915379, 37.4112026], [126.9144628, 37.4107252], [126.9141977, 37.4106331], [126.913991, 37.4105195], [126.9139621, 37.4104853], [126.913577, 37.410285], [126.913577, 37.410285], [126.9127031, 37.409826], [126.9124895, 37.4096979], [126.9124121, 37.4095801], [126.9124142, 37.4094359], [126.9124527, 37.4093241], [126.9130681, 37.4085971], [126.914197, 37.4072], [126.914197, 37.4072], [126.915068, 37.4061663], [126.9154711, 37.4056743], [126.9164474, 37.4045035], [126.9164474, 37.4045035], [126.9168043, 37.4040989], [126.9177429, 37.4029488], [126.9180203, 37.4026117], [126.9190034, 37.4014205], [126.9190034, 37.4014205], [126.919592, 37.4007219], [126.9196745, 37.4006313], [126.9198274, 37.400439], [126.9200628, 37.4001562], [126.920979, 37.399044], [126.920979, 37.399044], [126.924247, 37.3951515], [126.9249777, 37.3942786], [126.9251255, 37.3941605], [126.9252597, 37.3940926], [126.925421, 37.3940929], [126.9256139, 37.3941577], [126.926205, 37.39446], [126.926205, 37.39446], [126.9265018, 37.3946298], [126.926813, 37.394865], [126.9273429, 37.3951409], [126.9274099, 37.3952083], [126.9280416, 37.3955515], [126.9293482, 37.3962402], [126.9300352, 37.3966003], [126.9305199, 37.3968281], [126.9311595, 37.397144], [126.9320187, 37.3976045], [126.9326254, 37.3979033], [126.9326254, 37.3979033], [126.9334014, 37.3983005], [126.9338506, 37.3984848], [126.9344589, 37.3985984], [126.9347182, 37.3986278], [126.9350454, 37.3986487], [126.936311, 37.398633], [126.936311, 37.398633], [126.938807, 37.3986386], [126.938807, 37.3986386], [126.942646, 37.398681], [126.942646, 37.398681], [126.9431896, 37.3986758], [126.9439967, 37.3986732], [126.9443639, 37.3986641], [126.9453076, 37.3988931], [126.9456836, 37.3990549], [126.9463702, 37.3995055], [126.9463702, 37.3995055], [126.9473211, 37.4001478], [126.9476112, 37.400298], [126.9483928, 37.4008649], [126.948772, 37.4010957], [126.949004, 37.4011891], [126.9498458, 37.4015979], [126.9500064, 37.4016654], [126.950096, 37.4017584], [126.9501235, 37.401857], [126.9501064, 37.4019742], [126.9500596, 37.4025038], [126.9498979, 37.4029319], [126.949692, 37.403672], [126.949692, 37.403672], [126.9496901, 37.4038524], [126.9497087, 37.4039697], [126.9497641, 37.4041596], [126.9500826, 37.4055785], [126.9500826, 37.4055785], [126.9501801, 37.4059806], [126.9502039, 37.406113], [126.9503027, 37.4065278], [126.9503972, 37.4069612], [126.9503972, 37.4069612], [126.9506056, 37.4078352], [126.9508144, 37.4087579], [126.9507551, 37.4088565], [126.9507315, 37.4089163], [126.9507239, 37.4092806], [126.9507239, 37.4092806], [126.950712, 37.4093621], [126.9506986, 37.4094441], [126.9506824, 37.4096318], [126.9506792, 37.4097199], [126.9506859, 37.4098579], [126.950693, 37.4099472], [126.9507306, 37.4101388], [126.9508498, 37.4104715], [126.9509786, 37.4107908], [126.9510238, 37.4108936]];
	var busRouteFeature = map.getProjection().projectGeom(odf.FeatureFactory.fromGeoJson({
		geometry: {
			type: 'linestring',
			coordinates: busRoute
		},
		properties: {
			"routeNo": "5626",
			"routeNm": "5626",
		}
	}), '4326');
	lineLayer.addFeature(busRouteFeature);
	lineLayer.fit();
	lineLayer.setStyle(odf.StyleFactory.produce({
		stroke: {
			color: '#ffcbcbcc',
			lineCap: 'square',//선의 끝부분 모양 'square'(네모지게)
			lineJoin: 'miter',//꺾이는 부분 모양 'miter'(뾰족하게)
			width: 10,
		},
	}))

	//서울특별시 5626 버스 노선 정류소 정보 표출
	var stationLayer = odf.LayerFactory.produce('geojson', {
		data: {
			type: 'FeatureCollection',
			features: [
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9509050764, 37.411338299] }, properties: { nodeId: '209000265', nodeNm: '안양비산동종점', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9504322893, 37.4090866634] }, properties: { nodeId: '209000266', nodeNm: '안양인라인경기장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9499490837, 37.4068661203] }, properties: { nodeId: '209000267', nodeNm: '종합운동장수영장.비산동교회', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9492582075, 37.4037212932] }, properties: { nodeId: '209000268', nodeNm: '종합운동장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9475994079, 37.4008543797] }, properties: { nodeId: '209000048', nodeNm: '삼호아파트.평촌우리병원', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9443664466, 37.3990536669] }, properties: { nodeId: '209000047', nodeNm: '동양월드타워', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9426623137, 37.3989644888] }, properties: { nodeId: '209000229', nodeNm: '대림아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9384569713, 37.3990106853] }, properties: { nodeId: '209000046', nodeNm: '삼성래미안아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9358326998, 37.3989708776] }, properties: { nodeId: '209000045', nodeNm: '비산사거리.이마트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9306277293, 37.3970779903] }, properties: { nodeId: '208000115', nodeNm: '진흥아파트.비산대교', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9261461696, 37.3946962859] }, properties: { nodeId: '208000114', nodeNm: '우체국사거리.안양초교.중화한방', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9239438881, 37.3960443666] }, properties: { nodeId: '208000066', nodeNm: '남부시장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9214933974, 37.3989732019] }, properties: { nodeId: '208000065', nodeNm: '안양1번가.안양고용센터', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9195223006, 37.4013533427] }, properties: { nodeId: '208000064', nodeNm: '대동문고.댕리단길', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9173833674, 37.4039177924] }, properties: { nodeId: '208000105', nodeNm: '안양여중고', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9150271743, 37.4067574711] }, properties: { nodeId: '208000104', nodeNm: '만안초교', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9139980888, 37.4102591] }, properties: { nodeId: '208000020', nodeNm: '안양예술공원지하도', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9170080643, 37.413673936] }, properties: { nodeId: '208000019', nodeNm: '삼성.세림아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9146726545, 37.4166350319] }, properties: { nodeId: '208000042', nodeNm: '안양예술공원', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9103632666, 37.4196002753] }, properties: { nodeId: '208000041', nodeNm: '관악역', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.907283789, 37.4225901053] }, properties: { nodeId: '208000040', nodeNm: '한마음선원.관악역이안아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9043545024, 37.4297220712] }, properties: { nodeId: '208000116', nodeNm: '대림.LG.힐스테이트아파트(중)', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9033765126, 37.4335516543] }, properties: { nodeId: '208000037', nodeNm: '석수역(중)', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.903622, 37.44019] }, properties: { nodeId: '117000013', nodeNm: '시흥유통센터', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9032727409, 37.4478432162] }, properties: { nodeId: '117000011', nodeNm: '박미삼거리.국립전통예술중고', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9016519517, 37.4521824331] }, properties: { nodeId: '117000009', nodeNm: '시흥사거리', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8988721519, 37.4592088301] }, properties: { nodeId: '117000007', nodeNm: '금천구청.금천경찰서', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.897555, 37.463604] }, properties: { nodeId: '117000005', nodeNm: '말미고개.금천소방서', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.898028, 37.468978] }, properties: { nodeId: '117000003', nodeNm: '금천우체국', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8986308908, 37.47296493] }, properties: { nodeId: '117000063', nodeNm: '남문시장.청춘삘딩', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.898921, 37.476441] }, properties: { nodeId: '117000061', nodeNm: '독산고개', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.894635, 37.478747] }, properties: { nodeId: '116000139', nodeNm: '삼부르네상스.효성아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.891739, 37.479247] }, properties: { nodeId: '116000648', nodeNm: '가리봉파출소', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8887534687, 37.4801153252] }, properties: { nodeId: '116000635', nodeNm: '디지털단지오거리', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.887123, 37.48301] }, properties: { nodeId: '116000045', nodeNm: '가리봉시장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8856450789, 37.4860984011] }, properties: { nodeId: '116000048', nodeNm: '구로4동자치회관', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.88486, 37.487987] }, properties: { nodeId: '116000049', nodeNm: '구로시장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8842398696, 37.4902484188] }, properties: { nodeId: '116000051', nodeNm: '구로4동우체국.고대구로병원정문', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.884567, 37.493233] }, properties: { nodeId: '116000027', nodeNm: '고대구로병원후문', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8884812232, 37.4959285478] }, properties: { nodeId: '116000198', nodeNm: '구로구청', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.88258, 37.500848] }, properties: { nodeId: '116000059', nodeNm: '구로역·NC신구로점', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.874701, 37.499883] }, properties: { nodeId: '116000005', nodeNm: '구일역.중앙유통상가', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8679976691, 37.4996343963] }, properties: { nodeId: '116000007', nodeNm: '동양미래대학.구로성심병원', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8602005359, 37.4969418239] }, properties: { nodeId: '116000009', nodeNm: '개봉역.영화아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8554373749, 37.4974485938] }, properties: { nodeId: '116000011', nodeNm: '경인중학교.개봉사거리', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.84458597, 37.4965241154] }, properties: { nodeId: '116000084', nodeNm: '오류1동주민센터.오류문화센터', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.842536958, 37.4956023105] }, properties: { nodeId: '116000086', nodeNm: '오류동역', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8394647757, 37.4942645442] }, properties: { nodeId: '116000088', nodeNm: '오류지구대.신오류주유소', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8359813615, 37.4933027885] }, properties: { nodeId: '116000156', nodeNm: '연세중앙교회', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.832823, 37.493319] }, properties: { nodeId: '116000094', nodeNm: '궁동청소년문화의집', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.830847, 37.4933] }, properties: { nodeId: '116000096', nodeNm: '우신고등학교', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8238226092, 37.4928265566] }, properties: { nodeId: '116000098', nodeNm: '온수역', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.821273, 37.491642] }, properties: { nodeId: '116000194', nodeNm: '온수동종점', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8244009171, 37.492777021] }, properties: { nodeId: '116000099', nodeNm: '온수역', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8304506092, 37.4930633974] }, properties: { nodeId: '116000097', nodeNm: '우신고등학교', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.833633, 37.493133] }, properties: { nodeId: '116000095', nodeNm: '궁동청소년문화의집.구로검사소', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8354231175, 37.4931082751] }, properties: { nodeId: '116000093', nodeNm: '연세중앙교회', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.840682, 37.494463] }, properties: { nodeId: '116000087', nodeNm: '씨티월드', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.843351, 37.495698] }, properties: { nodeId: '116000085', nodeNm: '오류동역', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8461421452, 37.4968379665] }, properties: { nodeId: '116000083', nodeNm: '오류1동주민센터.오류문화센터', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.846791, 37.501413] }, properties: { nodeId: '116000144', nodeNm: '개봉1동사거리.개봉푸르지오아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8480532092, 37.5019938418] }, properties: { nodeId: '116000128', nodeNm: '개봉푸르지오아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8498423009, 37.5026683511] }, properties: { nodeId: '116000125', nodeNm: '세곡초등학교', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8508090334, 37.5011232856] }, properties: { nodeId: '116000129', nodeNm: '고척근린시장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8517949619, 37.4990015306] }, properties: { nodeId: '116000130', nodeNm: '용두연립', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8551547189, 37.4973068647] }, properties: { nodeId: '116000012', nodeNm: '경인중학교.개봉사거리', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8620566339, 37.4964773551] }, properties: { nodeId: '116000010', nodeNm: '개봉역.한마을아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8670186731, 37.4993764843] }, properties: { nodeId: '116000008', nodeNm: '동양미래대학.구로성심병원', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.87453, 37.49971] }, properties: { nodeId: '116000006', nodeNm: '구일역.제일제당', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8828494432, 37.5000384326] }, properties: { nodeId: '116000058', nodeNm: '구로역·NC신구로점', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8876042378, 37.4947508763] }, properties: { nodeId: '116000026', nodeNm: '구로구청', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8840651203, 37.4899022236] }, properties: { nodeId: '116000052', nodeNm: '구로4동우체국.고대구로병원정문', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.88461, 37.48798] }, properties: { nodeId: '116000050', nodeNm: '구로시장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.886268, 37.48418] }, properties: { nodeId: '116000047', nodeNm: '남구로역', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8869928718, 37.4824285977] }, properties: { nodeId: '116000046', nodeNm: '가리봉시장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.888329, 37.479826] }, properties: { nodeId: '116000636', nodeNm: '디지털단지오거리.가산파출소', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.893432, 37.478558] }, properties: { nodeId: '117000032', nodeNm: '디지털단지오거리', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.895556221, 37.4782346236] }, properties: { nodeId: '117000033', nodeNm: '가산초등학교', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.89833, 37.474034] }, properties: { nodeId: '117000002', nodeNm: '문성초등학교', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.898003, 37.470136] }, properties: { nodeId: '117000004', nodeNm: '금천우체국', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.897496, 37.464832] }, properties: { nodeId: '117000006', nodeNm: '말미고개.금천소방서', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.8993, 37.457841] }, properties: { nodeId: '117000008', nodeNm: '금천구청.금천경찰서', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.901123, 37.453086] }, properties: { nodeId: '117000010', nodeNm: '시흥사거리', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.902663, 37.449067] }, properties: { nodeId: '117000012', nodeNm: '박미삼거리.국립전통예술중고', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.903369, 37.440958] }, properties: { nodeId: '117000014', nodeNm: '시흥유통센터', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9027764439, 37.4348585994] }, properties: { nodeId: '117000310', nodeNm: '석수역', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9038374436, 37.4311661289] }, properties: { nodeId: '208000094', nodeNm: '대림.LG.힐스테이트아파트(중)', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9093149622, 37.4198813272] }, properties: { nodeId: '208000034', nodeNm: '관악역', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9139117096, 37.4165307496] }, properties: { nodeId: '208000033', nodeNm: '안양예술공원', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9169080538, 37.4140073118] }, properties: { nodeId: '208000022', nodeNm: '삼성아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.913636589, 37.410267843] }, properties: { nodeId: '208000021', nodeNm: '안양예술공원지하도', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9142908303, 37.4070636265] }, properties: { nodeId: '208000205', nodeNm: '신협', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9165238434, 37.4043947214] }, properties: { nodeId: '208000204', nodeNm: '안양여중고', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9191027175, 37.4012336509] }, properties: { nodeId: '208000158', nodeNm: '안양시외버스정류소.교보생명.댕', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9211701499, 37.3988093961] }, properties: { nodeId: '208000254', nodeNm: '포스빌.안양고용센터', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9262705949, 37.3945071318] }, properties: { nodeId: '208000095', nodeNm: '우체국사거리.안양초교.중화한방', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9326723615, 37.3978996488] }, properties: { nodeId: '209000044', nodeNm: '비산농협', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9363871047, 37.3986080982] }, properties: { nodeId: '209000043', nodeNm: '비산사거리.이마트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9388546492, 37.3985965014] }, properties: { nodeId: '209000042', nodeNm: '비산롯데캐슬.평촌래미안푸르지 ', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9426759037, 37.3986439744] }, properties: { nodeId: '209000041', nodeNm: '미륭아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9464944339, 37.3995724267] }, properties: { nodeId: '209000201', nodeNm: '삼호아파트.평촌우리병원', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9496342995, 37.403637253] }, properties: { nodeId: '209000260', nodeNm: '종합운동장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9500531966, 37.4055809851] }, properties: { nodeId: '209000261', nodeNm: '종합운동장정문', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9504019496, 37.4070212177] }, properties: { nodeId: '209000262', nodeNm: '행복마을아파트', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9507406838, 37.4092852424] }, properties: { nodeId: '209000263', nodeNm: '안양인라인경기장', } },
				{ type: 'Feature', geometry: { type: 'Point', coordinates: [126.9512272433, 37.4112415556] }, properties: { nodeId: '209000264', nodeNm: '안양비산동종점', } },
			]
		},
		dataProjectionCode: 'EPSG:4326',
		//변환 좌표계
		featureProjectionCode: 'EPSG:::srid::'
	});
	stationLayer.setMap(map);
	stationLayer.fit();
	stationLayer.setStyle(odf.StyleFactory.produce({
		image: {
			icon: {
				src: 'images/busStation.png',
				anchor: [0.5, 1], //아이콘 위치 조정 값
				anchorOrigin: 'center-center', //아이콘 위치 조정 기준점
				anchorXUnits: 'fraction', //아이콘 위치 조정 단위 설정 x축
				anchorYUnits: 'fraction', //아이콘 위치 조정 단위 설정 y축
				scale: 0.25,
			}
		},
	}));

	//버스 이동 표현할 레이어 및 feature 추가
	var busPositionLayer = odf.LayerFactory.produce('empty'/*레이어를 생성하기위 한 테이터 호출 방법*/, {}/*레이어 생성을 위한 옵션*/);
	busPositionLayer.setMap(map);
	var position = map.getProjection().projectGeom(odf.FeatureFactory.fromGeoJson({
		geometry: {
			type: 'point',
			coordinates: busRoute[0]
		}
	}), '4326');
	var positionGeometry = position.getGeometry();
	var busIconStyle = odf.StyleFactory.produce({
		image: {
			circle: {
				radius: 5,
				fill: {
					color: 'black'
				},
				stroke: {
					color: 'white',
					width: 1,
				}
			}
		},
	});
	position.setStyle(busIconStyle)
	busPositionLayer.addFeature(position);

	//속도 값 input
	var speedInput = document.getElementById('speed');
	//시작 버튼
	var startButton = document.getElementById('start');
	//애니메이션 상태
	var animating = false;
	//총 이동 거리
	var distance = 0;
	//이전 애니메이션 동작 시작 시간
	var lastTime;
	//postrender 이벤트 id
	var postrenderEventId;

	//도형 이동
	function moveFeature(event) {
		const speed = Number(speedInput.value);//속도
		const time = event.frameState.time;//이벤트가 발샏한 시간
		const elapsedTime = time - lastTime;//이전 이벤트 발생 시각과의 차이
		distance = (distance/*기존 이동 거리*/ + (speed/*속도*/ * elapsedTime/*시간*/) / 1e6) % 2;
		lastTime = time;
		const currentCoordinate = busRouteFeature.getGeometry().getCoordinateAt(
			distance > 1 ? 2 - distance : distance
		);
		positionGeometry.setCoordinates(currentCoordinate);

		//render 이벤트 객체로부터 vectorContext(벡터 도형을 그릴때 사용되는 컨텍스트) 추출
		const vectorContext = odf.Render.getVectorContext(event);
		//여기서 setStyle은 단일 스타일만 적용 가능
		vectorContext.setStyle(busIconStyle);
		//vectorContext 의 geometry 값 변경
		vectorContext.drawGeometry(positionGeometry);
		//지도 그리기 요청
		map.render();
	}

	//애니메이션 시작
	function startAnimation() {
		animating = true;
		lastTime = Date.now();
		startButton.textContent = '종료';
		//postrender 이벤트 등록(busPositionLayer 레이어가 렌더링 된 후 moveFeature가 트리거)
		postrenderEventId = odf.event.addListener(busPositionLayer, 'postrender', moveFeature)
		position.setGeometry(null);
	}

	//애니메이션 종료
	function stopAnimation() {
		animating = false;
		startButton.textContent = '시작';
		position.setGeometry(positionGeometry);
		//postrender 이벤트 제거
		odf.event.removeListener(postrenderEventId);
		postrenderEventId = undefined;
	}

	//애니메이션 상태 변경
	function start() {
		if (animating) {
			stopAnimation();
		} else {
			startAnimation();
		}
	}
</script>

</html>
