<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	//툴팁 메세지 셋팅
	var divElem = document.createElement('div');
	divElem.innerHTML = '마우스를 따라다니는 툴팁 메세지';

	var marker = new odf.Marker({
		/*마커의 위치*/
		position : [ ::coordx::, ::coordy:: ],
		/*드래그 가능 여부(기본 값 false)*/
		draggable : false,
		/*마커의 상대적 위치
		 - 'top-left' : position 값 기준 상단 좌측에 위치
		 - 'top-center' : position 값 기준 상단 중앙에 위치
		 - 'top-right' : position 값 기준 상단 우측에 위치
		 - 'center-left' : position 값 기준 중앙 좌측에 위치
		 - 'center-center' : position 값 기준 중앙 중앙에 위치
		 - 'center-right' : position 값 기준 중앙 우측에 위치
		 - 'bottom-left' : position 값 기준 하단 우측에 위치
		 - 'bottom-center' : position 값 기준 하단 중앙에 위치(기본값)
		 - 'bottom-right' : position 값 기준 하단 좌측에 위치
		*/
		positioning : 'bottom-center',
		/*기준점으로 부터 정의한 값만큼 마커를 이동(픽셀) (기본값 [0,0])
		 - 첫번째 요소 : 수평 오프셋 값. 양수는 마커를 오른쪽으로 이동시킴
   		 - 두번째 요소 : 수직 오프셋 값. 양수는 마커를 아래로 이동시킴
		*/
		offset : [0,0],
		/*마커 이벤트 전파 중지 여부.
		 - true : 이벤트 전파 중지,(마커 클릭해도 지도 클릭 이벤트 발생하지 않음)
		 - false : 이벤트 전파 (기본값 false)
		*/
		stopEvent : true,
		//마커의 스타일 정의
		 style :{
			// width : '30px' ,
			// height : '30px' ,
			// 이미지 경로
			// src : '' ,
			// 사용자 정의 마커
			element : divElem
		},
		/*지도 화면에서 팝업이 잘려나오게 될 경우, 지도를 이동할지 여부 (기본값 false)*/
		autoPan : false,
		/* autoPan이 ture일때 사용되는 옵션. 지도를 이동하는데 사용되는 애니메이션.
		(기본값 : 250) 최소 :0 최대 : 100000 */
		autoPanAnimation : 0,
		/* autoPan이 ture일때 사용되는 옵션. 팝업과 지도 사이의 여백(픽셀) 지정 (기본값 : 20) */
		autoPanMargin : 50,
	});
	//마우스 이동시 툴팁메세지 위치 이동
	odf.event.addListener(map, 'pointermove', function(event){
		marker.setPosition(event.coordinate);
	});
	marker.setMap(map);



	// map 객체에 추가된 모든 마커 조회
	var markerList = map.getMarkers();

	//map 객체에서 미커 id로 마커 조회
	var selectMarker = map.getMarker(marker.id);

	// 마거에 연결된 element 조회
	var element = marker.getElement();

	// 현재 마커 위치 조회
	var position = marker.getPosition();

	// 마커 위치 지정
	// marker.setPosition([::coordx::, ::coordy:: ]);

	// 마커 상대적 위치 값 변경
	marker.setPositioning('bottom-center'
	/*마커의 상대적 위치
	 - 'top-left' : position 값 기준 상단 좌측에 위치
	 - 'top-center' : position 값 기준 상단 중앙에 위치
	 - 'top-right' : position 값 기준 상단 우측에 위치
	 - 'center-left' : position 값 기준 중앙 좌측에 위치
	 - 'center-center' : position 값 기준 중앙 중앙에 위치
	 - 'center-right' : position 값 기준 중앙 우측에 위치
	 - 'bottom-left' : position 값 기준 하단 우측에 위치
	 - 'bottom-center' : position 값 기준 하단 중앙에 위치(기본값)
	 - 'bottom-right' : position 값 기준 하단 좌측에 위치
	*/)


	// 마커의 offset 값 변경
	// marker.setOffset([0,0])

	// 마커에 등록된 map 조회
	var linkedMap = marker.getMap();

	//설정된 드래그 여부 값 조회
	var draggable = marker.getDraggable();

	// 드래그 여부 수정
	// marker.setDraggable(false);

	//마커를 지도 객체에서 삭제
	//marker.removeMap();

</script>
</html>

