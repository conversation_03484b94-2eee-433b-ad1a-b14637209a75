<!DOCTYPE HTML>
<html>

<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>

<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="infoArea" style="margin-top: 15px;">
		<p>그리기 도구로 그린 영역으로 피쳐 정보를 조회</p>
		<br>
		<button onclick="selectDraw('polygon');" class="onoffOnlyBtn toggle grp1">레이어 다각형으로 영역 선택</button>
		<button onclick="selectDraw('box');" class="onoffOnlyBtn toggle grp1">레이어 사각형으로 영역 선택</button>
		<button onclick="selectDraw('circle');" class="onoffOnlyBtn toggle grp1">레이어 원형으로 영역 선택</button>
		<button onclick="selectDraw('point');" class="onoffOnlyBtn toggle grp1">레이어 점으로 영역 선택</button>
		<br>
		<div id="featureInfo"></div>
	</div>
</body>
<style>
	/* 피처 속성 정보 조회 css */
	#featureInfo>div,
	#featureInfo>div>div {
		padding: 10px;
	}

	#featureInfo>div>div {
		overflow-x: auto;
	}

	#featureInfo h5.title,
	featureInfo h6.title {
		font-weight: bold;
		margin-bottom: 5px;
	}

	#featureInfo table {
		border-collapse: collapse;
	}

	#featureInfo td {
		border: 1px solid #949494 !important;
		padding: 3px !important;
		text-align: center;
	}
</style>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/*wms 레이어 생성*/
	var wmsLayer = odf.LayerFactory.produce('geoserver', {
		method: 'get',
		server: '::WmsAPI::',
		layer: '::polygonLayer1::',
		service: 'wms',
	});
	wmsLayer.setMap(map);

	/*wfs 레이어 생성*/
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method: 'get',
		server: '::WfsAPI::',
		layer: '::polygonLayer2::',
		service: 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();

	/*ui 없이 그리기 도구 생성*/
	var drawControl = new odf.DrawControl({
		message : {
			DRAWSTART_POLYGON : '추가할 도형을 그리기 위해 지도를 클릭하세요.'
		}
	});
	drawControl.setMap(map,false);

	//사용자가 그린 도형으로 feature 조회
	function selectDraw(drawType) {
		map.selectFeature({
			/** extractType 피쳐 추출 유형
				* - draw 직접 그린 도형 내에 속한 피쳐 추출
				* - view 현재 지도영역에서 피쳐 추출
				* - pixel 특정 픽셀에 곂치는 피쳐 추출
				* - feature 특정 도형(compareFeature) 내에 속한 피쳐 추출
				* - cql cql_filter로 피처 추출
				*/
			extractType: 'draw',
			/*그리기 유형. extractType의 값이 'draw'일 경우 필수값
				* - polygon 다각형
				* - box 사각형
				* - circle 원형
				* - point 점
			*/
			drawType: drawType,
			//draw 타입일 경우, 내부적으로 drawControl을 사용. 그리기 툴팁 메세지 변경가능
			message: {
				DRAWSATRT_POINT: '지도를 클릭하여 도형을 검색하세요.',
				DRAWSTART_POLYGON: '지도를 클릭하여 영역 검색을 시작하세요.',
				DRAWSTART_CIRCLE: '지도를 클릭하여 영역 검색을 시작하세요.',
				DRAWSTART_BOX: '지도를 클릭하여 영역 검색을 시작하세요.',
				DRAWEND_DBCLICK: '더블클릭하여 영역 검색 도형 그리기를 완료하세요.',
			},
			//그리기 영역으로 부터 선택된 피쳐 배열을 리턴받을 콜백 함수. 없으면 selectFeature 함수에서 결과 반환됨(extractType의 값이 'draw' 타입일 경우 제외)
			callback: function (response) {

				var filteredResult = Object.entries(response).filter(([key, value]) => {
					return value.features.length > 0;
				})

				constructInfoDiv(filteredResult, 'featureInfo');

				//iframe 크기 조절
				if (parent.window.containerResize)
					parent.window.containerResize();
			}
		})
	}

	//속성정보 표출 div 생성(레이어 별)
	function constructInfoDiv(list, selector) {
		selector = document.getElementById(selector)
		selector.innerHTML = '';

		for (var i = 0; i < list.length; i++) {
			var [layerId, layerItem] = list[i];
			var layerDiv = document.createElement('div');
			var layerTitle = document.createElement('h5');
			layerTitle.classList.add('title')
			layerTitle.innerHTML = layerId;
			layerDiv.append(layerTitle);

			var featureLen = layerItem.features.length;

			for (var j = 0; j < featureLen; j++) {
				var featureDiv = document.createElement('div');
				var featureTitle = document.createElement('h6');
				featureTitle.classList.add('title')
				featureTitle.innerHTML = `도형-${j + 1}`;
				featureDiv.append(featureTitle);

				constructFeatureInfoTable(layerItem.features[j], featureDiv)

				layerDiv.append(featureDiv);
			}

			selector.append(layerDiv);
		}

	}

	//속성정보 표출 div 생성(피처 별)
	function constructFeatureInfoTable(feature, target) {

		var featureTable = document.createElement('table');
		var properties = Object.entries(feature.getProperties());
		var thead = document.createElement('thead');
		var tbody = document.createElement('tbody');

		var headerRow = document.createElement('tr');
		var bodyRow = document.createElement('tr');

		for (var i = 0; i < properties.length; i++) {
			if (properties[i][0] !== 'geometry') {
				var headerTd = document.createElement('td');
				headerTd.innerText = properties[i][0];
				headerRow.append(headerTd);

				var bodyTd = document.createElement('td');
				bodyTd.innerText = properties[i][1] ? properties[i][1] : '-';
				bodyRow.append(bodyTd);
			}
		}

		thead.append(headerRow);
		tbody.append(bodyRow);
		featureTable.append(thead);
		featureTable.append(tbody);
		target.append(featureTable);

	}
</script>

</html>
