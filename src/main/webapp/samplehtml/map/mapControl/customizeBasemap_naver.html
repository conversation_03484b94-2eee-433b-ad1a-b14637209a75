<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="https://oapi.map.naver.com/openapi/v3/maps.js?ncpClientId=vlf2u84az9"></script>
	<!--
    ★ 네이버 지도 API(Web Dynamic Map)는 10,000,000건 이하 무료입니다. 무료 사용량 이상 사용할 경우 1건당 0.1원 입니다.
    ★ key값 "vlf2u84az9"은 지온파스용 키값으로 예제 소스 사용시 직접 키를 발급받아서 사용하시기 바랍니다. -->
</head>
<style>
	#mapContainer{
		width:100%;
		height: 100%;
		padding:0px;
		margin:0px;
		right:0px;
	}
	.mapContent {
		position:absolute;
		-webkit-overflow-scrolling: touch;
		-webkit-transform: translate3d(0, 0, 0);
		-moz-transform: translate3d(0, 0, 0);
		-ms-transform: translate3d(0, 0, 0);
		-o-transform: translate3d(0, 0, 0);
		transform: translate3d(0, 0, 0);
		overflow: auto;
		width: 1100px;
		height: 550px;
	}
</style>
<body>
	<!--1. odf 맵 요소를 네이버지도 요소 위에 위치시킵니다. (map zindex > naver zIndex) -->
	<div id="mapContainer" style="">
		<div id="naverMap" class="mapContent" style="z-index:1; position: absolute; height:550px;"></div>
		<div id="map" class="mapContent" style="z-index:2; position: absolute; height:550px;"></div>
	</div>
</body>
<script>
	/* 네이버지도 위에 odf 레이어를 표출 하는 로직 설명
    * 1. odf 맵 요소를 네이버지도 요소 위에 위치시킵니다. (map zindex > naver map zIndex)
    * 2. odf 맵 객체 생성시 좌표계와 resolution을 네이버지도에 맞게 설정합니다.
    * 3. odf 맵 객체의 배경지도를 off 합니다.
    * 4. odf 맵 객체에 레이어를 추가합니다.
    * 5. 네이버지도 객체를 생성합니다. 초기 center 값과 zoom 값을 odf 맵 객체의 값으로 설정합니다.
    * 5. odf 맵 배경지도의 중심점이 변경될때 네이버지도도 변경되도록 합니다.
    * 6. odf 맵 배경지도의 resolution이 변경될때 네이버지도도 변경되도록 합니다.
    * */

	/* 2. odf 맵 객체 생성시 좌표계와 resolution을 네이버지도에 맞게 설정합니다. */
	var mapElement = document.getElementById('map');
	var coord = new odf.Coordinate(14232009.094513748, 4344427.004198583);

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapOption = {
		center: coord,
		zoom: 9,
		projection: 'EPSG:3857',
	};

	var map = new odf.Map(mapElement, mapOption);
	map.setViewOption({
		resolutions: [
			2445.98490512499,
			1222.99245256249,
			611.49622628138,
			305.748113140558,
			152.874056570411,
			76.4370282850732,
			38.2185141425366,
			19.1092570712683,
			9.55462853563415,
			4.77731426794937,
			2.38865713397468,
			1.19432856685505,
			0.597164283559817,
			0.298582141647617
		],
		projection: 'EPSG:3857',
		resolutionsFixed : true,
	})

	/* 3. odf 맵 객체의 배경지도를 off 합니다. */
	map.switchLayer('odf-basemap-eMapBasic', false);

	/* 4. odf 맵 객체에 레이어를 추가합니다. */
	var polygonLayer = odf.LayerFactory.produce('geoserver', {
		method: 'get',
		server: '::WfsAPI::',
		layer: '::polygonLayer1::',
		service : 'wfs',
		projection: 'EPSG:::srid::',
	});
	polygonLayer.setMap(map);
	polygonLayer.fit();

	/* 5. 네이버지도 객체를 생성합니다. 초기 center 값과 zoom 값을 odf 맵 객체의 값으로 설정합니다. */
	var naverMap;
	var naverMapInitCenter = map.getProjection().unproject(map.getView().getCenter(),'4326');//맵 센터값을 4326으로 변경
	naverMapInitCenter = {lat: naverMapInitCenter[1],lng: naverMapInitCenter[0]};
	var naverMapInitZoom = map.getView().getZoom();

	async function initNaverMap(position, zoom) {
		naverMap = new naver.maps.Map('naverMap', {
			center: new naver.maps.LatLng(position.lat, position.lng),
			zoom: zoom+6
		});
	}
	initNaverMap(naverMapInitCenter, naverMapInitZoom);


	/* 5. odf 배경지도의 중심점이 변경될때 네이버 배경지도도 변경되도록 하기 */
	odf.event.addListener(map.getView(), 'change:center', (evt) => {
		var center = map.getProjection().unproject(evt.target.getCenter(), '4326');

		const allowedBounds = new naver.maps.LatLngBounds(
				new naver.maps.LatLng(33.0, 124.0), // 남서쪽 경계점
				new naver.maps.LatLng(38.5, 132.0)  // 북동쪽 경계점
		);
		let changeCenter = new naver.maps.LatLng(center[1], center[0]);
		// 네이버지도는 기본적으로 영역제한이 되어있음
		if (!allowedBounds.hasLatLng(changeCenter)) {
			const basicCenterProjection = new odf.Projection({ EPSG: '4326' });
			let naverCenter = naverMap.getCenter();
			const limitedCenter = basicCenterProjection.unproject([naverCenter.x, naverCenter.y], '3857')

			map.setCenter(limitedCenter);
		}
		else{
			naverMap.setCenter(changeCenter);
		}
	})

	/* 6. odf 배경지도의 resolution이 변경될때 네이버 배경지도도 변경되도록 하기 */
	odf.event.addListener(map.getView(), 'change:resolution', (evt) => {
		naverMap.setZoom(Math.round(evt.target.getZoom())+6);

	})
</script>
</html>
