<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" style="height:550px;">
		<div id="map1" style="width: 49.5%; height: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
		<div id="map2" style="width: 49.5%; height: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
		<div id="map3" style="width: 49.5%; height: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
		<div id="map4" style="width: 49.5%; height: 49.5%; float: left; border: 1px black solid; border-collapse: collapse;"></div>
	</div>
	<input type="button" id="chageConnectFlag" class="onoffBtn toggle" value="동기화여부 변경" style="margin-top: 15px">
</body>
<script>

	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";

	// 맵객체1 생성
	var mapContainer = document.getElementById('map1');
	var map1 = new odf.Map(mapContainer, mapOption);

	// 맵객체1에 컨트롤 셋팅
	var basemapControl1 = new odf.BasemapControl();
	basemapControl1.setMap(map1);
	map1.updateSize();

	var zoomControl = new odf.ZoomControl();
	zoomControl.setMap(map1);

	var moveControl = new odf.MoveControl();
	moveControl.setMap(map1);

	//  맵객체2 생성
	var mapContainer2 = document.getElementById('map2');
	var map2 = new odf.Map(mapContainer2, mapOption);

	// 맵객체2에 컨트롤 셋팅
	var basemapControl2 = new odf.BasemapControl();
	basemapControl2.setMap(map2);

	/* 맵객체3 생성 */
	var mapContainer3 = document.getElementById('map3');
	var map3 = new odf.Map(mapContainer3, mapOption);

	// 맵객체3에 컨트롤 셋팅
	var basemapControl3 = new odf.BasemapControl();
	basemapControl3.setMap(map3);

	/* 맵객체4 생성 */
	var mapContainer4 = document.getElementById('map4');
	var map4 = new odf.Map(mapContainer4, mapOption);

	// 맵객체4에 컨트롤 셋팅
	var basemapControl4 = new odf.BasemapControl();
	basemapControl4.setMap(map4);


	// 동기화 여부 변경
	var connectMapFlag = false;
	document.getElementById('chageConnectFlag').addEventListener('click',
		function(evt) {
			if (connectMapFlag) {
				map2.connectOtherMap(map1, false);
				map3.connectOtherMap(map1, false);
				map4.connectOtherMap(map1, false);
			} else {
				map2.connectOtherMap(map1);
				map3.connectOtherMap(map1);
				map4.connectOtherMap(map1);
			}
			connectMapFlag = !connectMapFlag;
		}
	);
</script>
</html>
