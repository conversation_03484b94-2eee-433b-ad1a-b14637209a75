<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/* 줌 컨트롤 생성 */
	var zoomControl = new odf.ZoomControl({
		// 줌 슬라이더 사용여부. true/false
		// ※ 기본값 => false
		zoomSlider : true,
	});
	zoomControl.setMap(map);

	/* 축척 컨트롤 생성 */
	var scaleControl = new odf.ScaleControl({
		// 축척 컨트롤 크기 조정(pixel)
		// ※ 기본값 => 100, 최소 10 최대 2000
		size : 100,

		// 축척 입력 창 사용여부. true/false
		// ※ 기본값 => false
		scaleInput  : false,
	});
	scaleControl.setMap(map);

</script>
</html>
