<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div id="coordDiv" style="height: 25px; font-size: 20px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	/*마우스 좌표 컨트롤 생성*/
	var mousePositionControl = new odf.MousePositionControl({
		//특정 element에 표시
		element : document.querySelector('#coordDiv'),
	//   callback: function (position) {
	//     console.log(position);
	//   },
	});
	mousePositionControl.setMap(map);

	var fullScreenControl  = new odf.FullScreenControl();
	fullScreenControl.setMap(map);
</script>
</html>
