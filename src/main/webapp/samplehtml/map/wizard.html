<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
<title>wizard page</title>
</head>
<link href="::OdfUrl::/odf.css" rel="stylesheet">
<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
<link rel="stylesheet" href="::DeveloperUrl::/vendor/jqtree/dist/themes/default/style.min.css" />
<link rel="stylesheet" href="::DeveloperUrl::/vendor/colorpicker/css/colorpicker.css"/>
<link rel="stylesheet" href="::DeveloperUrl::/css/toc.css"/>
<script type="text/javascript" src="::DeveloperUrl::/vendor/jquery/jquery-1.12.4.js"></script>
<script type="text/javascript" src="::DeveloperUrl::/vendor/jquery/jquery-ui.min.js"></script>
<script type="text/javascript" src="::DeveloperUrl::/vendor/jqtree/dist/jstree.min.js"></script>
<script type="text/javascript" src="::DeveloperUrl::/vendor/colorpicker/js/colorpicker.js"></script>
<script type="text/javascript" src="::DeveloperUrl::/js/sample/toc.js"></script>
<body>
	<div class=toc_content>
	<div id="map" class="odf-view" style="height:550px;"></div>
	</div>
</body>
<script id="wizard_script">
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::, ::coordy::);
	var mapOption = ::mapOpt::;
	var map = new odf.Map(mapContainer, mapOption);
	//마우스 드래그를 통한 지도 이동 허용
	map.setDraggable(true);
	//마우스 휠을 통한 지도 확대, 축소 허용
	map.setZoomable(true);
</script>
</html>
