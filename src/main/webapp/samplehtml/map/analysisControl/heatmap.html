<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* 분석 결과 레이어 추가*/
	var _heatmapLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::', // 분석결과 레이어가 발행된 주소
		layer : '::pointLayer::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		crtfckey : '::crtfckey::',
		service : 'heatmap', // 분석 타입
		radius : 20, //반지름
		blur : 15, // 흐림정도
	});
	_heatmapLayer.setMap(map);
	"::viewOption::"
	map.setZoom(11);

</script>
</html>
