<!DOCTYPE HTML>
<html>
<head>
<meta charset="utf-8">
</head>
<link href="::OdfUrl::/odf.css" rel="stylesheet">
<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div style="margin-top: 15px">
	<div class="btnLogArea">
	<p>그리기 컨트롤</p>
			<input type="button" class="onoffBtn" onclick="drawControl.drawBox()" value="사각형 그리기">
			<input type="button" class="onoffBtn" onclick="drawControl.drawCircle()" value="원 그리기">
			<input type="button" class="onoffBtn" onclick="drawControl.drawCurve()" value="곡선 그리기">
			<input type="button" class="onoffBtn" onclick="drawControl.drawLineString()" value="라인 그리기">
			<input type="button" class="onoffBtn" onclick="drawControl.drawPoint()" value="점 그리기">
			<input type="button" class="onoffBtn" onclick="drawControl.drawPolygon()" value="다각형 그리기">
			<input type="button" class="onoffBtn" onclick="drawControl.drawText()" value="텍스트 입력 그리기">
			<input type="button" class="onoffBtn" onclick="drawVectorLayer()" value="그리기컨트롤 레이어 찾기">
			<input type="button" class="onoffBtn" onclick="activeType()" value="활성화타입 확인 ※현재 그리기 인터렉션 타입 확인">
	<input type="button" class="onoffBtn" onclick="drawControl.clear()" value="그리기중인 인터렉션 종료 ※진행중인 그리기인터렉션  종료">
	</div>
	<div class="btnLogArea">
	<p>측정 컨트롤</p>
			<input type="button" class="onoffBtn" onclick="measureControl.executeArea()" value="면적 측정">
			<input type="button" class="onoffBtn" onclick="measureControl.executeDistance()" value="거리 측정">
			<input type="button" class="onoffBtn" onclick="measureControl.executeRound()" value="반경 측정">
			<input type="button" class="onoffBtn" onclick="measureVectorLayer()" value="측정컨트롤 레이어 찾기">
			<input type="button" class="onoffBtn" onclick="measureControl.executeOff()" value="측정 레이어 삭제 ※측정 레이어 모두 삭제">
			<input type="button" class="onoffBtn" onclick="measureControl.clear()" value="측정중인 인터렉션 종료 ※진행중인 측정 인터렉션  종료">
	</div>
	<div class="btnLogArea">
	<p>초기화 컨트롤</p>
		<input type="button" class="onoffBtn" onclick="clearControl.clear()" value="그리기,측정 레이어 지우기">
	</div>
	</div>
</body>
<script>
	/* 맵 타겟 */
	var mapContainer = document.getElementById('map');

	/* 맵 중심점 */
	var coord = new odf.Coordinate(::coordx::,::coordy::);


	/* 맵객체 옵션 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapOption = "::mapOpt::";
	/*
	 * 배경지도 종류
		eMapBasic - 바로e맵 일반 지도
		eMapColor - 바로e맵 색각 지도
		eMapLowV - 바로e맵 큰글씨 지도
		eMapWhite - 바로e맵 백지도
		eMapEnglish - 바로e맵 영어 지도
		eMapChinese - 바로e맵 중어 지도
		eMapJapanese - 바로e맵 일어 지도
		eMapWhiteEdu - 바로e맵 교육용 백지도
		eMapAIR - 바로e맵  항공지도

	 * 프록시 사용
		proxyURL: 'proxy.jsp' 프록시 설정
	 */

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var map = new odf.Map(mapContainer, mapOption);

	/* 그리기 컨트롤 생성 */
	var drawControl = new odf.DrawControl();
	drawControl.setMap(map, false);

	function activeType(){
		alert("\"" + drawControl.getActiveType() + "\"  활성화 되어있습니다.");
	}
	function drawVectorLayer(){
		alert("콘솔창을 확인하세요")
		var layer = drawControl.findDrawVectorLayer();
		console.log(layer);

	}

	/* 측정 컨트롤 생성 */
	var measureControl = new odf.measureControl();
	measureControl.setMap(map, false);

	function measureVectorLayer(){
		alert("콘솔창을 확인하세요")
		var layer = measureControl.findDrawVectorLayer();
		console.log(layer);
	}
	/* 초기화 컨트롤 생성 */
	var clearControl = new odf.ClearControl();
	clearControl.setMap(map,false);
</script>
</html>
