<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="btnDiv">
		<input type="button" onclick="changeStyle(false)" class="onoffBtn" value="테두리 스타일 1">
		<input type="button" onclick="changeStyle(true)" class="onoffBtn" value="테두리 스타일 2">
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/*포인트 레이어 추가*/
	var pointLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer :  '::pointLayer::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		crtfckey : '::crtfckey::',
		service : 'wfs',
	});
	pointLayer.setMap(map);
	pointLayer.fit();

	/*점 스타일 생성*/
	var pointStyle = odf.StyleFactory.produce({
		image : {
			circle : {
				radius : 25,//크기
				fill : {
					color : [ 0, 0, 0, 0.2 ]
				//채우기 색
				},//채우기
				stroke : {//윤곽선
					color : [ 132, 229, 252, 0.95 ],//테두리 색
					width : 10,//굵기
				//lineDash:[4, 1]//점선 설정
				},
				//축척
				scale :1,
				// snapToPixel : true //true : sharp, false : blur
				/*
				//타원 스타일
				//원래 크기의 n배 [가로  축척, 세로 축척]
				scale : [1, 0.2],
				//회전
				rotation : (Math.PI*30/180),
				//지도 회전시 같이 회전할지 여부
				rotateWithView : true,
				*/
			}
		},
		text : {
			text : '포인트스타일',//텍스트 내용
			//offsetX : 0,//기준점으로부터 텍스트 x좌표 위치 이동
			//offsetY : 0,//기준점으로부터 텍스트 Y좌표 위치 이동
			//rotation : (Math.PI*270/180), //회전
			//textAlign : 'left',//텍스트 수평정렬
			//textBaseline : 'middle',//텍스트 수직정렬
			font : 'bold 14px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션)
			fill : {
				color : [ 0, 0, 0, 0.95 ]
			},
			stroke : {//text 안의 stroke는 lineCap/lineJoin/lineDash/lineDashOffset/miterLimit 옵션 적용  x
				color : [ 255, 255, 255, 0.8 ],
			},
			padding : [ 0.5, 0.5, 0.5, 0.5 ],//text와 background영역 사이의 여백 //placement :'line' 일 경우 미적용
			backgroundStroke : {
				color : 'black'
			},//placement :'line' 일경우 미적용
			backgroundFill : {
				color : 'white'
			},//placement :'line' 일경우 미적용
			//maxAngle : 90*Math.PI/180,//placement :'line' 일경우 적용
			//overflow : false,//placement :'line' 일경우 적용//텍스트를 나열한 길이보다 선이 짧을 경우, 넘치는 글자를 쭉 나열할지 여부
			scale : 1, //텍스트 크기를 정해진 값의 n배로 셋팅
			rotateWithView : true
		//지도가 회전할때 텍스트도 적절하게 회전할지 여부
		}

	});
	pointLayer.setStyle(pointStyle);

	//스타일 변경
	function changeStyle(styleFlag){
		var chagedStyle = pointStyle.modify('image.circle.stroke.color', styleFlag?'pink':[ 132, 229, 252, 0.95 ]);
		pointLayer.setStyle(chagedStyle);
	}
</script>
</html>
