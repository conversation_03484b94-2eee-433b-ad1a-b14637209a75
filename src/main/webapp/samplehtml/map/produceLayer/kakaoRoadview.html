<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
	<script type="text/javascript" src="//dapi.kakao.com/v2/maps/sdk.js?appkey=::kakaoAppKey::"></script>
	<script type="text/javascript" charset="UTF-8" src="//t1.daumcdn.net/mapjsapi/js/main/4.4.3/kakao.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="content-box" style="width: 100%; height: 300px;">
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	//kakao xyz 레이어
	//로드뷰 레이어 생성
	var roadviewLayer = odf.LayerFactory.produce('api', {
		service : 'xyz',
		projection:"EPSG:5181",
		extent:[-30000, -60000, 494288, 988576],
		tileGrid:{
			origin:[-30000,-60000],
			resolutions:[4096,2048,1024,512,256,128,64,32,16,8,4,2,1,0.5,0.25,0.125],
			matrixIds:["0","1","2","3","4","5","6","7","8","9","10","11","12","13","14","15"]
		},
		server :{
			url : '::DeveloperUrl::/proxyUrl.jsp?url=https://map.daumcdn.net/map_k3f_prod/bakery/image_map_png/PNGSD_RV01/v16_o9kb2/{{15-z}}/{{-y-1}}/{{x}}.png',
		}
	});
	roadviewLayer.setMap(map);


	var roadViewTarget;
	var roadviewClient;
	var marker;
	var markerEventId;

	//로드뷰는 영역 따로 잡아야.
	var odfId = odf.event.addListener(map,'click',function(evt){

	if(!kakao){
		alert('kakao.js가 로드되지 않았습니다.');
		return;
	}

	if(marker){
		marker.removeMap();
	}else{
		marker = new odf.Marker({
			position: evt.coordinate, //클릭한 좌표로 생성될 마커의 위치 설정
			style: { //마커의 스타일 설정
				width: '30px', //너비
				height: '30px', //높이
			},
			//draggable: true,
		});
	}
	marker.setMap(map);

	roadviewClient = new kakao.maps.RoadviewClient();
	roadViewTarget = new kakao.maps.Roadview(document.querySelector('.content-box'))

	// 지도좌표계를 카카오지도 좌표계(4326)으로 변경
	var projectionPosition = map.getProjection().unproject(evt.coordinate, '4326');
	// 카카오지도 내에서 사용하는 좌표 객체로 변경
	var kakaoPosition = new kakao.maps.LatLng(projectionPosition[1], projectionPosition[0]);
	// 특정 좌표에서 반경 내 가장 가까운 로드뷰 파노라마 ID를 구해 로드뷰를 설정
	roadviewClient.getNearestPanoId(kakaoPosition, 50, function (panoId) {
		// 파노라마 ID가 null 이면 로드뷰를 숨깁니다
		if (panoId === null) {
			alert('조회되는 로드뷰가 없습니다.');
		} else {
			// panoId로 로드뷰를 설정합니다
			roadViewTarget.setPanoId(panoId, kakaoPosition);
		}
	});

	// 카카오 로드뷰 위치 변경시 이벤트 해제
	kakao.maps.event.removeListener(roadViewTarget, 'position_changed', function () { })
	// 카카오 로드뷰 위치 변경시 이벤트
	kakao.maps.event.addListener(roadViewTarget, 'position_changed', function () {

		var roadViewPosition= [roadViewTarget.getPosition().La, roadViewTarget.getPosition().Ma];
		var mapProjectPosition = map.getProjection().project(roadViewPosition, '4326');

		// 마커 위치 변경
		marker.setPosition(mapProjectPosition);

	})
})

</script>
</html>
