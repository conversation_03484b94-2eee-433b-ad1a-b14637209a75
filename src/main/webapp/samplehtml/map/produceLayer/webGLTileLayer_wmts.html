<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div class="infoArea" id="featureInfo"></div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);


	// wmts 레이어 생성
	// LayerFactory의 produce 함수는 option이 다양하니 개발자지원센터 '지도문서'를 확인하세요
	var wmtsLayer = odf.LayerFactory.produce('geoserver'/*레이어를 생성하기위 한 테이터 호출 방법*/, {
		method : 'get',//'post'
		server : '::WmtsAPI::', // 레이어가 발행된 서버 주소
		layer : '::wmtsLayer::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		service : 'wmts', // 호출하고자 하는 레이여 형태(wms, wfs, wmts)

		//webgGLRender 적용여부
		webGLRender :true,
	}/*레이어 생성을 위한 옵션*/);
	wmtsLayer.setMap(map);
	// 해당 layer가 한눈에 보이는 보여주는 extent로 화면 위치 이동 및 줌 레벨 변경
	wmtsLayer.fit();




	// 레이어 삭제
	// map.removeLayer(wmtsLayer.getODFId());

	// 레이어 on/off
	// map.switchLayer(wmtsLayer.getODFId()/*odf id*/, false/*on/off여부*/);

	// 레이어 z-index 조절
	// map.setZIndex(wmtsLayer.getODFId(), 0);

	// 레이어 가시범위 설정
	// wmtsLayer.setMinZoom(10);
	// wmtsLayer.setMaxResolution(152.70292183870401);
	// wmtsLayer.setMaxZoom(18);
	// wmtsLayer.setMinResolution(0.5964957884324376);

	// 레이어 투명도 조절
	// wmtsLayer.setOpacity(0.5);
</script>
</html>
