<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div style="margin-top: 15px;">
		<button onclick="exchange('geoJson');" class="onoffOnlyBtn">1. geoJson으로 변경</button>
		<button onclick="exchange('clearFeatures');" class="onoffOnlyBtn">2-1. 모든 feature제거</button>
		<button onclick="exchange('removeOneFeature');" class="onoffOnlyBtn">2-2. feature 하나 제거</button>
		<button onclick="exchange('layer');" class="onoffOnlyBtn">3. geoJson으로 feature 생성</button>
		<p>해당 Sample의 결과는 Console 창을 통해 확인 하실 수 있습니다.</p>
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* wfs 레이어 생성 */
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::polygonLayer1::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);
	wfsLayer.fit();

	var geoJson;
	//Layer와 GeoJson 상호변환
	function exchange(param) {

		var result = document.getElementById('result');
		if (param === "geoJson") {
			geoJson = wfsLayer.toGeoJson(); //geoJson으로 변환
			console.log(geoJson);
		}
		else if (param === "clearFeatures") {
			wfsLayer.clearFeatures();//wfsLayer의 모든 feature 제거
		}
		else if (param === "removeOneFeature") {
			if (wfsLayer.getFeatures().length != 0) {
				wfsLayer.removeFeature(wfsLayer.getFeatures()[0]);//wfsLayer의 특정 feature 제거
			}
		}
		else {
			if (!geoJson) {
				alert('geoJson변환을 먼저하세요.');
				return;
			}
			if (wfsLayer.getFeatures().length != 0) {
				alert('모든 feature제거 후 이용하세요.');
				return;
			}
			var newLayer = odf.LayerFactory.fromGeoJson(geoJson); // geoJson으로 Layer를 생성
			newLayer.setMap(map); // 생성된 레이어를 맵객체에 추가
		}
	}
</script>
</html>
