<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<div style="margin-top: 15px">
		<button onclick="chgZIndexFn('wms', 'up');" class="onoffOnlyBtn">wms zIndex +</button>
		<button onclick="chgZIndexFn('wms', 'down');" class="onoffOnlyBtn">wms zIndex -</button>
		<button onclick="chgZIndexFn('wfs', 'up');" class="onoffOnlyBtn">wfs zIndex +</button>
		<button onclick="chgZIndexFn('wfs', 'down');" class="onoffOnlyBtn">wfs zIndex -</button>
	</div>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/* wms 레이어 생성 */
	var wmsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WmsAPI::',
		layer : '::polygonLayer1::',
		service : 'wms',
	});
	wmsLayer.setMap(map);
	wmsLayer.fit();

	/* wfs 레이어 생성 */
	var wfsLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::polygonLayer2::',
		service : 'wfs',
	});
	wfsLayer.setMap(map);


	//z-index 변경
	function chgZIndexFn(type, condition) {
		if (type === 'wms') {
			if (condition === 'up') {
				map.setZIndex(
					wmsLayer.getODFId()/* 레이어의 odfId*/,
					map.getZIndex(wfsLayer.getODFId()) + 1 /*변경할 ZIndex*/
				);
			}
			else if (condition === 'down') {
				map.setZIndex(
					wmsLayer.getODFId()/* 레이어의 odfId*/,
					map.getZIndex(wfsLayer.getODFId()) - 1 /*변경할 ZIndex*/
				);
			}
		} else if (type === 'wfs') {
			if (condition === 'up') {
				map.setZIndex(
					wfsLayer.getODFId()/* 레이어의 odfId*/,
					map.getZIndex(wmsLayer.getODFId()) + 1 /*변경할 ZIndex*/
				);
			}
			else if (condition === 'down') {
				map.setZIndex(
					wfsLayer.getODFId()/* 레이어의 odfId*/,
					map.getZIndex(wmsLayer.getODFId()) - 1 /*변경할 ZIndex*/
				);
			}
		}
	}
</script>
</html>
