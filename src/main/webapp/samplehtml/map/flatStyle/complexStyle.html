<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
</body>
<script>
	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/*포인트 레이어 추가*/
	var pointLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer :  '::pointLayer::', // 발행된 레이어 명칭 (ex. 저장소명:레이어명)
		crtfckey : '::crtfckey::',
		service : 'wfs',
	});
	pointLayer.setMap(map);
	pointLayer.fit();
	map.setZoom(17);

	/*아이콘 스타일 생성*/
	var complexStyle = [
		{
			'circle-radius' : 25,//크기
			'circle-fill-color' : [ 0, 0, 0, 0.2 ],//채우기 색
			'circle-stroke-color' : [ 132, 229, 252, 0.95 ],//운곽선 색
			'circle-stroke-width': 2,//운곽선 굵기
			'circle-scale' : 1,//축척
		},
		{
			'icon-scale':0.7, //크기를 정해진 값의 n배로 셋팅
			'icon-rotation': (Math.PI*30/180),//회전
			'icon-src':'/images/common/ico-main-logo.png', //이미지 경로

			'text-value' : '복합',//텍스트 내용
			'text-font' : '12px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션) (ex) 'bold 10px sans-serif', 기본값 :'10px sans-serif'
			'text-fill-color' :'#CC70B4',//텍스트 색상 색상
			'text-stroke-color' :'white',//텍스트 색상 색상
		}
	]
	pointLayer.setStyle(complexStyle);
</script>
</html>

