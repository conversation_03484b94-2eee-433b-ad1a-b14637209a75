<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8">
	<link href="::OdfUrl::/odf.css" rel="stylesheet">
	<script type="text/javascript" src="::OdfUrl::/odf.min.js"></script>
</head>
<body>
	<div id="map" class="odf-view" style="height:550px;"></div>
	<p>마우스 휠을 이용하여 줌 레벨을 변경해보세요</p>
</body>
<script>

	/* 맵객체 생성 (외부에서 사용할 때는 proxyURL, proxyParam 옵션이 필요합니다.) */
	var mapContainer = document.getElementById('map');
	var coord = new odf.Coordinate(::coordx::,::coordy::);
	var mapOption = "::mapOpt::";
	var map = new odf.Map(mapContainer, mapOption);

	/*면 레이어 추가*/
	var polygonLayer = odf.LayerFactory.produce('geoserver', {
		method : 'get',
		server : '::WfsAPI::',
		layer : '::polygonLayer1::',
		service : 'wfs',
	});
	polygonLayer.setMap(map);
	polygonLayer.fit();

	/*폴리곤 스타일 생성*/
	var dynamicStyleObject = [
		{
			filter: ['>', ['resolution'], 1],
		    style: {
		      'fill-color': [ 0, 255, 100, 0.5 ],
		      'stroke-color': 'green',
		      'stroke-width':3,//윤곽선 두께
			'text-value' :['get','DGM_NM'],//텍스트 내용
			'text-font' : '20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션) (ex) 'bold 10px sans-serif', 기본값 :'10px sans-serif'
			'text-fill-color' :'green',//텍스트 색상 색상
		    }
		},
		{
			filter: ['>', ['resolution'], 3],
		    style: {
		      'fill-color': [ 0, 100, 255, 0.5 ],
		      'stroke-color': 'blue',
		      'stroke-width':3,//윤곽선 두께
			'text-value' :['get','DGM_NM'],//텍스트 내용
			'text-font' : '20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션) (ex) 'bold 10px sans-serif', 기본값 :'10px sans-serif'
			'text-fill-color' :'blue',//텍스트 색상 색상
		    }
		},
		{
			else : true,
		    style: {
		      'fill-color': [206, 252, 254, 0.8],
		      'stroke-color': 'red',
		      'stroke-width':3,//윤곽선 두께
			'text-value' :['get','DGM_NM'],//텍스트 내용
			'text-font' : '20px Courier New',//폰트 크기(필수) 및 글씨체(필수), 두께(옵션) (ex) 'bold 10px sans-serif', 기본값 :'10px sans-serif'
			'text-fill-color' :'red',//텍스트 색상 색상
		    }
		},
	];
	polygonLayer.setStyle(dynamicStyleObject);
</script>
</html>

