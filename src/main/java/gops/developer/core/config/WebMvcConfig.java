package gops.developer.core.config;

import egovframework.com.cmm.service.EgovProperties;
import gops.developer.core.interceptor.DefaultInterceptor;
import nz.net.ultraq.thymeleaf.layoutdialect.LayoutDialect;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.config.annotation.*;
import org.springframework.web.servlet.view.JstlView;
import org.springframework.web.servlet.view.UrlBasedViewResolver;
import org.thymeleaf.dialect.IDialect;
import org.thymeleaf.extras.springsecurity6.dialect.SpringSecurityDialect;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.thymeleaf.spring6.templateresolver.SpringResourceTemplateResolver;
import org.thymeleaf.spring6.view.ThymeleafViewResolver;

import java.util.HashSet;
import java.util.Set;

@Configuration
@ComponentScan(
        basePackages = {"egovframework", "gops.developer"},
        includeFilters = @ComponentScan.Filter(type = FilterType.ANNOTATION, classes = Controller.class),
        excludeFilters = {
                @ComponentScan.Filter(type = FilterType.ANNOTATION, classes = Service.class),
                @ComponentScan.Filter(type = FilterType.ANNOTATION, classes = Repository.class)
        }
)
@EnableWebMvc
public class WebMvcConfig implements WebMvcConfigurer {

    // Interceptor 설정
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(defaultInterceptor())
                .addPathPatterns("/**");
    }

    @Bean
    public DefaultInterceptor defaultInterceptor() {
        return new DefaultInterceptor();
    }

    // View Resolver 설정
    @Bean
    public ViewResolver jspViewResolver() {
        UrlBasedViewResolver resolver = new UrlBasedViewResolver();
        resolver.setViewClass(JstlView.class);
        resolver.setPrefix("/WEB-INF/jsp/");
        resolver.setSuffix(".jsp");
        resolver.setOrder(2);
        resolver.setViewNames("*.jsp");
        return resolver;
    }

    // Static Resources 설정
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
//        registry.addResourceHandler("/asset/**").addResourceLocations("/asset/");
//        registry.addResourceHandler("/css/**").addResourceLocations("/css/");
//        registry.addResourceHandler("/images/**").addResourceLocations("/images/");
//        registry.addResourceHandler("/js/**").addResourceLocations("/js/");
//        registry.addResourceHandler("/lib/**").addResourceLocations("/lib/");
//        registry.addResourceHandler("/vendor/**").addResourceLocations("/vendor/");
//        registry.addResourceHandler("/samplehtml/**").addResourceLocations("/samplehtml/");
//        registry.addResourceHandler("/smt/**").addResourceLocations("/smt/");
//        registry.addResourceHandler("/menual/**").addResourceLocations("/menual/");
//        registry.addResourceHandler("/data/**").addResourceLocations("/data/");
        /* 마지막 패턴이 위의 모든 패턴을 커버함  */
        registry.addResourceHandler("/**").addResourceLocations("/");
    }

    // Thymeleaf 설정
    @Bean
    public SpringResourceTemplateResolver templateResolver() {
        SpringResourceTemplateResolver resolver = new SpringResourceTemplateResolver();
        resolver.setPrefix("/WEB-INF/views/");
        resolver.setTemplateMode("HTML");
        resolver.setCharacterEncoding("UTF-8");
        resolver.setCacheable(false);
        return resolver;
    }

    @Bean
    public SpringTemplateEngine templateEngine() {
        SpringTemplateEngine engine = new SpringTemplateEngine();
        engine.setTemplateResolver(templateResolver());

        Set<IDialect> dialects = new HashSet<>();
        dialects.add(new LayoutDialect());
        dialects.add(new SpringSecurityDialect());
        engine.setAdditionalDialects(dialects);

        return engine;
    }

    @Bean
    public ViewResolver thymeleafViewResolver() {
        ThymeleafViewResolver thymeleafViewResolver = new ThymeleafViewResolver();
        thymeleafViewResolver.setTemplateEngine(templateEngine());
        thymeleafViewResolver.setCharacterEncoding("UTF-8");
        thymeleafViewResolver.setOrder(1);
        return thymeleafViewResolver;
    }

    @Bean
    public EgovProperties egovProperties() {
        return new EgovProperties();
    }

    /*
     * Validator 관련 설정. 사용안하는 듯 해보여 주석처리
    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addViewController("/validator.do").setViewName("/common/validator");
    }
    */
}
