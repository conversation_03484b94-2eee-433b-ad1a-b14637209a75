package gops.developer.core.viewResolver;

import java.util.Locale;

import org.springframework.beans.BeanInstantiationException;
import org.springframework.beans.BeansException;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.AbstractUrlBasedView;
import org.springframework.web.servlet.view.UrlBasedViewResolver;

public class CustomUrlBasedViewResolver extends UrlBasedViewResolver {
	
	@Override
	protected View loadView(String viewName, Locale locale) throws BeansException, BeanInstantiationException, IllegalStateException, Exception {
		
		AbstractUrlBasedView view = buildView(viewName);
		View viewObj = (View) getApplicationContext().getAutowireCapableBeanFactory().initializeBean(view, viewName);

		if(viewName.toLowerCase().endsWith(".html")) {
			return null;
		}
		
		return (view.checkResource(locale) ? viewObj : null);
	}

}
