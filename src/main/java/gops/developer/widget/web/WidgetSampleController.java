package gops.developer.widget.web;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @Class Name : EgovSampleController.java
 * @Description : EgovSample Controller Class
 */

@Controller
public class WidgetSampleController {

	private static final Logger LOGGER = LoggerFactory.getLogger(WidgetSampleController.class);
	
	
	/*
	 * VIEWS
	 */
	@RequestMapping(value = "/widgetsample", method = RequestMethod.GET)
	public String sample() {
		
		return "widget/sample/index.html";
	}

}
