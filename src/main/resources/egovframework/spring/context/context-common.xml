<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd">

    <context:component-scan base-package="egovframework, gops.developer">
       <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller" />
    </context:component-scan>

   	<!-- 메시지소스빈 설정 -->
    <bean id="egovMessageSource" class="egovframework.com.cmm.EgovMessageSource">
<!--        <property name="reloadableResourceBundleMessageSource">-->
<!--            <ref bean="messageSource" />-->
<!--        </property> -->
    </bean>

    <!-- 메시지소스빈 설정..인데 메세지가 없어서 ㄱ -->
<!--    <bean id="egovMessageSource" class="egovframework.com.cmm.EgovMessageSource">-->
<!--        <property name="reloadableResourceBundleMessageSource" ref="messageSource"/>-->
<!--    </bean>-->

<!--    <bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">-->
<!--        <property name="basenames">-->
<!--            <list>-->
<!--                <value>classpath:/egovframework/message/message-common</value>-->
<!--            </list>-->
<!--        </property>-->
<!--        <property name="cacheSeconds" value="60"/>-->
<!--    </bean>-->

<!--	<bean id="leaveaTrace" class="egovframework.rte.fdl.cmmn.trace.LeaveaTrace">-->
<!--		<property name="traceHandlerServices">-->
<!--			<list>-->
<!--				<ref bean="traceHandlerService" />-->
<!--			</list>-->
<!--		</property>-->
<!--	</bean>-->

<!--	<bean id="traceHandlerService" class="egovframework.rte.fdl.cmmn.trace.manager.DefaultTraceHandleManager">-->
<!--		<property name="reqExpMatcher">-->
<!--			<ref bean="antPathMater" />-->
<!--		</property>-->
<!--		<property name="patterns">-->
<!--			<list>-->
<!--				<value>*</value>-->
<!--			</list>-->
<!--		</property>-->
<!--		<property name="handlers">-->
<!--			<list>-->
<!--				<ref bean="defaultTraceHandler" />-->
<!--			</list>-->
<!--		</property>-->
<!--	</bean>-->

	<bean id="antPathMater" class="org.springframework.util.AntPathMatcher" />
<!--	<bean id="defaultTraceHandler" class="egovframework.rte.fdl.cmmn.trace.handler.DefaultTraceHandler" />-->
</beans>
