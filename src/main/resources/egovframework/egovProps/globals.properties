#-----------------------------------------------------------------------
#
#   globals.properties : 시스템
#
#-----------------------------------------------------------------------
#   1.  key = value 구조입니다.
#   2.  key값은 공백문자를 포함불가, value값은 공백문자를 가능
#   3.  key값으로 한글을 사용불가,   value값은 한글사용이 가능
#   4.  줄을 바꿀 필요가 있으면 '\'를 라인의 끝에 추가(만약  '\'문자를 사용해야 하는 경우는 '\\'를 사용)
#   5.  Windows에서의 디렉토리 표시 : '\\' or '/'  ('\' 사용하면 안됨)
#   6.  Unix에서의 디렉토리 표시 : '/'
#   7.  주석문 처리는  #사용
#   8.  value값 뒤에 스페이스가 존재하는 경우 서블릿에서 참조할때는 에러발생할 수 있으므로 trim()하거나 마지막 공백없이 properties 값을 설정할것
#-----------------------------------------------------------------------
#일반
#Url.APIGW = ${GEONPAAS_GATEWAY_URL:http://geon.wavus.co.kr:14062}
Url.APIGW = http://geon.wavus.co.kr:14062
Url.Geoserver = http://geon.wavus.co.kr:14066/geoserver

Url.ContextPath =
Url.WfsAPI =  /api/map/wfs
Url.WmsAPI = /api/map/wms
Url.WmtsAPI = /api/map/wmts

Url.DOCS = /docs/api/
#농협 SWAGGER 호출 분기처리용
Type.DOCS = NORMAL
#일반 KONG SWAGGER 호출 분기처리용
#Type.DOCS = NORMAL

#샘플에 프록시 사용할지 여부(Y, N) N 으로 설정시 프록시 설정부분 주석처리됩니다.
Sample.Proxy.Use = Y
#샘플에 사용할 배경지도 (VWORLD, BAROEMAP, CUSTOM)
Sample.Basemap = CUSTOM
#CUSTOM 배경지도 설정 - JSON 문자열로 설정 (환경변수 ${CUSTOM_BASEMAP_CONFIG}로 오버라이드 가능)
Sample.Basemap.Custom.Config = ${CUSTOM_BASEMAP_CONFIG:}
#지도 예제 SRID (전체 예제 동일하게 적용됨 -> 5179, 5186)
Sample.map.Srid = 5186
#위젯 예제 SRID (전체 예제 동일하게 적용됨 -> 5179, 5186)
Sample.widget.Srid = 5186
#테스트 유저 id
Sample.UserId = geonuser


#샘플용 url
Url.BaroEMapURL = http://geon.wavus.co.kr:14062/map/api/map/baroemap
Url.BaroEMapAirURL = http://geon.wavus.co.kr:14062/map/api/map/ngisair
#Url.BaroEMapKey = 3FA3CDEA6C6EAAA78FDBF09F84A91EA7
Url.VWorldURL =http://geon.wavus.co.kr:14062/map/api/vworld/wmts
#odf 샘플 레이어
Layer.PointLayer = geonpaas:L100000256
Layer.LineLayer = geonpaas:L100000255
Layer.PolygonLayer1 = geonpaas:L100000254
Layer.PolygonLayer2 = geonpaas:L100000258
Layer.WmtsLayer = geonpaas:L100000252
Layer.HotspotLayer = geonpaas:L100001001

#oui 샘플 레이어 (PointLayer1 : [점]제주도 전기차 충전소 / LineLayer1 : [선]제주도 자전거길 / PolygonLayer1 : [면]제주도 서귀포시 읍면동)
Layer.TestPointLayer1 = Wgeonuser:L100000696
Layer.TestPointLayer1Id = LR0000000541
Layer.TestPointLayer1Nm = [점]제주도 전기차 충전소
Layer.TestLineLayer1 =  Wgeonuser:L100000556
Layer.TestLineLayer1Id = LR0000000408
Layer.TestLineLayer1Nm = [선]제주도 자전거길
Layer.TestPolygonLayer1 = Wgeonuser:L100000516
Layer.TestPolygonLayer1Id = LR0000000374
Layer.TestPolygonLayer1Nm = [면]제주도 서귀포시 읍면동
#oui 편집때 사용되는 레이어 ([면]제주도 제주시 행정구역)
Layer.TestEditPolygonLayer1 = Wgeonuser:L100000577
Layer.TestEditPolygonLayer1Id = LR0000000425
Map.UserMapId = UM0000000212


#일반
#Service.API = map,smt,analysis,coord,addrgeo
#농협
#Service.API = :14110,:14120,:14130
Service.API = smt,map,analysis,publish,coord,addrgeo
#API 서비스
#1.저작도구
Service.smt=http://geon.wavus.co.kr:14062/smt
#2.지도관리
Service.map= http://geon.wavus.co.kr:14062/map
#3.도형분석
Service.analysis= http://geon.wavus.co.kr:14062/analysis
#4.도형정보 등록
Service.publish= http://geon.wavus.co.kr:14062/publish
#5.좌표변환 API
Service.coord= http://geon.wavus.co.kr:14062/coord
#6.지오코딩
Service.addrgeo= http://geon.wavus.co.kr:14062/addrgeo

## 워크플로우, 로그, 레이어그룹 API 미사용으로 관련 코드 주석처리
#7.워크플로우 API
#Service.workflow= http://geon.wavus.co.kr:14062/workflow
#8.로그 API
#Service.log= http://geon.wavus.co.kr:14062/log
#9.레이어그룹 API
#Service.layerGroup= http://geon.wavus.co.kr:14062/lyrgroup

#,cdr,bag,est,ctt,adg
Service.API.Order = API 사용 정보 관리,공통 코드 조회,레이어 속성필터 설정,레이어 스타일 설정,레이어 정보 관리,레이어 속성 정보 관리,레이어 컬럼 정보 관리,레이어 팝업 설정,로그인 관리,베이스맵 관리,사용자 북마크 관리,사용자 이미지 관리,사용자 지도 작업 알림 관리,웹레이어서비스 관리,웹맵 관리,웹앱 템플릿 관리,지도 TOC 관리,지오코딩 결과 파일 관리,저장소 정보 관리,테이블 레이어 정보 관리,\
                    지도 요청,컨텐츠관리,포탈 요청,\
                    도형분석-공간패턴 분석,도형분석-근접도 분석,도형분석-데이터 관리 분석,도형분석-데이터 요약 분석,도형분석-위치찾기 분석,레이어 파일 다운로드,아틀란-Api,작업알림,\
                    도형정보 업로드,레이어 관리,\
                    좌표변환,\
                    위치검색,주소정제,지오코딩,행정구역 검색,\
                    라이브러리 정보 관리,라이브러리 파라미터 정보 관리,워크플로우 공유 관리,워크플로우 라이브러리 관리,워크플로우 이력 관리,워크플로우 정보 관리,레이어그룹 정보

#,작업알림,레이어 관리,컨텐츠 정보,지도 요청,\
주소검색,주소정제,지오코딩,좌표변환,공간정보 파일 다운로드,\
				데이터추출,일필지 종합 정보,토지 조회(일필지),건물 조회(일필지),가격 정보 조회(일필지),토지이용계획 조회(일필지),\
				도형영역으로 검색,행정경계로 검색,국가법령정보,정부디렉터리,SMS 메시지
Service.Crtfckey = tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh

#Kakao Key
#AppKey.Kakao = b064950d475be361fd1e97aed8644716
AppKey.Kakao = 1292bf8d492b48c32219cd6cb549c276

#Naver Key
AppKey.Naver = vlf2u84az9

#Google Key
AppKey.Google = AIzaSyCHW_Vs6mcwzRZWYpFxe1EgQ46DNDAPxeA

#브이월드 Key
VWorld.ApiKey = 998FA064-9D48-32C2-8DC8-4DBA90793E9F
VWorld.Domain = https://developer.geon.kr

#국가공간정보 포털 Key
Nsdi.ApiKey = 132d391c091bf7d7e0659e

#국가교통정보센터 Key, Url (CCTV 위젯)
Ntic.ApiKey = 46d62a1ce207492aa55ee90017079639
Ntic.Url = https://openapi.its.go.kr:9443

Url.Tif = https://developer.geon.kr
Url.Pbf = https://developer.geon.kr
