#-----------------------------------------------------------------------
#
#   globals.properties : 시스템
#
#-----------------------------------------------------------------------
#   1.  key = value 구조입니다.
#   2.  key값은 공백문자를 포함불가, value값은 공백문자를 가능
#   3.  key값으로 한글을 사용불가,   value값은 한글사용이 가능
#   4.  줄을 바꿀 필요가 있으면 '\'를 라인의 끝에 추가(만약  '\'문자를 사용해야 하는 경우는 '\\'를 사용)
#   5.  Windows에서의 디렉토리 표시 : '\\' or '/'  ('\' 사용하면 안됨)
#   6.  Unix에서의 디렉토리 표시 : '/'
#   7.  주석문 처리는  #사용
#   8.  value값 뒤에 스페이스가 존재하는 경우 서블릿에서 참조할때는 에러발생할 수 있으므로 trim()하거나 마지막 공백없이 properties 값을 설정할것
#-----------------------------------------------------------------------
Url.ContextPath = /developer
Url.WfsAPI =  /api/map/wfs
Url.WmsAPI = /api/map/wms
Url.WmtsAPI = /api/map/wmts
#Url.APIGW = https://api.gonp.duckdns.org
#일반
#Url.APIGW = http://geon.wavus.co.kr:14062
#농협 api 포트없는 url
Url.APIGW = http://19.24.117.163
#Url.Geoserver = https://geoserver.gonp.duckdns.org/geoserver
Url.Geoserver = http://19.24.117.163:20080/geoserver
Url.DOCS = /docs/api/
#농협 SWAGGER 호출 분기처리용
Type.DOCS = NH
#일반 KONG SWAGGER 호출 분기처리용
#Type.DOCS = NORMAL

#샘플에 사용할 배경지도 (VWORLD, BAROEMAP)
#Sample.Basemap = VWORLD
Sample.Basemap = BAROEMAP
#샘플 예제 SRID (전체 예제 동일하게 적용됨 -> 5179, 5186)
#Sample.Srid = 5179
Sample.Srid = 5186
#샘플용 url
Url.BaroEMapURL = http://19.24.117.163:30120/api/map/baroemap
Url.BaroEMapAirURL = http://19.24.117.163:30120/api/map/ngisair
#Url.BaroEMapKey =
Url.VWorldURL = http://19.24.117.163:30120/api/vworld/wmts
#샘플 레이어
Layer.PointLayer = geonpaas:L100000256
Layer.LineLayer = geonpaas:L100000255
Layer.PolygonLayer1 = geonpaas:L100000254
Layer.PolygonLayer2 = geonpaas:L100000258
Layer.WmtsLayer = geonpaas:L100000252
Layer.HotspotLayer = geonpaas:L100001001

#일반
#Service.API = map,smt,analysis,coord,addrgeo
#농협
Service.API = :30120,:30020,:30040,:30050,:30060,:30090,:30110
#API 서비스
#1.저작도구
Service.smt=http://19.24.117.163:14062
#2.지도관리
Service.map= http://19.24.117.163:14120
#3.도형분석
Service.analysis= http://19.24.117.163:14120
#4.도형정보 등록
Service.publish= http://19.24.117.163:14120
#5.좌표변환 API
Service.coord= http://19.24.117.163:14120
#6.지오코딩
Service.addrgeo= http://19.24.117.163:14120

## 워크플로우, 로그, 레이어그룹 API 미사용으로 관련 코드 주석처리
#7.워크플로우 API
#Service.workflow= http://19.24.117.163:14120
#8.로그 API
#Service.log= http://19.24.117.163:14120
#9.레이어그룹 API
#Service.layerGroup= http://19.24.117.163:14120

#,cdr,bag,est,ctt,adg
Service.API.Order = API 사용 정보 관리,공통 코드 조회,레이어 속성필터 설정,레이어 스타일 설정,레이어 정보 관리,레이어 컬럼 정보 관리,레이어 팝업 설정,베이스맵 관리,사용자 북마크 관리,사용자 이미지 관리,사용자 지도 작업 알림 관리,웹레이어서비스 관리,웹맵 관리,웹앱 템플릿 관리,지도 TOC 관리,지오코딩 결과 파일 관리,\
                    지도 요청,컨텐츠관리,포탈 요청,\
                    도형분석-공간패턴 분석,도형분석-근접도 분석,도형분석-데이터 관리 분석,도형분석-데이터 요약 분석,도형분석-위치찾기 분석,레이어 파일 다운로드,아틀란-Api,작업알림,\
                    도형정보 업로드,레이어 관리,\
                    좌표변환,\
                    위치검색,주소정제,지오코딩,행정구역 검색,\
                    라이브러리 정보 관리,라이브러리 파라미터 정보 관리,워크플로우 공유 관리,워크플로우 라이브러리 관리,워크플로우 이력 관리,워크플로우 정보 관리,레이어그룹 정보

#,작업알림,레이어 관리,컨텐츠 정보,지도 요청,\
				주소검색,주소정제,지오코딩,좌표변환,공간정보 파일 다운로드,\
				데이터추출,일필지 종합 정보,토지 조회(일필지),건물 조회(일필지),가격 정보 조회(일필지),토지이용계획 조회(일필지),\
				도형영역으로 검색,행정경계로 검색,국가법령정보,정부디렉터리,SMS 메시지
APIGW.Crtfckey = tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh

#Kakao Key
AppKey.Kakao = 1292bf8d492b48c32219cd6cb549c276

#브이월드 Key
VWorld.ApiKey = 998FA064-9D48-32C2-8DC8-4DBA90793E9F
VWorld.Domain = https://developer.geon.kr

#국가공간정보 포털 Key
Nsdi.ApiKey = 132d391c091bf7d7e0659e

Url.Tif = https://developer.geon.kr
Url.Pbf = https://developer.geon.kr
