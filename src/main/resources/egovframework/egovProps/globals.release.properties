#-----------------------------------------------------------------------
#
#   globals.release.properties : 시스템
#
#-----------------------------------------------------------------------
#   1.  key = value 구조입니다.
#   2.  key값은 공백문자를 포함불가, value값은 공백문자를 가능
#   3.  key값으로 한글을 사용불가,   value값은 한글사용이 가능
#   4.  줄을 바꿀 필요가 있으면 '\'를 라인의 끝에 추가(만약  '\'문자를 사용해야 하는 경우는 '\\'를 사용)
#   5.  Windows에서의 디렉토리 표시 : '\\' or '/'  ('\' 사용하면 안됨)
#   6.  Unix에서의 디렉토리 표시 : '/'
#   7.  주석문 처리는  #사용
#   8.  value값 뒤에 스페이스가 존재하는 경우 서블릿에서 참조할때는 에러발생할 수 있으므로 trim()하거나 마지막 공백없이 properties 값을 설정할것
#-----------------------------------------------------------------------

Url.WfsAPI =  /bag/api/map/wfs
Url.WmsAPI = /bag/api/map/wms
Url.WmtsAPI = /bag/api/map/wmts
Url.APIGW = http://*************:8000
Url.ODF = http://localhost:8080
Url.DOCS = /docs/api/

Service.ODF = /dev
Service.Layer = /bag
Service.API = ana,pub,cdr,bag,est,ctt,adg
Service.API.Order = 도형분석,도형정보 파일 업로드,작업알림,레이어 관리,컨텐츠 정보,지도 요청,\
				주소검색,주소정제,지오코딩,좌표변환,공간정보 파일 다운로드,\
				데이터추출,일필지 종합 정보,토지 조회(일필지),건물 조회(일필지),가격 정보 조회(일필지),토지이용계획 조회(일필지),\
				도형영역으로 검색,행정경계로 검색,국가법령정보,정부디렉터리,SMS 메시지
sysSeCode = 05
APIGW.Apikey = tmiKPqf1niMu5rq1VcG49XKIYmhwDJEh

#샘플에 사용할 배경지도 (VWORLD, BAROEMAP)
Sample.Basemap = VWORLD
#Sample.Basemap = BAROEMAP

#샘플 예제 SRID (전체 예제 동일하게 적용됨 -> 5179, 5186)
#Sample.Srid = 5179
Sample.Srid = 5186

#Kakao Key
AppKey.Kakao = 1292bf8d492b48c32219cd6cb549c276

#브이월드 Key
VWorld.ApiKey = 998FA064-9D48-32C2-8DC8-4DBA90793E9F
VWorld.Domain = https://developer.geon.kr

#국가공간정보 포털 Key
Nsdi.ApiKey = 132d391c091bf7d7e0659e

Url.Tif = https://developer.geon.kr
Url.Pbf = https://developer.geon.kr
