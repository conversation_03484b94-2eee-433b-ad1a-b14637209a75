#!/bin/bash

# CUSTOM 배경지도 설정을 외부 파일로 설정하는 스크립트

# 1. JSON 설정 파일 경로 설정
export CUSTOM_BASEMAP_CONFIG_FILE="$(pwd)/config/custom-basemap-default.json"

echo "CUSTOM_BASEMAP_CONFIG_FILE 환경변수가 설정되었습니다:"
echo "$CUSTOM_BASEMAP_CONFIG_FILE"

# 파일 존재 여부 확인
if [ -f "$CUSTOM_BASEMAP_CONFIG_FILE" ]; then
    echo "설정 파일이 존재합니다."
    echo "파일 내용:"
    cat "$CUSTOM_BASEMAP_CONFIG_FILE"
else
    echo "경고: 설정 파일이 존재하지 않습니다: $CUSTOM_BASEMAP_CONFIG_FILE"
fi

# 애플리케이션 실행 (예시)
# java -jar target/developer-center.war
