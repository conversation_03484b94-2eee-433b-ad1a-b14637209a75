<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CUSTOM 배경지도 통합 테스트</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .config-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-results {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CUSTOM 배경지도 통합 테스트</h1>
        <p>이 페이지는 CUSTOM 배경지도 설정이 올바르게 작동하는지 테스트합니다.</p>

        <!-- 환경변수 테스트 -->
        <div class="test-section">
            <h3>1. 환경변수 설정 테스트</h3>
            <p>환경변수 CUSTOM_BASEMAP_CONFIG와 CUSTOM_BASEMAP_CONFIG_FILE의 설정 상태를 확인합니다.</p>
            <button onclick="testEnvironmentVariables()">환경변수 테스트</button>
            <div id="env-test-results" class="test-results"></div>
        </div>

        <!-- 설정 로드 테스트 -->
        <div class="test-section">
            <h3>2. 설정 로드 테스트</h3>
            <p>BasemapUtils의 설정 로드 및 검증 기능을 테스트합니다.</p>
            <button onclick="testConfigLoading()">설정 로드 테스트</button>
            <div id="config-test-results" class="test-results"></div>
        </div>

        <!-- JSON 파싱 테스트 -->
        <div class="test-section">
            <h3>3. JSON 파싱 테스트</h3>
            <p>다양한 JSON 형식의 설정이 올바르게 파싱되는지 테스트합니다.</p>
            <button onclick="testJsonParsing()">JSON 파싱 테스트</button>
            <div id="json-test-results" class="test-results"></div>
        </div>

        <!-- 배경지도 옵션 생성 테스트 -->
        <div class="test-section">
            <h3>4. 배경지도 옵션 생성 테스트</h3>
            <p>CUSTOM 설정으로부터 올바른 배경지도 옵션이 생성되는지 테스트합니다.</p>
            <button onclick="testBasemapOptionGeneration()">옵션 생성 테스트</button>
            <div id="option-test-results" class="test-results"></div>
        </div>

        <!-- 통합 테스트 -->
        <div class="test-section">
            <h3>5. 통합 테스트</h3>
            <p>전체 CUSTOM 배경지도 기능이 통합적으로 작동하는지 테스트합니다.</p>
            <button onclick="runIntegrationTest()">통합 테스트 실행</button>
            <div id="integration-test-results" class="test-results"></div>
        </div>
    </div>

    <!-- basemap-config.js 로드 (실제 환경에서는 이미 로드되어 있음) -->
    <script>
        // 테스트용 샘플 설정
        const sampleConfigs = {
            valid: {
                "custom": {
                    "esri_lrc_cache": {
                        "type": "api",
                        "name": "ESRI LRC 타일 캐시",
                        "params": {
                            "server": {
                                "url": "http://**************:28083/tiles/2025/{L}/{R}/{C}.jpg",
                                "proxyURL": "proxyUrl.jsp",
                                "proxyParam": "url"
                            },
                            "service": "esriLrc",
                            "projection": "EPSG:5186",
                            "tileGrid": {
                                "origin": [-5423200, 6394600],
                                "resolutions": [
                                    1058.3354500042335,
                                    529.16772500211675,
                                    264.58386250105838,
                                    132.29193125052919,
                                    66.145965625264594,
                                    26.458386250105836,
                                    13.229193125052918,
                                    7.9375158750317505,
                                    2.6458386250105836,
                                    1.3229193125052918,
                                    0.66145965625264591,
                                    0.26458386250105836
                                ],
                                "tileSize": 256
                            }
                        }
                    }
                }
            },
            invalid: '{"custom":{"invalid_structure"}}',
            empty: '',
            malformed: '{"custom":{"esri_lrc_cache":{'
        };

        function displayResult(containerId, status, message, details = '') {
            const container = document.getElementById(containerId);
            const statusClass = status === 'success' ? 'success' : status === 'warning' ? 'warning' : 'error';
            
            container.innerHTML = `
                <div class="status ${statusClass}">
                    <strong>${status.toUpperCase()}:</strong> ${message}
                </div>
                ${details ? `<div class="config-display">${details}</div>` : ''}
            `;
        }

        function testEnvironmentVariables() {
            try {
                // 실제 환경에서는 서버에서 전달된 값을 사용
                const customConfig = typeof customBasemapConfig !== 'undefined' ? customBasemapConfig : '';
                
                let message = '환경변수 테스트 완료';
                let details = `CUSTOM_BASEMAP_CONFIG: ${customConfig || '(설정되지 않음)'}`;
                
                if (customConfig && customConfig.trim()) {
                    displayResult('env-test-results', 'success', message, details);
                } else {
                    displayResult('env-test-results', 'warning', '환경변수가 설정되지 않음', details);
                }
            } catch (error) {
                displayResult('env-test-results', 'error', '환경변수 테스트 실패', error.message);
            }
        }

        function testConfigLoading() {
            try {
                if (typeof BasemapUtils === 'undefined') {
                    displayResult('config-test-results', 'error', 'BasemapUtils가 로드되지 않음', 'basemap-config.js 파일을 확인하세요.');
                    return;
                }

                const config = BasemapUtils.getBasemapConfig('CUSTOM');
                if (config && config.defaultConfig) {
                    displayResult('config-test-results', 'success', '설정 로드 성공', 
                        `기본 설정:\n${JSON.stringify(config.defaultConfig, null, 2)}`);
                } else {
                    displayResult('config-test-results', 'error', '설정 로드 실패', 'CUSTOM 설정을 찾을 수 없습니다.');
                }
            } catch (error) {
                displayResult('config-test-results', 'error', '설정 로드 테스트 실패', error.message);
            }
        }

        function testJsonParsing() {
            try {
                const results = [];
                
                // 유효한 JSON 테스트
                try {
                    const validConfig = JSON.stringify(sampleConfigs.valid);
                    const parsed = JSON.parse(validConfig);
                    results.push('✓ 유효한 JSON 파싱 성공');
                } catch (e) {
                    results.push('✗ 유효한 JSON 파싱 실패: ' + e.message);
                }

                // 빈 문자열 테스트
                const emptyResult = sampleConfigs.empty === '' ? '✓ 빈 문자열 처리 성공' : '✗ 빈 문자열 처리 실패';
                results.push(emptyResult);

                // 잘못된 JSON 테스트
                try {
                    JSON.parse(sampleConfigs.malformed);
                    results.push('✗ 잘못된 JSON이 파싱됨 (예상하지 못한 결과)');
                } catch (e) {
                    results.push('✓ 잘못된 JSON 오류 처리 성공');
                }

                displayResult('json-test-results', 'success', 'JSON 파싱 테스트 완료', results.join('\n'));
            } catch (error) {
                displayResult('json-test-results', 'error', 'JSON 파싱 테스트 실패', error.message);
            }
        }

        function testBasemapOptionGeneration() {
            try {
                if (typeof BasemapUtils === 'undefined') {
                    displayResult('option-test-results', 'error', 'BasemapUtils가 로드되지 않음');
                    return;
                }

                const testConfig = JSON.stringify(sampleConfigs.valid);
                const option = BasemapUtils.generateBasemapOption('CUSTOM', {
                    customConfig: testConfig
                });

                if (option && option.includes('basemap')) {
                    displayResult('option-test-results', 'success', '배경지도 옵션 생성 성공', 
                        `생성된 옵션:\n${option}`);
                } else {
                    displayResult('option-test-results', 'error', '배경지도 옵션 생성 실패', 
                        `결과: ${option}`);
                }
            } catch (error) {
                displayResult('option-test-results', 'error', '옵션 생성 테스트 실패', error.message);
            }
        }

        function runIntegrationTest() {
            try {
                const results = [];
                let allPassed = true;

                // 1. BasemapUtils 존재 확인
                if (typeof BasemapUtils !== 'undefined') {
                    results.push('✓ BasemapUtils 로드됨');
                } else {
                    results.push('✗ BasemapUtils 로드되지 않음');
                    allPassed = false;
                }

                // 2. CUSTOM 설정 존재 확인
                if (typeof BasemapUtils !== 'undefined') {
                    const customConfig = BasemapUtils.getBasemapConfig('CUSTOM');
                    if (customConfig) {
                        results.push('✓ CUSTOM 배경지도 설정 존재');
                        
                        // 3. 기본 설정 확인
                        if (customConfig.defaultConfig) {
                            results.push('✓ 기본 설정 존재');
                        } else {
                            results.push('✗ 기본 설정 없음');
                            allPassed = false;
                        }

                        // 4. 옵션 생성 함수 확인
                        if (typeof customConfig.option === 'function') {
                            results.push('✓ 옵션 생성 함수 존재');
                        } else {
                            results.push('✗ 옵션 생성 함수 없음');
                            allPassed = false;
                        }
                    } else {
                        results.push('✗ CUSTOM 배경지도 설정 없음');
                        allPassed = false;
                    }
                }

                // 5. 환경변수 처리 확인
                const envConfig = typeof customBasemapConfig !== 'undefined' ? customBasemapConfig : '';
                if (envConfig) {
                    results.push('✓ 환경변수 설정 감지됨');
                } else {
                    results.push('⚠ 환경변수 설정 없음 (기본 설정 사용)');
                }

                const status = allPassed ? 'success' : 'error';
                const message = allPassed ? '모든 통합 테스트 통과' : '일부 테스트 실패';
                
                displayResult('integration-test-results', status, message, results.join('\n'));
            } catch (error) {
                displayResult('integration-test-results', 'error', '통합 테스트 실행 실패', error.message);
            }
        }

        // 페이지 로드 시 자동으로 기본 테스트 실행
        window.addEventListener('load', function() {
            console.log('CUSTOM 배경지도 테스트 페이지 로드됨');
            
            // BasemapUtils가 로드되어 있는지 확인
            if (typeof BasemapUtils === 'undefined') {
                console.warn('BasemapUtils가 로드되지 않았습니다. basemap-config.js를 먼저 로드하세요.');
            }
        });
    </script>
</body>
</html>
