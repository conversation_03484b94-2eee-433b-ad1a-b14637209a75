#!/bin/bash
# Linux/Unix 환경에서 CUSTOM basemap 설정을 위한 환경변수 설정 스크립트

# JSON 파일에서 읽어서 환경변수로 설정 (공백과 개행 제거)
export CUSTOM_BASEMAP_CONFIG=$(cat config/custom-basemap-config.json | tr -d '\n\r' | tr -s ' ')

# 또는 직접 JSON 문자열로 설정
# export CUSTOM_BASEMAP_CONFIG='{"custom":{"esri_lrc_cache":{"type":"api","name":"ESRI LRC 타일 캐시","params":{"server":{"url":"http://121.163.19.101:28083/tiles/2025/{L}/{R}/{C}.png","proxyURL":"proxyUrl.jsp","proxyParam":"url"},"service":"esriLrc","projection":"EPSG:5186","tileGrid":{"origin":[-5423200,6394600],"resolutions":[1058.3354500042335,529.16772500211675,264.58386250105838,132.29193125052919,66.145965625264594,26.458386250105836,13.229193125052918,7.9375158750317505,2.6458386250105836,1.3229193125052918,0.66145965625264591,0.26458386250105836],"tileSize":256}}}}}'

echo "CUSTOM_BASEMAP_CONFIG 환경변수가 설정되었습니다:"
echo "$CUSTOM_BASEMAP_CONFIG"

# 애플리케이션 실행 (예시)
# java -jar target/developer-center.war
