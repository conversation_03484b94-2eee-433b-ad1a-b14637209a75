<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CUSTOM Basemap 설정 테스트</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        textarea {
            width: 100%;
            height: 200px;
            font-family: monospace;
            font-size: 12px;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>CUSTOM Basemap 설정 테스트</h1>
        <p>이 페이지는 CUSTOM basemap 설정의 JSON 구조를 테스트하고 검증하는 도구입니다.</p>

        <div class="test-section">
            <h3>1. JSON 설정 입력</h3>
            <textarea id="configInput" placeholder="CUSTOM basemap JSON 설정을 입력하세요..."></textarea>
            <br>
            <button onclick="loadSampleConfig()">샘플 설정 로드</button>
            <button onclick="validateConfig()">설정 검증</button>
            <button onclick="generateBasemapOption()">Basemap 옵션 생성</button>
            <button onclick="clearInput()">초기화</button>
        </div>

        <div class="test-section">
            <h3>2. 검증 결과</h3>
            <div id="validationResult" class="result info">설정을 입력하고 검증 버튼을 클릭하세요.</div>
        </div>

        <div class="test-section">
            <h3>3. 생성된 Basemap 옵션</h3>
            <div id="basemapResult" class="result info">Basemap 옵션 생성 버튼을 클릭하세요.</div>
        </div>

        <div class="test-section">
            <h3>4. 환경변수 형식 (한 줄)</h3>
            <div id="envVarResult" class="result info">JSON을 환경변수 형식으로 변환된 결과가 여기에 표시됩니다.</div>
        </div>
    </div>

    <!-- basemap-config.js 로드 -->
    <script src="../src/main/webapp/js/basemap-config.js"></script>
    
    <script>
        // 샘플 설정 데이터
        const sampleConfig = {
            "custom": {
                "esri_lrc_cache": {
                    "type": "api",
                    "name": "ESRI LRC 타일 캐시",
                    "params": {
                        "server": {
                            "url": "http://**************:28083/tiles/2025/{L}/{R}/{C}.png",
                            "proxyURL": "proxyUrl.jsp",
                            "proxyParam": "url"
                        },
                        "service": "esriLrc",
                        "projection": "EPSG:5186",
                        "tileGrid": {
                            "origin": [-5423200, 6394600],
                            "resolutions": [
                                1058.3354500042335,
                                529.16772500211675,
                                264.58386250105838,
                                132.29193125052919,
                                66.145965625264594,
                                26.458386250105836,
                                13.229193125052918,
                                7.9375158750317505,
                                2.6458386250105836,
                                1.3229193125052918,
                                0.66145965625264591,
                                0.26458386250105836
                            ],
                            "tileSize": 256
                        }
                    }
                }
            }
        };

        function loadSampleConfig() {
            document.getElementById('configInput').value = JSON.stringify(sampleConfig, null, 2);
        }

        function validateConfig() {
            const input = document.getElementById('configInput').value;
            const resultDiv = document.getElementById('validationResult');
            
            if (!input.trim()) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '설정을 입력해주세요.';
                return;
            }

            try {
                const validation = BasemapUtils.validateCustomBasemapConfig(input);
                
                if (validation.isValid) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✓ 설정이 유효합니다!\n\n검증된 설정:\n' + JSON.stringify(validation.config, null, 2);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '✗ 설정에 오류가 있습니다:\n\n' + validation.errors.join('\n');
                }
            } catch (e) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ 검증 중 오류 발생: ' + e.message;
            }
        }

        function generateBasemapOption() {
            const input = document.getElementById('configInput').value;
            const resultDiv = document.getElementById('basemapResult');
            
            if (!input.trim()) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '설정을 입력해주세요.';
                return;
            }

            try {
                const basemapOption = BasemapUtils.generateBasemapOption('CUSTOM', {
                    customConfig: input
                });
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '생성된 Basemap 옵션:\n\n' + basemapOption;
                
                // 환경변수 형식도 생성
                const envVarDiv = document.getElementById('envVarResult');
                const compactJson = JSON.stringify(JSON.parse(input));
                envVarDiv.className = 'result info';
                envVarDiv.textContent = '환경변수 설정 (Windows):\nset CUSTOM_BASEMAP_CONFIG=' + compactJson + '\n\n환경변수 설정 (Linux/Unix):\nexport CUSTOM_BASEMAP_CONFIG=\'' + compactJson + '\'';
                
            } catch (e) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '✗ Basemap 옵션 생성 중 오류 발생: ' + e.message;
            }
        }

        function clearInput() {
            document.getElementById('configInput').value = '';
            document.getElementById('validationResult').className = 'result info';
            document.getElementById('validationResult').textContent = '설정을 입력하고 검증 버튼을 클릭하세요.';
            document.getElementById('basemapResult').className = 'result info';
            document.getElementById('basemapResult').textContent = 'Basemap 옵션 생성 버튼을 클릭하세요.';
            document.getElementById('envVarResult').className = 'result info';
            document.getElementById('envVarResult').textContent = 'JSON을 환경변수 형식으로 변환된 결과가 여기에 표시됩니다.';
        }

        // 페이지 로드 시 샘플 설정 자동 로드
        window.onload = function() {
            loadSampleConfig();
        };
    </script>
</body>
</html>
