# CUSTOM 배경지도 설정 예시 (YAML 형식)
# 이 파일은 CUSTOM 배경지도 설정의 예시를 보여줍니다.
# JSON 형식으로 변환하여 환경변수 CUSTOM_BASEMAP_CONFIG에 설정하거나
# 별도의 JSON 파일로 저장하여 사용할 수 있습니다.

custom:
  esri_lrc_cache:
    type: "api"
    name: "ESRI LRC 타일 캐시"
    params:
      server:
        url: "http://121.163.19.101:28083/tiles/2025/{L}/{R}/{C}.jpg"
        proxyURL: "proxyUrl.jsp"
        proxyParam: "url"
      service: "esriLrc"
      projection: "EPSG:5186"
      tileGrid:
        origin: [-5423200, 6394600]
        resolutions:
          - 1058.3354500042335   # L0
          - 529.16772500211675   # L1
          - 264.58386250105838   # L2
          - 132.29193125052919   # L3
          - 66.145965625264594   # L4
          - 26.458386250105836   # L5
          - 13.229193125052918   # L6
          - 7.9375158750317505   # L7
          - 2.6458386250105836   # L8
          - 1.3229193125052918   # L9
          - 0.66145965625264591  # L10
          - 0.26458386250105836  # L11
        tileSize: 256

# 다른 타일 서비스 예시들:

# OpenStreetMap 타일 서비스 예시
# osm_tiles:
#   type: "api"
#   name: "OpenStreetMap 타일"
#   params:
#     server:
#       url: "https://tile.openstreetmap.org/{z}/{x}/{y}.png"
#       proxyURL: "proxyUrl.jsp"
#       proxyParam: "url"
#     service: "xyz"
#     projection: "EPSG:3857"
#     tileGrid:
#       origin: [-20037508.342789244, 20037508.342789244]
#       resolutions:
#         - 156543.03392804097
#         - 78271.51696402048
#         - 39135.75848201024
#         - 19567.87924100512
#         - 9783.93962050256
#         - 4891.96981025128
#         - 2445.98490512564
#         - 1222.99245256282
#         - 611.49622628141
#         - 305.748113140705
#         - 152.8740565703525
#         - 76.43702828517625
#         - 38.21851414258813
#         - 19.109257071294063
#         - 9.554628535647032
#         - 4.777314267823516
#         - 2.388657133911758
#         - 1.194328566955879
#         - 0.5971642834779395
#       tileSize: 256

# WMS 서비스 예시
# wms_service:
#   type: "geoserver"
#   name: "WMS 배경지도"
#   params:
#     method: "get"
#     service: "wms"
#     server: "http://your-geoserver.com/geoserver/wms"
#     layer: "your:layer_name"
#     crtfckey: "your_certificate_key"
#     projection: "EPSG:5186"

# WMTS 서비스 예시
# wmts_service:
#   type: "geoserver"
#   name: "WMTS 배경지도"
#   params:
#     method: "get"
#     service: "wmts"
#     server: "http://your-geoserver.com/geoserver/gwc/service/wmts"
#     layer: "your:layer_name"
#     crtfckey: "your_certificate_key"
#     projection: "EPSG:5186"
