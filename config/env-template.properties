# 개발자센터 환경변수 설정 템플릿
# 이 파일을 복사하여 각 환경에 맞게 수정하세요.

# =============================================================================
# CUSTOM BASEMAP 설정
# =============================================================================

# CUSTOM basemap JSON 설정 (한 줄로 작성)
# 아래는 예시이며, 실제 환경에 맞게 수정하세요.
CUSTOM_BASEMAP_CONFIG={"custom":{"esri_lrc_cache":{"type":"api","name":"ESRI LRC 타일 캐시","params":{"server":{"url":"http://121.163.19.101:28083/tiles/2025/{L}/{R}/{C}.png","proxyURL":"proxyUrl.jsp","proxyParam":"url"},"service":"esriLrc","projection":"EPSG:5186","tileGrid":{"origin":[-5423200,6394600],"resolutions":[1058.3354500042335,529.16772500211675,264.58386250105838,132.29193125052919,66.145965625264594,26.458386250105836,13.229193125052918,7.9375158750317505,2.6458386250105836,1.3229193125052918,0.66145965625264591,0.26458386250105836],"tileSize":256}}}}}

# =============================================================================
# 다른 환경변수들 (필요시 추가)
# =============================================================================

# 데이터베이스 설정
# DB_URL=*************************************************
# DB_USERNAME=developer
# DB_PASSWORD=password

# API Gateway 설정
# GEONPAAS_GATEWAY_URL=https://geon-gateway.geon.kr

# 로그 레벨
# LOG_LEVEL=INFO

# JVM 옵션
# JAVA_OPTS=-Xmx2g -Xms1g -XX:+UseG1GC

# =============================================================================
# 환경별 설정 예시
# =============================================================================

# 개발 환경
# CUSTOM_BASEMAP_CONFIG={"custom":{"esri_lrc_cache":{"type":"api","name":"개발 서버 타일","params":{"server":{"url":"http://dev-server:8080/tiles/{L}/{R}/{C}.png","proxyURL":"proxyUrl.jsp","proxyParam":"url"},"service":"esriLrc","projection":"EPSG:5186","tileGrid":{"origin":[-5423200,6394600],"resolutions":[1058.3354500042335,529.16772500211675],"tileSize":256}}}}}

# 스테이징 환경
# CUSTOM_BASEMAP_CONFIG={"custom":{"esri_lrc_cache":{"type":"api","name":"스테이징 서버 타일","params":{"server":{"url":"http://staging-server:8080/tiles/{L}/{R}/{C}.png","proxyURL":"proxyUrl.jsp","proxyParam":"url"},"service":"esriLrc","projection":"EPSG:5186","tileGrid":{"origin":[-5423200,6394600],"resolutions":[1058.3354500042335,529.16772500211675],"tileSize":256}}}}}

# 운영 환경
# CUSTOM_BASEMAP_CONFIG={"custom":{"esri_lrc_cache":{"type":"api","name":"운영 서버 타일","params":{"server":{"url":"https://prod-server.example.com/tiles/{L}/{R}/{C}.png","proxyURL":"proxyUrl.jsp","proxyParam":"url"},"service":"esriLrc","projection":"EPSG:5186","tileGrid":{"origin":[-5423200,6394600],"resolutions":[1058.3354500042335,529.16772500211675,264.58386250105838,132.29193125052919,66.145965625264594,26.458386250105836,13.229193125052918,7.9375158750317505,2.6458386250105836,1.3229193125052918,0.66145965625264591,0.26458386250105836],"tileSize":256}}}}}

# =============================================================================
# 사용법
# =============================================================================

# 1. Windows에서 환경변수 설정:
#    set CUSTOM_BASEMAP_CONFIG={"custom":...}
#    java -jar developer-center.war

# 2. Linux/Unix에서 환경변수 설정:
#    export CUSTOM_BASEMAP_CONFIG='{"custom":...}'
#    java -jar developer-center.war

# 3. Docker에서 환경변수 설정:
#    docker run -e CUSTOM_BASEMAP_CONFIG='{"custom":...}' developer-center

# 4. systemd 서비스에서 환경변수 설정:
#    Environment="CUSTOM_BASEMAP_CONFIG={\"custom\":...}"

# 5. Kubernetes에서 환경변수 설정:
#    env:
#    - name: CUSTOM_BASEMAP_CONFIG
#      value: '{"custom":...}'
