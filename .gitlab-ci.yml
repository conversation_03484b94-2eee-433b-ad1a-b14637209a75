variables:
  DOCKER_TLS_CERTDIR: "/certs"
  MAVEN_OPTS: >-
    -Dhttps.protocols=TLSv1.2 
    -Dmaven.repo.local=$CI_PROJECT_DIR/.m2/repository 
    -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN 
    -Dorg.slf4j.simpleLogger.showDateTime=true 
    -Djava.awt.headless=true
  MAVEN_CLI_OPTS: >-
    --batch-mode 
    --errors 
    --fail-at-end 
    --show-version 
    -DinstallAtEnd=true 
    -DdeployAtEnd=true
  DEPLOY_NAME: "dev"
  GS_DEVLOPER: "$GS_HOME/developer"

image: maven:3-eclipse-temurin-17-alpine

cache:
  paths:
    - .m2/repository
  key: "$CI_BUILD_REF_NAME"

stages:
  - build
  - deploy

maven-build:
  stage: build
  script:
    - "mvn $MAVEN_CLI_OPTS clean package -DskipTests"
  artifacts:
    expire_in: 1d
    paths:
      - target/*.war

deploy:
  image: alpine:latest
  stage: deploy
  before_script:
    - 'which ssh-agent || (apk update && apk add openssh-client)'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -p 22 $GS_SERVER >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts
  script:
    - scp config/geon-global.properties geon@$GS_SERVER:$GS_DEVLOPER/globals.properties
    - scp target/*.war geon@$GS_SERVER:$GS_DEVLOPER/developer.war
    - scp Dockerfile geon@$GS_SERVER:$GS_DEVLOPER/Dockerfile
    - ssh geon@$GS_SERVER "cd $GS_DEVLOPER; bash ./build.sh; bash ./run-developer.sh"

#maven-test:
#  stage: test
#  script:
#    - "mvn $MAVEN_CLI_OPTS test"
#
#docker-build:
#  image: docker:latest
#  stage: package
#  services:
#    - docker:dind
#
#  before_script:
#    - docker login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" $CI_REGISTRY
#
#  script:
#    - docker pull $CI_REGISTRY_IMAGE:latest || true
#    - docker build --cache-from $CI_REGISTRY_IMAGE:latest --tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA --tag $CI_REGISTRY_IMAGE:latest .
#    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
#    - docker push $CI_REGISTRY_IMAGE:latest
#
#deploy-image:
#  image: alpine:latest
#  stage: deploy
#  before_script:
#    - 'which ssh-agent || (apk update && apk add openssh-client)'
#    - eval $(ssh-agent -s)
#    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null
#    - mkdir -p ~/.ssh
#    - chmod 700 ~/.ssh
#    - ssh-keyscan -p 22 $GS_DEVCNTR_SERVER_IP >> ~/.ssh/known_hosts
#    - chmod 644 ~/.ssh/known_hosts
#  script:
#     copy properties
#    - scp config/geon-global.properties $DEVCNTR_SERVER_USER@$GS_DEVCNTR_SERVER_IP:/config/geon-global.properties
#    - ssh $DEVCNTR_SERVER_USER@$GS_DEVCNTR_SERVER_IP "docker login $CI_REGISTRY -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD && docker pull $CI_REGISTRY_IMAGE:latest && ~/run_stop_remote.sh $DEPLOY_NAME && docker run -d --restart=unless-stopped -p 8080:8080 -v /data/dev/conf:/usr/local/tomcat/conf --name $DEPLOY_NAME $CI_REGISTRY_IMAGE:latest"
#  rules:
#    - when: manual
